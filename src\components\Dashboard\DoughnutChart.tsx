import React, { useMemo } from 'react';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { useTheme } from '../../context/ThemeContext';

ChartJS.register(ArcElement, Tooltip, Legend);

interface DoughnutChartProps {
  percentage: number;
  title: string;
  color: string;
  size?: 'normal' | 'large';
}

export const DoughnutChart: React.FC<DoughnutChartProps> = React.memo(({ percentage, title, color, size = 'normal' }) => {
  const { mode } = useTheme();
  const dimensions = size === 'large' ? { width: '160px', height: '160px' } : { width: '120px', height: '120px' };
  const fontSize = size === 'large' ? '1.75rem' : '1.5rem';

  const data = useMemo(() => ({
    labels: ['Data', 'Remaining'],
    datasets: [
      {
        data: [percentage, 100 - percentage],
        backgroundColor: [color, mode === 'dark' ? '#374151' : '#f3f4f6'],
        borderWidth: 0,
        cutout: '80%',
        borderRadius: 5,
      },
    ],
  }), [percentage, color, mode]);

  const options = useMemo(() => ({
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    maintainAspectRatio: true,
    responsive: true,
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart'
    }
  }), []);

  return (
    <div className="flex flex-col items-center">
      <div style={{
        position: 'relative',
        ...dimensions,
        willChange: 'transform',
        contain: 'content'
      }}>
        <Doughnut
          data={data}
          options={{
            ...options,
            animation: {
              duration: 1000,
              easing: 'easeOutQuart' // Using valid easing type from Chart.js
            }
          }}
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <span style={{ fontSize, fontWeight: '600', color: mode === 'dark' ? '#f9fafb' : '#1f2937' }}>{percentage}%</span>
          </div>
        </div>
      </div>
      <div className="mt-3 text-center">
        <p className="text-sm font-medium text-text">{title}</p>
        <p className="text-xs text-text-secondary">Data Consent</p>
      </div>
    </div>
  );
});

DoughnutChart.displayName = 'DoughnutChart';