import { useState, useEffect, useCallback } from 'react';
import privacyDashboardService, {
  ConsentLevelData,
  AmberLevelData,
  RedLevelData,
  DataPrivacyAcknowledgementData
} from '../services/privacyDashboardService';

export type PrivacyDashboardLevel = 'green' | 'amber' | 'red' | 'privacy';

interface UsePrivacyDashboardState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UsePrivacyDashboardActions {
  refresh: () => Promise<void>;
  exportData: (format?: 'csv' | 'pdf' | 'excel') => Promise<boolean>;
  generateReport: () => Promise<boolean>;
  startReviewProcess?: () => Promise<boolean>;
  addressCriticalIssues?: () => Promise<boolean>;
  contactLegalTeam?: () => Promise<boolean>;
}

type UsePrivacyDashboardReturn<T> = UsePrivacyDashboardState<T> & UsePrivacyDashboardActions;

export function useGreenLevelData(): UsePrivacyDashboardReturn<ConsentLevelData> {
  const [state, setState] = useState<UsePrivacyDashboardState<ConsentLevelData>>({
    data: null,
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const data = await privacyDashboardService.getGreenLevelData();
      setState({
        data,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load green level data',
      }));
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const exportData = useCallback(async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    return await privacyDashboardService.exportData('green', format);
  }, []);

  const generateReport = useCallback(async () => {
    return await privacyDashboardService.generateReport('green');
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refresh,
    exportData,
    generateReport,
  };
}

export function useAmberLevelData(): UsePrivacyDashboardReturn<AmberLevelData> {
  const [state, setState] = useState<UsePrivacyDashboardState<AmberLevelData>>({
    data: null,
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const data = await privacyDashboardService.getAmberLevelData();
      setState({
        data,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load amber level data',
      }));
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const exportData = useCallback(async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    return await privacyDashboardService.exportData('amber', format);
  }, []);

  const generateReport = useCallback(async () => {
    return await privacyDashboardService.generateReport('amber');
  }, []);

  const startReviewProcess = useCallback(async () => {
    return await privacyDashboardService.startReviewProcess();
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refresh,
    exportData,
    generateReport,
    startReviewProcess,
  };
}

export function useRedLevelData(): UsePrivacyDashboardReturn<RedLevelData> {
  const [state, setState] = useState<UsePrivacyDashboardState<RedLevelData>>({
    data: null,
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const data = await privacyDashboardService.getRedLevelData();
      setState({
        data,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load red level data',
      }));
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const exportData = useCallback(async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    return await privacyDashboardService.exportData('red', format);
  }, []);

  const generateReport = useCallback(async () => {
    return await privacyDashboardService.generateReport('red');
  }, []);

  const addressCriticalIssues = useCallback(async () => {
    return await privacyDashboardService.addressCriticalIssues();
  }, []);

  const contactLegalTeam = useCallback(async () => {
    return await privacyDashboardService.contactLegalTeam();
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refresh,
    exportData,
    generateReport,
    addressCriticalIssues,
    contactLegalTeam,
  };
}

export function useDataPrivacyAcknowledgementData(): UsePrivacyDashboardReturn<DataPrivacyAcknowledgementData> {
  const [state, setState] = useState<UsePrivacyDashboardState<DataPrivacyAcknowledgementData>>({
    data: null,
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const data = await privacyDashboardService.getDataPrivacyAcknowledgementData();
      setState({
        data,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load data privacy acknowledgement data',
      }));
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const exportData = useCallback(async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    return await privacyDashboardService.exportData('privacy', format);
  }, []);

  const generateReport = useCallback(async () => {
    return await privacyDashboardService.generateReport('privacy');
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refresh,
    exportData,
    generateReport,
  };
}
