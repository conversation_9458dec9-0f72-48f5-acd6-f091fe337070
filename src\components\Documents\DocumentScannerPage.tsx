import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import ComplianceDocumentUpload from './ComplianceDocumentUpload';
import { Shield, Upload } from 'lucide-react';

const DocumentScannerPage: React.FC = () => {
  const { mode } = useTheme();

  return (
    <div className="flex-1 bg-background min-h-screen">
      <div className="p-6 md:p-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full flex items-center justify-center">
              <Upload className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-text">Document Scanner</h1>
              <p className="text-text-secondary text-lg">
                Upload and analyze documents for compliance requirements
              </p>
            </div>
          </div>
          
          {/* Page Description */}
          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-text mb-2">Compliance Document Analysis</h2>

              </div>
            </div>
          </div>
        </div>

        {/* Document Scanner Component */}
        <ComplianceDocumentUpload />
      </div>
    </div>
  );
};

export default DocumentScannerPage;
