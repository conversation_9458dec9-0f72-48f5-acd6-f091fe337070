import { useRef, useEffect, useCallback, useState, useMemo } from 'react';

/**
 * Custom hook for optimizing rendering performance
 * Provides utilities for debouncing, throttling, and detecting performance issues
 */
export const usePerformanceOptimizer = () => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(performance.now());
  const renderTimes = useRef<number[]>([]);
  const [isLowPerformance, setIsLowPerformance] = useState(false);

  // Track render performance
  useEffect(() => {
    renderCount.current += 1;
    const now = performance.now();
    const renderTime = now - lastRenderTime.current;
    renderTimes.current.push(renderTime);
    
    // Keep only the last 10 render times
    if (renderTimes.current.length > 10) {
      renderTimes.current.shift();
    }
    
    // Calculate average render time
    const avgRenderTime = renderTimes.current.reduce((sum, time) => sum + time, 0) / renderTimes.current.length;
    
    // If average render time is high, mark as low performance
    if (avgRenderTime > 16.67) { // 60fps threshold
      setIsLowPerformance(true);
    }
    
    lastRenderTime.current = now;
  });

  /**
   * Debounce function to limit the rate at which a function can fire
   */
  const debounce = useCallback(<T extends (...args: any[]) => any>(
    func: T,
    wait: number = 100
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), wait);
    };
  }, []);

  /**
   * Throttle function to limit the rate at which a function can fire
   */
  const throttle = useCallback(<T extends (...args: any[]) => any>(
    func: T,
    limit: number = 100
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle = false;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  }, []);

  /**
   * Memoize a value with deep comparison
   */
  const memoizeDeep = useCallback(<T>(value: T, deps: any[]): T => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => value, deps);
  }, []);

  /**
   * Get performance metrics for the component
   */
  const getPerformanceMetrics = useCallback(() => {
    return {
      renderCount: renderCount.current,
      averageRenderTime: renderTimes.current.reduce((sum, time) => sum + time, 0) / renderTimes.current.length,
      isLowPerformance,
      renderTimes: [...renderTimes.current],
    };
  }, [isLowPerformance]);

  return {
    debounce,
    throttle,
    memoizeDeep,
    getPerformanceMetrics,
    isLowPerformance,
  };
};

/**
 * Utility function to detect if the current device is a low-performance device
 */
export const isLowPerformanceDevice = (): boolean => {
  // Check for mobile devices
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  // Check for low memory (if available)
  const hasLowMemory = 'deviceMemory' in navigator && (navigator as any).deviceMemory < 4;
  
  // Check for low CPU cores (if available)
  const hasLowCPU = 'hardwareConcurrency' in navigator && navigator.hardwareConcurrency < 4;
  
  return isMobile || hasLowMemory || hasLowCPU;
};

/**
 * Utility function to optimize a large array for rendering
 * Reduces the number of items in the array based on the device performance
 */
export const optimizeArrayForRendering = <T>(
  array: T[],
  maxItems: number = 100
): T[] => {
  if (array.length <= maxItems) return array;
  
  const isLowPerf = isLowPerformanceDevice();
  const targetLength = isLowPerf ? Math.min(50, maxItems) : maxItems;
  
  if (array.length <= targetLength) return array;
  
  // Sample the array to reduce its size
  const step = Math.ceil(array.length / targetLength);
  const result: T[] = [];
  
  for (let i = 0; i < array.length; i += step) {
    result.push(array[i]);
  }
  
  return result;
};
