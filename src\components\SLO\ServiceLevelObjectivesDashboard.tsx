import React, { useState, useMemo, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Zap,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LineChart,
  DollarSign,
  Users,
  Server
} from 'lucide-react';

// Types for SLO Dashboard
interface SLOMetric {
  id: string;
  name: string;
  description: string;
  currentValue: number;
  targetValue: number;
  threshold: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  category: 'availability' | 'latency' | 'throughput' | 'error_rate';
  errorBudget: {
    remaining: number;
    consumed: number;
    total: number;
  };
  historicalData: Array<{
    timestamp: Date;
    value: number;
    target: number;
  }>;
}

interface SLODashboardData {
  overview: {
    totalSLOs: number;
    healthySLOs: number;
    warningSLOs: number;
    criticalSLOs: number;
    averageCompliance: number;
    errorBudgetUtilization: number;
  };
  metrics: SLOMetric[];
  performanceBudgets: {
    monthly: number;
    weekly: number;
    daily: number;
    remaining: number;
  };
  trends: {
    complianceTrend: Array<{ date: string; compliance: number }>;
    errorBudgetTrend: Array<{ date: string; budget: number }>;
    performanceTrend: Array<{ date: string; latency: number; throughput: number }>;
  };
}

interface ServiceLevelObjectivesDashboardProps {
  className?: string;
}

// Mock data generator
const generateMockSLOData = (): SLODashboardData => {
  const metrics: SLOMetric[] = [
    {
      id: 'slo-001',
      name: 'API Availability',
      description: 'Core API service availability target',
      currentValue: 99.95,
      targetValue: 99.9,
      threshold: 99.5,
      unit: '%',
      status: 'healthy',
      trend: 'stable',
      category: 'availability',
      errorBudget: {
        remaining: 85.2,
        consumed: 14.8,
        total: 100
      },
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        value: 99.8 + Math.random() * 0.15,
        target: 99.9
      }))
    },
    {
      id: 'slo-002',
      name: 'Response Time',
      description: 'API response time under 200ms',
      currentValue: 185,
      targetValue: 200,
      threshold: 250,
      unit: 'ms',
      status: 'healthy',
      trend: 'down',
      category: 'latency',
      errorBudget: {
        remaining: 92.1,
        consumed: 7.9,
        total: 100
      },
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        value: 180 + Math.random() * 40,
        target: 200
      }))
    },
    {
      id: 'slo-003',
      name: 'Error Rate',
      description: 'Error rate below 0.1%',
      currentValue: 0.08,
      targetValue: 0.1,
      threshold: 0.5,
      unit: '%',
      status: 'healthy',
      trend: 'stable',
      category: 'error_rate',
      errorBudget: {
        remaining: 78.5,
        consumed: 21.5,
        total: 100
      },
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        value: 0.05 + Math.random() * 0.1,
        target: 0.1
      }))
    },
    {
      id: 'slo-004',
      name: 'Throughput',
      description: 'Minimum 1000 requests per second',
      currentValue: 1250,
      targetValue: 1000,
      threshold: 800,
      unit: 'req/s',
      status: 'healthy',
      trend: 'up',
      category: 'throughput',
      errorBudget: {
        remaining: 95.3,
        consumed: 4.7,
        total: 100
      },
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        value: 1000 + Math.random() * 400,
        target: 1000
      }))
    }
  ];

  const healthySLOs = metrics.filter(m => m.status === 'healthy').length;
  const warningSLOs = metrics.filter(m => m.status === 'warning').length;
  const criticalSLOs = metrics.filter(m => m.status === 'critical').length;
  const averageCompliance = metrics.reduce((sum, m) => {
    const compliance = m.category === 'latency' || m.category === 'error_rate' 
      ? (m.currentValue <= m.targetValue ? 100 : (m.targetValue / m.currentValue) * 100)
      : (m.currentValue / m.targetValue) * 100;
    return sum + Math.min(compliance, 100);
  }, 0) / metrics.length;

  return {
    overview: {
      totalSLOs: metrics.length,
      healthySLOs,
      warningSLOs,
      criticalSLOs,
      averageCompliance,
      errorBudgetUtilization: metrics.reduce((sum, m) => sum + m.errorBudget.consumed, 0) / metrics.length
    },
    metrics,
    performanceBudgets: {
      monthly: 99.9,
      weekly: 99.95,
      daily: 99.99,
      remaining: 85.2
    },
    trends: {
      complianceTrend: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        compliance: 95 + Math.random() * 4
      })),
      errorBudgetTrend: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        budget: 80 + Math.random() * 15
      })),
      performanceTrend: Array.from({ length: 24 }, (_, i) => ({
        date: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        latency: 180 + Math.random() * 40,
        throughput: 1000 + Math.random() * 400
      }))
    }
  };
};

export const ServiceLevelObjectivesDashboard: React.FC<ServiceLevelObjectivesDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<SLODashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'availability' | 'latency' | 'throughput' | 'error_rate'>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<'24h' | '7d' | '30d'>('7d');
  const [animatedCounters, setAnimatedCounters] = useState<Record<string, number>>({});

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        const data = generateMockSLOData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load SLO data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Animated counters effect
  useEffect(() => {
    if (!dashboardData) return;

    const targets = {
      totalSLOs: dashboardData.overview.totalSLOs,
      healthySLOs: dashboardData.overview.healthySLOs,
      averageCompliance: dashboardData.overview.averageCompliance,
      errorBudgetUtilization: dashboardData.overview.errorBudgetUtilization
    };

    const duration = 1500; // 1.5 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      setAnimatedCounters({
        totalSLOs: Math.round(targets.totalSLOs * easeOutQuart),
        healthySLOs: Math.round(targets.healthySLOs * easeOutQuart),
        averageCompliance: targets.averageCompliance * easeOutQuart,
        errorBudgetUtilization: targets.errorBudgetUtilization * easeOutQuart
      });

      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedCounters(targets);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter metrics based on category
  const filteredMetrics = useMemo(() => {
    if (!dashboardData) return [];
    
    if (selectedCategory === 'all') {
      return dashboardData.metrics;
    }
    
    return dashboardData.metrics.filter(metric => metric.category === selectedCategory);
  }, [dashboardData, selectedCategory]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Generate SLO compliance chart data
  const sloComplianceChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: ['Healthy', 'Warning', 'Critical'],
      datasets: [{
        data: [
          dashboardData.overview.healthySLOs,
          dashboardData.overview.warningSLOs,
          dashboardData.overview.criticalSLOs
        ],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(248, 113, 113, 0.8)'
        ],
        borderColor: [
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
      }]
    };
  }, [dashboardData]);

  // Generate compliance trend chart data
  const complianceTrendChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.trends.complianceTrend.map(d =>
        new Date(d.date).toLocaleDateString('en-US', { weekday: 'short' })
      ),
      datasets: [{
        label: 'SLO Compliance',
        data: dashboardData.trends.complianceTrend.map(d => d.compliance),
        borderColor: 'rgb(79, 142, 247)',
        backgroundColor: 'rgba(79, 142, 247, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(79, 142, 247)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 7,
      }]
    };
  }, [dashboardData]);

  // Generate error budget trend chart data
  const errorBudgetChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.trends.errorBudgetTrend.map(d =>
        new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      ),
      datasets: [{
        label: 'Error Budget Remaining',
        data: dashboardData.trends.errorBudgetTrend.map(d => d.budget),
        backgroundColor: dashboardData.trends.errorBudgetTrend.map(d =>
          d.budget > 80 ? 'rgba(52, 211, 153, 0.8)' :
          d.budget > 50 ? 'rgba(251, 191, 36, 0.8)' :
          'rgba(248, 113, 113, 0.8)'
        ),
        borderColor: dashboardData.trends.errorBudgetTrend.map(d =>
          d.budget > 80 ? 'rgb(52, 211, 153)' :
          d.budget > 50 ? 'rgb(251, 191, 36)' :
          'rgb(248, 113, 113)'
        ),
        borderWidth: 2,
      }]
    };
  }, [dashboardData]);

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateMockSLOData();
      setDashboardData(data);
      setError(null);
    } catch (err) {
      setError('Failed to refresh SLO data');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load SLO Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Target className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Service-Level Objectives</h1>
              <p className="text-text-secondary">
                Monitor and track SLO compliance, performance budgets, and error budget utilization
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total SLOs</p>
                  <p className="text-2xl font-bold text-text">{animatedCounters.totalSLOs || 0}</p>
                </div>
                <Target className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Healthy SLOs</p>
                  <p className="text-2xl font-bold text-green-500">{animatedCounters.healthySLOs || 0}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Avg Compliance</p>
                  <p className="text-2xl font-bold text-text">{(animatedCounters.averageCompliance || 0).toFixed(1)}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Error Budget Used</p>
                  <p className="text-2xl font-bold text-amber-500">{(animatedCounters.errorBudgetUtilization || 0).toFixed(1)}%</p>
                </div>
                <Zap className="w-8 h-8 text-amber-500" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* SLO Status Distribution */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <PieChart className="w-5 h-5" />
            SLO Status Distribution
          </h3>
          {sloComplianceChartData && (
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={sloComplianceChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Compliance Trend */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            7-Day Compliance Trend
          </h3>
          {complianceTrendChartData && (
            <div className="h-64">
              <Line
                data={complianceTrendChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 90,
                      max: 100
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-text-secondary" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Categories</option>
              <option value="availability">Availability</option>
              <option value="latency">Latency</option>
              <option value="throughput">Throughput</option>
              <option value="error_rate">Error Rate</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-text-secondary" />
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>
        </div>

        {/* SLO Metrics List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text">SLO Metrics</h3>

          {filteredMetrics.length === 0 ? (
            <div className="text-center py-8">
              <Target className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <p className="text-text-secondary">No SLO metrics found for the selected category.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredMetrics.map((metric) => (
                <div key={metric.id} className="bg-card rounded-lg p-6 border border-border hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(metric.status)}
                        <h4 className="text-lg font-semibold text-text">{metric.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(metric.status)}`}>
                          {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                        </span>
                        {getTrendIcon(metric.trend)}
                      </div>

                      <p className="text-sm text-text-secondary mb-4">{metric.description}</p>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <p className="text-xs text-text-secondary">Current Value</p>
                          <p className="text-lg font-bold text-text">
                            {metric.currentValue.toFixed(metric.category === 'error_rate' ? 2 : 0)}{metric.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Target</p>
                          <p className="text-lg font-bold text-text">
                            {metric.targetValue.toFixed(metric.category === 'error_rate' ? 2 : 0)}{metric.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Error Budget</p>
                          <p className="text-lg font-bold text-text">{metric.errorBudget.remaining.toFixed(1)}%</p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Category</p>
                          <p className="text-sm font-semibold text-text capitalize">{metric.category.replace('_', ' ')}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <button className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors">
                        <Settings className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                      <span>Compliance Progress</span>
                      <span>
                        {metric.category === 'latency' || metric.category === 'error_rate'
                          ? `${Math.min((metric.targetValue / metric.currentValue) * 100, 100).toFixed(1)}%`
                          : `${Math.min((metric.currentValue / metric.targetValue) * 100, 100).toFixed(1)}%`
                        }
                      </span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          metric.status === 'healthy' ? 'bg-green-500' :
                          metric.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'
                        }`}
                        style={{
                          width: `${Math.min(
                            metric.category === 'latency' || metric.category === 'error_rate'
                              ? (metric.targetValue / metric.currentValue) * 100
                              : (metric.currentValue / metric.targetValue) * 100,
                            100
                          )}%`
                        }}
                      />
                    </div>
                  </div>

                  {/* Error Budget Bar */}
                  <div>
                    <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                      <span>Error Budget Remaining</span>
                      <span>{metric.errorBudget.remaining.toFixed(1)}% of {metric.errorBudget.total}%</span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          metric.errorBudget.remaining > 80 ? 'bg-green-500' :
                          metric.errorBudget.remaining > 50 ? 'bg-amber-500' : 'bg-red-500'
                        }`}
                        style={{
                          width: `${metric.errorBudget.remaining}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
