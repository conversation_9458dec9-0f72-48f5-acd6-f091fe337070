import axios from 'axios';

// Define types for DPDP rules
export interface DPDPCondition {
  field: string;
  operator: string;
  value: string;
}

export interface DPDPRule {
  rule_id: string;
  description: string;
  trigger: string;
  conditions: DPDPCondition[];
  action: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
}

// Define types for GDPR rules
export interface GDPRCondition {
  condition: string;
}

export interface GDPRAction {
  action: string;
}

export interface GDPRRule {
  id: string;
  name: string;
  description: string;
  conditions: string[];
  actions: string[];
}

// Combined rule type for internal use
export type ComplianceRule = DPDPRule | GDPRRule;

// Rule evaluation result
export interface RuleEvaluationResult {
  rule_id: string;
  compliant: boolean;
  message: string;
  severity?: string;
  timestamp: string;
  details?: any;
}

// Rule trigger event
export interface ComplianceEvent {
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
  source?: string;
}

// DPDP Rules
const dpdpRules: DPDPRule[] = [
  {
    rule_id: "DPDP-001",
    description: "Obtain valid consent before processing personal data.",
    trigger: "data_processing_initiated",
    conditions: [
      { field: "consent_status", operator: "!=", value: "granted" }
    ],
    action: "block_processing",
    severity: "high",
    message: "Processing not allowed without valid consent under DPDP."
  },
  {
    rule_id: "DPDP-002",
    description: "Ensure the ability to revoke consent at any time.",
    trigger: "user_consent_revoke_request",
    conditions: [],
    action: "revoke_consent_and_log",
    severity: "medium",
    message: "Consent revoked. Data processing must stop immediately."
  },
  {
    rule_id: "DPDP-003",
    description: "Retain personal data only as long as necessary for the purpose.",
    trigger: "data_retention_check",
    conditions: [
      { field: "retention_period_exceeded", operator: "==", value: "true" }
    ],
    action: "initiate_data_deletion",
    severity: "medium",
    message: "Retention period exceeded. Data must be deleted per DPDP requirements."
  },
  {
    rule_id: "DPDP-004",
    description: "Notify user and Data Protection Board of breach within defined time.",
    trigger: "data_breach_detected",
    conditions: [],
    action: "notify_user_and_regulator",
    severity: "critical",
    message: "Data breach must be reported within timelines per DPDP."
  },
  {
    rule_id: "DPDP-005",
    description: "Obtain verifiable parental consent before processing children's data.",
    trigger: "child_data_processing_requested",
    conditions: [
      { field: "age", operator: "<", value: "18" },
      { field: "parental_consent", operator: "!=", value: "verified" }
    ],
    action: "block_processing",
    severity: "high",
    message: "Child data processing blocked without parental consent."
  },
  {
    rule_id: "DPDP-006",
    description: "Provide users with the right to access their personal data.",
    trigger: "data_access_request",
    conditions: [],
    action: "initiate_data_portability_export",
    severity: "medium",
    message: "User data access enabled as per DPDP rights."
  },
  {
    rule_id: "DPDP-007",
    description: "Minimize data collected to what is strictly necessary.",
    trigger: "data_collection_event",
    conditions: [
      { field: "data_scope", operator: ">", value: "purpose_requirement" }
    ],
    action: "log_and_alert_for_review",
    severity: "medium",
    message: "Data minimization principle violated."
  },
  {
    rule_id: "DPDP-008",
    description: "Ensure purpose limitation is enforced.",
    trigger: "data_usage_attempt",
    conditions: [
      { field: "usage_purpose", operator: "!=", value: "original_consent_purpose" }
    ],
    action: "block_usage",
    severity: "high",
    message: "Usage outside original purpose is not permitted."
  },
  {
    rule_id: "DPDP-009",
    description: "Ensure accountability of data fiduciaries via periodic audits.",
    trigger: "monthly_audit_schedule",
    conditions: [],
    action: "run_policy_audit_and_log_findings",
    severity: "low",
    message: "Automated audit executed for DPDP compliance tracking."
  }
];

// GDPR Rules
const gdprRules: GDPRRule[] = [
  {
    id: "GDPR_01",
    name: "Consent Management",
    description: "Ensure valid user consent is collected before processing personal data.",
    conditions: [
      "User has explicitly agreed to data processing",
      "Consent is freely given, specific, informed, and unambiguous"
    ],
    actions: [
      "Record timestamp and context of consent",
      "Link consent record to user profile",
      "Provide mechanism to withdraw consent at any time"
    ]
  },
  {
    id: "GDPR_02",
    name: "Data Minimization",
    description: "Collect and process only data that is necessary for the intended purpose.",
    conditions: [
      "Data fields mapped to processing purposes",
      "Purpose is legally justified and documented"
    ],
    actions: [
      "Block unnecessary fields from being collected",
      "Trigger alerts for unapproved data collection"
    ]
  },
  {
    id: "GDPR_03",
    name: "Right to Access",
    description: "Allow data subjects to access their personal data upon request.",
    conditions: [
      "Verified data subject request received"
    ],
    actions: [
      "Generate data report within 30 days",
      "Notify requester and provide data securely"
    ]
  },
  {
    id: "GDPR_04",
    name: "Right to Erasure",
    description: "Allow individuals to request deletion of their personal data.",
    conditions: [
      "Verified deletion request received",
      "No overriding legal obligation to retain data"
    ],
    actions: [
      "Delete data from all systems and backups",
      "Notify third parties of erasure request"
    ]
  },
  {
    id: "GDPR_05",
    name: "Data Breach Notification",
    description: "Notify authorities and affected users within 72 hours of a personal data breach.",
    conditions: [
      "Breach involves personal data",
      "Breach poses a risk to data subject rights and freedoms"
    ],
    actions: [
      "Notify supervisory authority within 72 hours",
      "Inform affected users with breach details"
    ]
  }
];

// Service functions
export const complianceRulesService = {
  // Get all DPDP rules
  getDPDPRules: () => {
    return dpdpRules;
  },

  // Get all GDPR rules
  getGDPRRules: () => {
    return gdprRules;
  },

  // Get a specific rule by ID
  getRuleById: (ruleId: string) => {
    return [...dpdpRules, ...gdprRules].find(rule =>
      'rule_id' in rule ? rule.rule_id === ruleId : rule.id === ruleId
    );
  },

  // Evaluate a compliance event against applicable rules
  evaluateComplianceEvent: (event: ComplianceEvent): RuleEvaluationResult[] => {
    const results: RuleEvaluationResult[] = [];

    // Evaluate DPDP rules
    // Find rules that match this event type
    const applicableDPDPRules = dpdpRules.filter(rule => rule.trigger === event.type);

    // Evaluate each DPDP rule
    for (const rule of applicableDPDPRules) {
      let compliant = true;

      // Check all conditions
      for (const condition of rule.conditions) {
        const fieldValue = event.data[condition.field];

        switch (condition.operator) {
          case '==':
            if (fieldValue != condition.value) compliant = false;
            break;
          case '!=':
            if (fieldValue == condition.value) compliant = false;
            break;
          case '>':
            if (fieldValue <= condition.value) compliant = false;
            break;
          case '<':
            if (fieldValue >= condition.value) compliant = false;
            break;
          // Add more operators as needed
        }
      }

      results.push({
        rule_id: rule.rule_id,
        compliant,
        message: compliant ? `Compliant with ${rule.rule_id}` : rule.message,
        severity: rule.severity,
        timestamp: new Date().toISOString(),
        details: { event, rule }
      });
    }

    // Evaluate GDPR rules
    // For GDPR rules, we'll use a different approach since they have a different structure
    if (event.type === 'gdpr_consent_check') {
      const consentRule = gdprRules.find(rule => rule.id === 'GDPR_01');
      if (consentRule) {
        const hasConsent = event.data.explicit_consent === true;
        const isInformed = event.data.informed_consent === true;

        results.push({
          rule_id: consentRule.id,
          compliant: hasConsent && isInformed,
          message: hasConsent && isInformed
            ? `Compliant with ${consentRule.id}: Valid consent obtained`
            : `Non-compliant with ${consentRule.id}: Valid consent not obtained`,
          severity: 'high',
          timestamp: new Date().toISOString(),
          details: { event, rule: consentRule }
        });
      }
    }

    if (event.type === 'gdpr_data_minimization_check') {
      const minimizationRule = gdprRules.find(rule => rule.id === 'GDPR_02');
      if (minimizationRule) {
        const dataMinimized = event.data.data_minimized === true;
        const purposeJustified = event.data.purpose_justified === true;

        results.push({
          rule_id: minimizationRule.id,
          compliant: dataMinimized && purposeJustified,
          message: dataMinimized && purposeJustified
            ? `Compliant with ${minimizationRule.id}: Data minimization principles followed`
            : `Non-compliant with ${minimizationRule.id}: Data minimization principles not followed`,
          severity: 'medium',
          timestamp: new Date().toISOString(),
          details: { event, rule: minimizationRule }
        });
      }
    }

    if (event.type === 'gdpr_data_access_request') {
      const accessRule = gdprRules.find(rule => rule.id === 'GDPR_03');
      if (accessRule) {
        const requestVerified = event.data.request_verified === true;
        const responseTimely = event.data.response_days <= 30;

        results.push({
          rule_id: accessRule.id,
          compliant: requestVerified && responseTimely,
          message: requestVerified && responseTimely
            ? `Compliant with ${accessRule.id}: Data access request handled properly`
            : `Non-compliant with ${accessRule.id}: Data access request not handled properly`,
          severity: 'medium',
          timestamp: new Date().toISOString(),
          details: { event, rule: accessRule }
        });
      }
    }

    if (event.type === 'gdpr_data_breach') {
      const breachRule = gdprRules.find(rule => rule.id === 'GDPR_05');
      if (breachRule) {
        const notifiedWithin72Hours = event.data.notification_hours <= 72;
        const usersInformed = event.data.users_informed === true;

        results.push({
          rule_id: breachRule.id,
          compliant: notifiedWithin72Hours && usersInformed,
          message: notifiedWithin72Hours && usersInformed
            ? `Compliant with ${breachRule.id}: Data breach properly reported`
            : `Non-compliant with ${breachRule.id}: Data breach not properly reported`,
          severity: 'critical',
          timestamp: new Date().toISOString(),
          details: { event, rule: breachRule }
        });
      }
    }

    return results;
  },

  // Log compliance evaluation results
  logComplianceResult: async (result: RuleEvaluationResult) => {
    try {
      console.log('Compliance result logged:', result);
      // Use our mock API
      await axios.post('/compliance/logs', result);
      return true;
    } catch (error) {
      console.error('Error logging compliance result:', error);
      return false;
    }
  }
};

export default complianceRulesService;
