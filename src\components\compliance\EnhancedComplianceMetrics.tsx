import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Calendar,
  Activity,
  Zap,
  Target,
  Eye,
  ChevronDown,
  ChevronRight,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  MousePointer,
  Layers,
  Clock,
  Users,
  Building
} from 'lucide-react';

// Enhanced Compliance Types
interface ComplianceMetric {
  id: string;
  name: string;
  category: 'privacy' | 'security' | 'operational' | 'regulatory';
  currentValue: number;
  targetValue: number;
  trend: 'up' | 'down' | 'stable';
  status: 'compliant' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    target: number;
    violations: number;
  }>;
  drillDownData?: {
    departments: Array<{ name: string; value: number; status: string }>;
    policies: Array<{ name: string; value: number; violations: number }>;
    timeBreakdown: Array<{ period: string; value: number; incidents: number }>;
  };
}

interface ComplianceOverview {
  totalMetrics: number;
  compliantMetrics: number;
  warningMetrics: number;
  criticalMetrics: number;
  overallScore: number;
  trendDirection: 'up' | 'down' | 'stable';
  lastAssessment: Date;
  nextAssessment: Date;
}

interface EnhancedComplianceData {
  overview: ComplianceOverview;
  metrics: ComplianceMetric[];
  categoryBreakdown: {
    privacy: { score: number; count: number; trend: string };
    security: { score: number; count: number; trend: string };
    operational: { score: number; count: number; trend: string };
    regulatory: { score: number; count: number; trend: string };
  };
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    updateFrequency: number;
    pendingUpdates: number;
  };
}

interface EnhancedComplianceMetricsProps {
  className?: string;
}

// Mock data generator
const generateEnhancedComplianceData = (): EnhancedComplianceData => {
  const metrics: ComplianceMetric[] = [
    {
      id: 'gdpr-001',
      name: 'GDPR Data Processing Compliance',
      category: 'privacy',
      currentValue: 94.2,
      targetValue: 95.0,
      trend: 'up',
      status: 'warning',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 90 + Math.random() * 8,
        target: 95,
        violations: Math.floor(Math.random() * 5)
      })),
      drillDownData: {
        departments: [
          { name: 'Marketing', value: 92.1, status: 'warning' },
          { name: 'Sales', value: 96.8, status: 'compliant' },
          { name: 'HR', value: 93.5, status: 'warning' },
          { name: 'IT', value: 97.2, status: 'compliant' }
        ],
        policies: [
          { name: 'Data Retention Policy', value: 95.2, violations: 2 },
          { name: 'Consent Management', value: 91.8, violations: 5 },
          { name: 'Data Transfer Policy', value: 96.1, violations: 1 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 90 + Math.random() * 8,
          incidents: Math.floor(Math.random() * 3)
        }))
      }
    },
    {
      id: 'sec-001',
      name: 'Security Framework Compliance',
      category: 'security',
      currentValue: 97.8,
      targetValue: 98.0,
      trend: 'stable',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 8 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 95 + Math.random() * 4,
        target: 98,
        violations: Math.floor(Math.random() * 3)
      })),
      drillDownData: {
        departments: [
          { name: 'Engineering', value: 98.5, status: 'compliant' },
          { name: 'Operations', value: 97.2, status: 'compliant' },
          { name: 'Support', value: 96.8, status: 'warning' }
        ],
        policies: [
          { name: 'Access Control Policy', value: 98.9, violations: 0 },
          { name: 'Encryption Standards', value: 97.1, violations: 1 },
          { name: 'Incident Response', value: 97.5, violations: 1 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 95 + Math.random() * 4,
          incidents: Math.floor(Math.random() * 2)
        }))
      }
    },
    {
      id: 'ops-001',
      name: 'Operational Compliance',
      category: 'operational',
      currentValue: 89.3,
      targetValue: 92.0,
      trend: 'down',
      status: 'critical',
      lastUpdated: new Date(Date.now() - 3 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 85 + Math.random() * 10,
        target: 92,
        violations: Math.floor(Math.random() * 8)
      })),
      drillDownData: {
        departments: [
          { name: 'Finance', value: 91.2, status: 'warning' },
          { name: 'Legal', value: 94.1, status: 'compliant' },
          { name: 'Procurement', value: 85.7, status: 'critical' },
          { name: 'Facilities', value: 87.9, status: 'critical' }
        ],
        policies: [
          { name: 'Document Management', value: 88.5, violations: 8 },
          { name: 'Approval Workflows', value: 90.1, violations: 6 },
          { name: 'Audit Procedures', value: 89.3, violations: 7 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 85 + Math.random() * 10,
          incidents: Math.floor(Math.random() * 4)
        }))
      }
    },
    {
      id: 'reg-001',
      name: 'Regulatory Compliance',
      category: 'regulatory',
      currentValue: 96.1,
      targetValue: 95.0,
      trend: 'up',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 12 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 92 + Math.random() * 6,
        target: 95,
        violations: Math.floor(Math.random() * 4)
      })),
      drillDownData: {
        departments: [
          { name: 'Compliance', value: 98.2, status: 'compliant' },
          { name: 'Risk Management', value: 96.8, status: 'compliant' },
          { name: 'Audit', value: 94.3, status: 'warning' }
        ],
        policies: [
          { name: 'SOX Compliance', value: 97.1, violations: 1 },
          { name: 'Financial Reporting', value: 95.8, violations: 2 },
          { name: 'Risk Assessment', value: 95.4, violations: 3 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 92 + Math.random() * 6,
          incidents: Math.floor(Math.random() * 2)
        }))
      }
    }
  ];

  const compliantMetrics = metrics.filter(m => m.status === 'compliant').length;
  const warningMetrics = metrics.filter(m => m.status === 'warning').length;
  const criticalMetrics = metrics.filter(m => m.status === 'critical').length;
  const overallScore = metrics.reduce((sum, m) => sum + m.currentValue, 0) / metrics.length;

  return {
    overview: {
      totalMetrics: metrics.length,
      compliantMetrics,
      warningMetrics,
      criticalMetrics,
      overallScore,
      trendDirection: 'up',
      lastAssessment: new Date(Date.now() - 2 * 60 * 60 * 1000),
      nextAssessment: new Date(Date.now() + 22 * 60 * 60 * 1000)
    },
    metrics,
    categoryBreakdown: {
      privacy: { 
        score: metrics.filter(m => m.category === 'privacy').reduce((sum, m) => sum + m.currentValue, 0) / metrics.filter(m => m.category === 'privacy').length,
        count: metrics.filter(m => m.category === 'privacy').length,
        trend: 'up'
      },
      security: { 
        score: metrics.filter(m => m.category === 'security').reduce((sum, m) => sum + m.currentValue, 0) / metrics.filter(m => m.category === 'security').length,
        count: metrics.filter(m => m.category === 'security').length,
        trend: 'stable'
      },
      operational: { 
        score: metrics.filter(m => m.category === 'operational').reduce((sum, m) => sum + m.currentValue, 0) / metrics.filter(m => m.category === 'operational').length,
        count: metrics.filter(m => m.category === 'operational').length,
        trend: 'down'
      },
      regulatory: { 
        score: metrics.filter(m => m.category === 'regulatory').reduce((sum, m) => sum + m.currentValue, 0) / metrics.filter(m => m.category === 'regulatory').length,
        count: metrics.filter(m => m.category === 'regulatory').length,
        trend: 'up'
      }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30,
      pendingUpdates: Math.floor(Math.random() * 5)
    }
  };
};

export const EnhancedComplianceMetrics: React.FC<EnhancedComplianceMetricsProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<EnhancedComplianceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'privacy' | 'security' | 'operational' | 'regulatory'>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('30d');
  const [expandedMetric, setExpandedMetric] = useState<string | null>(null);
  const [drillDownView, setDrillDownView] = useState<'departments' | 'policies' | 'timeline'>('departments');
  const [animatedCounters, setAnimatedCounters] = useState<Record<string, number>>({});

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1200));
        const data = generateEnhancedComplianceData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load compliance metrics data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          metrics: prevData.metrics.map(metric => ({
            ...metric,
            currentValue: Math.max(80, Math.min(100, metric.currentValue + (Math.random() - 0.5) * 2)),
            lastUpdated: new Date()
          })),
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            pendingUpdates: Math.floor(Math.random() * 5)
          }
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Animated counters effect
  useEffect(() => {
    if (!dashboardData) return;

    const targets = {
      totalMetrics: dashboardData.overview.totalMetrics,
      compliantMetrics: dashboardData.overview.compliantMetrics,
      overallScore: dashboardData.overview.overallScore,
      warningMetrics: dashboardData.overview.warningMetrics,
      criticalMetrics: dashboardData.overview.criticalMetrics
    };

    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      setAnimatedCounters({
        totalMetrics: Math.round(targets.totalMetrics * easeOutQuart),
        compliantMetrics: Math.round(targets.compliantMetrics * easeOutQuart),
        overallScore: targets.overallScore * easeOutQuart,
        warningMetrics: Math.round(targets.warningMetrics * easeOutQuart),
        criticalMetrics: Math.round(targets.criticalMetrics * easeOutQuart)
      });

      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedCounters(targets);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter metrics based on category
  const filteredMetrics = useMemo(() => {
    if (!dashboardData) return [];

    if (selectedCategory === 'all') {
      return dashboardData.metrics;
    }

    return dashboardData.metrics.filter(metric => metric.category === selectedCategory);
  }, [dashboardData, selectedCategory]);

  // Generate overview chart data
  const overviewChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: ['Compliant', 'Warning', 'Critical'],
      datasets: [{
        data: [
          dashboardData.overview.compliantMetrics,
          dashboardData.overview.warningMetrics,
          dashboardData.overview.criticalMetrics
        ],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(248, 113, 113, 0.8)'
        ],
        borderColor: [
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
        hoverOffset: 8
      }]
    };
  }, [dashboardData]);

  // Generate category breakdown chart
  const categoryBreakdownData = useMemo(() => {
    if (!dashboardData) return null;

    const categories = Object.entries(dashboardData.categoryBreakdown);

    return {
      labels: categories.map(([key]) => key.charAt(0).toUpperCase() + key.slice(1)),
      datasets: [{
        label: 'Compliance Score',
        data: categories.map(([, value]) => value.score),
        backgroundColor: [
          'rgba(139, 92, 246, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ],
        borderColor: [
          'rgb(139, 92, 246)',
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(245, 158, 11)'
        ],
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  // Generate trend chart data
  const trendChartData = useMemo(() => {
    if (!dashboardData || filteredMetrics.length === 0) return null;

    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toISOString().split('T')[0];
    });

    return {
      labels: last30Days.map(date => new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
      datasets: filteredMetrics.map((metric, index) => ({
        label: metric.name,
        data: metric.historicalData.map(d => d.value),
        borderColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)'
        ][index % 5],
        backgroundColor: [
          'rgba(79, 142, 247, 0.1)',
          'rgba(52, 211, 153, 0.1)',
          'rgba(251, 191, 36, 0.1)',
          'rgba(248, 113, 113, 0.1)',
          'rgba(139, 92, 246, 0.1)'
        ][index % 5],
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)'
        ][index % 5],
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
      }))
    };
  }, [dashboardData, filteredMetrics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateEnhancedComplianceData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMetricClick = useCallback((metricId: string) => {
    setExpandedMetric(expandedMetric === metricId ? null : metricId);
  }, [expandedMetric]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Compliance Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <BarChart3 className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Enhanced Compliance Metrics</h1>
              <p className="text-text-secondary">
                Interactive compliance monitoring with drill-down analytics and real-time updates
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • Last sync: {dashboardData.realTimeUpdates.lastSync.toLocaleTimeString()}
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Overview Statistics with Animated Counters */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total Metrics</p>
                  <p className="text-2xl font-bold text-text">{animatedCounters.totalMetrics || 0}</p>
                </div>
                <Target className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Compliant</p>
                  <p className="text-2xl font-bold text-green-500">{animatedCounters.compliantMetrics || 0}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Warning</p>
                  <p className="text-2xl font-bold text-amber-500">{animatedCounters.warningMetrics || 0}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-amber-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Critical</p>
                  <p className="text-2xl font-bold text-red-500">{animatedCounters.criticalMetrics || 0}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Overall Score</p>
                  <p className="text-2xl font-bold text-text">{(animatedCounters.overallScore || 0).toFixed(1)}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-500" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Interactive Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Overview Chart */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <PieChart className="w-5 h-5" />
            Compliance Status Overview
          </h3>
          {overviewChartData && (
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={overviewChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => {
                          const label = context.label || '';
                          const value = context.parsed;
                          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                          const percentage = ((value / total) * 100).toFixed(1);
                          return `${label}: ${value} (${percentage}%)`;
                        }
                      }
                    }
                  },
                  onHover: (event, elements) => {
                    if (event.native?.target) {
                      (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    }
                  },
                  onClick: (event, elements) => {
                    if (elements.length > 0) {
                      const index = elements[0].index;
                      const categories = ['compliant', 'warning', 'critical'];
                      console.log(`Clicked on ${categories[index]} metrics`);
                      // Could implement drill-down here
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Category Breakdown Chart */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Category Performance
          </h3>
          {categoryBreakdownData && (
            <div className="h-64">
              <Bar
                data={categoryBreakdownData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => `Score: ${context.parsed.y.toFixed(1)}%`
                      }
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 0,
                      max: 100
                    }
                  },
                  onHover: (event, elements) => {
                    if (event.native?.target) {
                      (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    }
                  },
                  onClick: (event, elements) => {
                    if (elements.length > 0) {
                      const index = elements[0].index;
                      const categories = ['privacy', 'security', 'operational', 'regulatory'];
                      setSelectedCategory(categories[index] as any);
                      console.log(`Filtering by ${categories[index]} category`);
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Trend Analysis Chart */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            Compliance Trends ({selectedTimeRange})
          </h3>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-text-secondary" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="all">All Categories</option>
                <option value="privacy">Privacy</option>
                <option value="security">Security</option>
                <option value="operational">Operational</option>
                <option value="regulatory">Regulatory</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-text-secondary" />
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </div>
          </div>
        </div>

        {trendChartData && (
          <div className="h-80">
            <Line
              data={trendChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                  mode: 'index' as const,
                  intersect: false,
                },
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      color: chartTheme.textColor,
                      padding: 20,
                      usePointStyle: true,
                    }
                  },
                  tooltip: {
                    backgroundColor: chartTheme.tooltipBg,
                    titleColor: chartTheme.textColor,
                    bodyColor: chartTheme.textColor,
                    borderColor: chartTheme.borderColor,
                    borderWidth: 1,
                    callbacks: {
                      label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                    }
                  }
                },
                scales: {
                  x: {
                    grid: {
                      color: chartTheme.gridColor,
                    },
                    ticks: {
                      color: chartTheme.textSecondary,
                    }
                  },
                  y: {
                    grid: {
                      color: chartTheme.gridColor,
                    },
                    ticks: {
                      color: chartTheme.textSecondary,
                      callback: (value) => `${value}%`
                    },
                    min: 80,
                    max: 100
                  }
                },
                onHover: (event, elements) => {
                  if (event.native?.target) {
                    (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                  }
                },
                onClick: (event, elements) => {
                  if (elements.length > 0) {
                    const datasetIndex = elements[0].datasetIndex;
                    const metric = filteredMetrics[datasetIndex];
                    if (metric) {
                      handleMetricClick(metric.id);
                    }
                  }
                }
              }}
            />
          </div>
        )}
      </div>

      {/* Detailed Metrics with Drill-down */}
      <div className="bg-surface rounded-lg p-6">
        <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
          <Layers className="w-5 h-5" />
          Detailed Compliance Metrics
        </h3>

        <div className="space-y-4">
          {filteredMetrics.map((metric) => (
            <div key={metric.id} className="bg-card rounded-lg border border-border overflow-hidden">
              {/* Metric Header */}
              <div
                className="p-4 cursor-pointer hover:bg-border/30 transition-colors"
                onClick={() => handleMetricClick(metric.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(metric.status)}
                    <div>
                      <h4 className="text-lg font-semibold text-text">{metric.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                        <span className="capitalize">{metric.category}</span>
                        <span>Updated: {metric.lastUpdated.toLocaleTimeString()}</span>
                        {getTrendIcon(metric.trend)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-2xl font-bold text-text">{metric.currentValue.toFixed(1)}%</p>
                      <p className="text-sm text-text-secondary">Target: {metric.targetValue}%</p>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(metric.status)}`}>
                        {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                      </span>

                      {expandedMetric === metric.id ? (
                        <ChevronDown className="w-5 h-5 text-text-secondary" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-text-secondary" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-text-secondary">Progress to Target</span>
                    <span className="text-text font-medium">
                      {Math.min((metric.currentValue / metric.targetValue) * 100, 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        metric.status === 'compliant' ? 'bg-green-500' :
                        metric.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'
                      }`}
                      style={{
                        width: `${Math.min((metric.currentValue / metric.targetValue) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Drill-down Content */}
              {expandedMetric === metric.id && metric.drillDownData && (
                <div className="border-t border-border p-4 bg-background/50">
                  <div className="flex items-center gap-4 mb-4">
                    <button
                      onClick={() => setDrillDownView('departments')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'departments'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Building className="w-4 h-4 inline mr-2" />
                      Departments
                    </button>
                    <button
                      onClick={() => setDrillDownView('policies')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'policies'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Target className="w-4 h-4 inline mr-2" />
                      Policies
                    </button>
                    <button
                      onClick={() => setDrillDownView('timeline')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'timeline'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Clock className="w-4 h-4 inline mr-2" />
                      Timeline
                    </button>
                  </div>

                  {/* Drill-down Data */}
                  <div className="space-y-3">
                    {drillDownView === 'departments' && metric.drillDownData.departments.map((dept, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                        <div className="flex items-center gap-3">
                          <Building className="w-4 h-4 text-text-secondary" />
                          <span className="text-text font-medium">{dept.name}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(dept.status)}`}>
                            {dept.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-text">{dept.value.toFixed(1)}%</p>
                        </div>
                      </div>
                    ))}

                    {drillDownView === 'policies' && metric.drillDownData.policies.map((policy, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                        <div className="flex items-center gap-3">
                          <Target className="w-4 h-4 text-text-secondary" />
                          <span className="text-text font-medium">{policy.name}</span>
                          {policy.violations > 0 && (
                            <span className="px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400 rounded-full text-xs font-medium">
                              {policy.violations} violations
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-text">{policy.value.toFixed(1)}%</p>
                        </div>
                      </div>
                    ))}

                    {drillDownView === 'timeline' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                        {metric.drillDownData.timeBreakdown.slice(0, 12).map((period, index) => (
                          <div key={index} className="p-3 bg-card rounded-lg border border-border">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-text-secondary">{period.period}</span>
                              <Clock className="w-3 h-3 text-text-secondary" />
                            </div>
                            <p className="text-lg font-bold text-text">{period.value.toFixed(1)}%</p>
                            {period.incidents > 0 && (
                              <p className="text-xs text-red-500">{period.incidents} incidents</p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
