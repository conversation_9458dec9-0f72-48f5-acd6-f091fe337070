import React, { useState } from 'react';
import ComplianceRulesList from './ComplianceRulesList';
import ComplianceHistory from './ComplianceHistory';
import ComplianceEventTester from './ComplianceEventTester';
import ComplianceRulesTest from './ComplianceRulesTest';
import { useComplianceRules } from '../../context/ComplianceRulesContext';

const ComplianceRulesDashboard: React.FC = () => {
  const { dpdpEnabled, gdprEnabled, toggleDPDP, toggleGDPR } = useComplianceRules();
  const [activeTab, setActiveTab] = useState<'rules' | 'tester' | 'history' | 'test'>('rules');

  return (
    <div className="bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-text">Compliance Rules Management</h1>
            <p className="mt-1 text-sm text-text-secondary">
              Manage and test DPDP and GDPR compliance rules
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex space-x-4">
            <div className="flex items-center">
              <span className="mr-2 text-sm font-medium text-text">GDPR</span>
              <button
                type="button"
                onClick={() => toggleGDPR(!gdprEnabled)}
                className={`${
                  gdprEnabled ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800`}
              >
                <span
                  className={`${
                    gdprEnabled ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>

            <div className="flex items-center">
              <span className="mr-2 text-sm font-medium text-text">DPDP</span>
              <button
                type="button"
                onClick={() => toggleDPDP(!dpdpEnabled)}
                className={`${
                  dpdpEnabled ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800`}
              >
                <span
                  className={`${
                    dpdpEnabled ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-border mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('rules')}
              className={`${
                activeTab === 'rules'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-text-secondary hover:text-text hover:border-border'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Rules
            </button>
            <button
              onClick={() => setActiveTab('tester')}
              className={`${
                activeTab === 'tester'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-text-secondary hover:text-text hover:border-border'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Rule Tester
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`${
                activeTab === 'history'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-text-secondary hover:text-text hover:border-border'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Evaluation History
            </button>
            <button
              onClick={() => setActiveTab('test')}
              className={`${
                activeTab === 'test'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-text-secondary hover:text-text hover:border-border'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Test Suite
            </button>
          </nav>
        </div>

        {/* Content */}
        <div>
          {activeTab === 'rules' && <ComplianceRulesList />}
          {activeTab === 'tester' && <ComplianceEventTester />}
          {activeTab === 'history' && <ComplianceHistory />}
          {activeTab === 'test' && <ComplianceRulesTest />}
        </div>
      </div>
    </div>
  );
};

export default ComplianceRulesDashboard;
