import React from 'react';

interface PolicyData {
  name: string;
  compliant: number;
  nonCompliant: number;
}

export const ComplianceByPolicy: React.FC = () => {
  const policies: PolicyData[] = [
    { name: 'ISO 27001', compliant: 75, nonCompliant: 25 },
    { name: 'GDPR', compliant: 80, nonCompliant: 20 },
    { name: 'HIPAA', compliant: 85, nonCompliant: 15 },
    { name: '(Prod)-Corp', compliant: 90, nonCompliant: 10 },
  ];

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm">
      <h2 className="text-xl font-semibold text-text mb-6">Compliance By Policy</h2>
      <div className="space-y-4">
        {policies.map((policy) => (
          <div key={policy.name} className="space-y-2">
            <div className="flex justify-between text-sm text-text">
              <span>{policy.name}</span>
              <span>{policy.compliant}%</span>
            </div>
            <div className="h-2 bg-surface rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full transition-all duration-300"
                style={{ width: `${policy.compliant}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
