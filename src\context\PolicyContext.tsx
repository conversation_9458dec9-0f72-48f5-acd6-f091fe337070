import React, { createContext, useContext, useState, ReactNode } from 'react';

interface SubPolicy {
  name: string;
  status: string;
  lastUpdated?: string;
  assignee?: string;
}

interface PolicyWithSubPolicies {
  name: string;
  subPolicies?: SubPolicy[];
  status?: string;
  lastUpdated?: string;
  priority?: 'high' | 'medium' | 'low';
  impact?: string;
}

interface PolicyContextType {
  policyStatuses: Record<string, string>;
  subPolicyStatuses: Record<string, Record<string, string>>;
  updatePolicyStatus: (policyName: string, status: string, isSubPolicy?: boolean, parentPolicy?: string) => void;
  criticalPolicies: PolicyWithSubPolicies[];
  moderatePolicies: PolicyWithSubPolicies[];
  marginalPolicies: PolicyWithSubPolicies[];
}

export const PolicyContext = createContext<PolicyContextType | undefined>(undefined);

export const PolicyProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // ... (implementation as provided in previous response)
};

export const usePolicyContext = () => {
  const context = useContext(PolicyContext);
  if (!context) {
    throw new Error('usePolicyContext must be used within a PolicyProvider');
  }
  return context;
};