import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { X } from 'lucide-react';

interface SubPolicyDetailsProps {
  subPolicy: {
    name: string;
    status: string;
    documentRef?: string;
    version?: string;
    owner?: string;
    completionDate?: string;  // Add this line
    toBeUpdated?: boolean;    // Add this line
  };
  onClose: () => void;
  onStatusChange: (status: string, toBeUpdated?: boolean) => void;
}

// Add getStatusColor function
const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-orange-50 text-orange-700';
    case 'in_progress':
      return 'bg-blue-50 text-blue-700';
    case 'completed':
      return 'bg-green-50 text-green-700';
    case 'to_be_updated':
      return 'bg-purple-50 text-purple-700';
    default:
      return 'bg-gray-50 text-gray-700';
  }
};

export const SubPolicyDetails: React.FC<SubPolicyDetailsProps> = ({ subPolicy, onClose, onStatusChange }) => {
  const [updateReason, setUpdateReason] = useState('');

  return (
    <Dialog open={true} onClose={onClose} className="relative z-[60]">
      <div className="fixed inset-0 bg-white/[0.15] backdrop-blur-sm" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-white rounded-xl shadow-xl p-6 max-w-2xl w-full">
          <div className="flex justify-between items-center mb-6 pb-4 border-b">
            <div>
              <Dialog.Title className="text-xl font-semibold text-gray-900">
                {subPolicy.name}
              </Dialog.Title>
              <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full mt-2 ${getStatusColor(subPolicy.status)}`}>
                <span className="text-sm font-medium capitalize">
                  {subPolicy.status.replace('_', ' ')}
                </span>
              </div>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-full"
            >
              <X size={20} />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Document Details</h3>
                  <p className="text-sm text-gray-600">Reference: DOC-{Math.random().toString(36).substr(2, 9).toUpperCase()}</p>
                  <p className="text-sm text-gray-600">Version: 1.0.0</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Owner: John Smith</p>
                  <p className="text-xs text-gray-500">Last updated: {new Date().toLocaleDateString()}</p>
                </div>
              </div>
              <p className="text-gray-600">
                Detailed information about {subPolicy.name} implementation and requirements.
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Requirements</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Requirement 1</li>
                <li>Requirement 2</li>
                <li>Requirement 3</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Implementation Timeline</h3>
              <p className="text-gray-600">
                Expected completion: Q4 2024
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              // Update the Status Update section:
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">Status Update</h3>
                <div className="space-y-4">
                  <div className="flex gap-3">
                    <button
                      onClick={() => onStatusChange('pending')}
                      className={`px-4 py-2 rounded-lg border transition-colors ${
                        subPolicy.status === 'pending' 
                          ? 'bg-orange-50 border-orange-200 text-orange-700' 
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      Pending
                    </button>
                    <button
                      onClick={() => onStatusChange('in_progress')}
                      className={`px-4 py-2 rounded-lg border transition-colors ${
                        subPolicy.status === 'in_progress' 
                          ? 'bg-blue-50 border-blue-200 text-blue-700' 
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      In Progress
                    </button>
                    <button
                      onClick={() => onStatusChange('completed')}
                      className={`px-4 py-2 rounded-lg border transition-colors ${
                        subPolicy.status === 'completed' 
                          ? 'bg-green-50 border-green-200 text-green-700' 
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      Completed
                    </button>
                  </div>
                
                  <div className="flex items-center justify-between pt-3 border-t">
                    <div className="space-y-2 w-full">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="toBeUpdated"
                          checked={subPolicy.toBeUpdated}
                          onChange={(e) => {
                            onStatusChange(subPolicy.status, e.target.checked);
                            if (!e.target.checked) {
                              setUpdateReason('');
                            }
                          }}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="toBeUpdated" className="text-sm text-gray-700">
                          To Be Updated
                        </label>
                      </div>
                      
                      {subPolicy.toBeUpdated && (
                        <div className="mt-2">
                          <textarea
                            value={updateReason}
                            onChange={(e) => setUpdateReason(e.target.value)}
                            placeholder="Enter reason for update..."
                            className="w-full px-3 py-2 text-sm border rounded-lg focus:ring-blue-500 focus:border-blue-500"
                            rows={2}
                          />
                        </div>
                      )}
                    </div>
                    
                    {subPolicy.status === 'completed' && (
                      <div className="text-sm text-gray-600 ml-4 text-right">
                        Completed on: {subPolicy.completionDate || new Date().toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};