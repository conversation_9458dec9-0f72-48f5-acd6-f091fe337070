import React from 'react';
import { Line } from 'react-chartjs-2';
import { useTheme } from '../context/ThemeContext';
import { getChartColors, createChartOptions } from '../utils/chartOptimizations';
// Chart.js components are registered globally

const ConsentTrends: React.FC = () => {
  const { mode } = useTheme();
  const isDark = mode === 'dark';
  const colors = getChartColors(isDark);

  const data = {
    labels: ['Nov-23', 'Dec-23', 'Jan-24', 'Feb-24', 'Mar-24', 'Apr-24', 'May-24', 'Jun-24'],
    datasets: [
      {
        label: 'Non-Compliant',
        data: [0.2, 1.8, 1.5, 1.0, 0.2, 1.8, 1.7, 2.8],
        borderColor: colors.nonCompliant,
        backgroundColor: 'transparent',
        tension: 0.2,
        borderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: colors.nonCompliant,
        pointBorderColor: isDark ? '#334155' : '#FFFFFF',
        pointBorderWidth: 2,
      },
      {
        label: 'Pending Review',
        data: [1.8, 1.2, 0.9, 1.5, 2.0, 1.5, 0.5, 1.6],
        borderColor: colors.pending,
        backgroundColor: 'transparent',
        tension: 0.2,
        borderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: colors.pending,
        pointBorderColor: isDark ? '#334155' : '#FFFFFF',
        pointBorderWidth: 2,
      },
      {
        label: 'Compliant',
        data: [0.2, 0.5, 0.5, 0.5, 2.4, 1.6, 1.1, 0.9],
        borderColor: colors.compliant,
        backgroundColor: 'transparent',
        tension: 0.2,
        borderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: colors.compliant,
        pointBorderColor: isDark ? '#334155' : '#FFFFFF',
        pointBorderWidth: 2,
      },
    ],
  };

  const options = createChartOptions(isDark, {
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    scales: {
      y: {
        min: 0,
        max: 4,
        ticks: {
          stepSize: 1,
        },
        title: {
          display: true,
          text: 'Consent Rate',
          font: {
            size: 12,
            family: 'Inter, system-ui, sans-serif',
            weight: '500' as const,
          },
          color: isDark ? '#CBD5E1' : '#4B5563',
          padding: { top: 0, bottom: 10 },
        },
      },
      x: {
        grid: {
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Consent Management Trends',
        align: 'center' as const,
        font: {
          size: 16,
          family: 'Inter, system-ui, sans-serif',
          weight: '600' as const,
        },
        padding: {
          bottom: 30,
        },
        color: isDark ? '#F8FAFC' : '#111827',
      },
    },
  });

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border chart-container">
      <h3 className="text-lg font-semibold mb-4 text-text">Compliance Trends</h3>
      <div style={{ height: '400px' }}>
        <Line data={data} options={options} />
      </div>
    </div>
  );
};

export default ConsentTrends;