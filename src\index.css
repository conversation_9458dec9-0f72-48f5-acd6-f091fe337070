/* Import Leaflet CSS - Must be first */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default theme variables (will be overridden by ThemeContext) */

  /* Primary colors */
  --color-primary: #A6D933;
  --color-primary-light: #B5E350;
  --color-primary-dark: #8AB929;
  --color-primary-hover: #B5E350;
  --color-primary-active: #8AB929;
  --color-primary-focus: #A6D933;

  /* Secondary colors */
  --color-secondary: #0D9488;
  --color-secondary-light: #14B8A6;
  --color-secondary-dark: #0F766E;
  --color-secondary-hover: #14B8A6;
  --color-secondary-active: #0F766E;

  /* Accent colors */
  --color-accent-blue: #A6D933;
  --color-accent-teal: #0D9488;
  --color-accent-purple: #8B5CF6;

  /* Background and surface colors */
  --color-background: #F9FAFB;
  --color-surface: #FFFFFF;
  --color-card-bg: #FFFFFF;

  /* Text colors */
  --color-text: #111827;
  --color-text-secondary: #4B5563;
  --color-text-tertiary: #6B7280;

  /* Border colors */
  --color-border: #E5E7EB;
  --color-border-light: #F3F4F6;
  --color-border-focus: #4F46E5;

  /* Semantic colors */
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-error-dark: #B91C1C;
  --color-error-bg: #FEF2F2;

  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-success-dark: #047857;
  --color-success-bg: #ECFDF5;

  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-warning-dark: #B45309;
  --color-warning-bg: #FFFBEB;

  --color-info: #A6D933;
  --color-info-light: #EBF7C6;
  --color-info-dark: #8AB929;
  --color-info-bg: #F5FBDF;

  /* Shadow values */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-focus: 0 0 0 3px rgba(166, 217, 51, 0.45);

  /* Chart colors */
  --chart-bg: #FFFFFF;
  --chart-grid: #E5E7EB;
  --chart-text: #111827;
  --chart-text-secondary: #4B5563;

  /* Dashboard colors */
  --dashboard-green: #10B981;
  --dashboard-amber: #F59E0B;
  --dashboard-red: #EF4444;
  --dashboard-purple: #8B5CF6;
  --dashboard-button-bg: #F3F4F6;
  --dashboard-button-text: #111827;

  /* Transition speeds */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;

  /* Typography */
  --font-family-base: 'Inter', system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-lg: 1.125rem;
  /* 18px */
  --font-size-xl: 1.25rem;
  /* 20px */
  --font-size-2xl: 1.5rem;
  /* 24px */
  --font-size-3xl: 1.875rem;
  /* 30px */
  --font-size-4xl: 2rem;
  /* 32px */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

@layer base {

  /* Base styles */
  body {
    background-color: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-family-base);
    line-height: var(--line-height-base);
    transition: background-color var(--transition-speed), color var(--transition-speed);
  }
}

/* Dark mode - Navy Blue and Blue-Gray Professional Theme */
.dark {
  /* Primary colors - Strategic Blue Highlighting */
  --color-primary: #60A5FA;
  --color-primary-light: #93C5FD;
  --color-primary-dark: #3B82F6;
  --color-primary-hover: #93C5FD;
  --color-primary-active: #3B82F6;
  --color-primary-focus: #60A5FA;

  /* Secondary colors - Navy Blue for Navigation */
  --color-secondary: #1A1F2E;
  --color-secondary-light: #252B42;
  --color-secondary-dark: #0F1419;
  --color-secondary-hover: #252B42;
  --color-secondary-active: #0F1419;

  /* Accent colors - Strategic Highlighting System */
  --color-accent-blue: #60A5FA;
  --color-accent-teal: #34D399;
  --color-accent-purple: #1A1F2E;

  /* Background and surface colors - Blue-Gray and Navy */
  --color-background: #344054;
  --color-surface: #1A1F2E;
  --color-card-bg: #252B42;

  /* Text colors - Optimized contrast for navy blue and blue-gray backgrounds */
  --color-text: #FFFFFF;
  --color-text-secondary: #E2E8F0;
  --color-text-tertiary: #CBD5E1;

  /* Border colors - Navy Blue and Blue-Gray Tones */
  --color-border: #374151;
  --color-border-light: #4B5563;
  --color-border-focus: #60A5FA;

  /* Semantic colors - Navy blue and blue-gray harmonized */
  --color-error: #F87171;
  --color-error-light: #7F1D1D;
  --color-error-dark: #EF4444;
  --color-error-bg: #451A1A;

  --color-success: #34D399;
  --color-success-light: #065F46;
  --color-success-dark: #10B981;
  --color-success-bg: #064E3B;

  --color-warning: #FBBF24;
  --color-warning-light: #92400E;
  --color-warning-dark: #F59E0B;
  --color-warning-bg: #78350F;

  --color-info: #60A5FA;
  --color-info-light: #1E3A8A;
  --color-info-dark: #3B82F6;
  --color-info-bg: #1A1F2E;

  /* Shadow values - Navy Blue and Blue-Gray tinted */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4), 0 1px 1px 0 rgba(26, 31, 46, 0.08);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.5), 0 1px 2px 0 rgba(26, 31, 46, 0.12);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.6), 0 2px 4px -1px rgba(26, 31, 46, 0.15);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(26, 31, 46, 0.12);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(26, 31, 46, 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgba(26, 31, 46, 0.15);
  --shadow-focus: 0 0 0 3px rgba(96, 165, 250, 0.7);

  /* Chart colors - Navy Blue and Blue-Gray Theme */
  --chart-bg: #252B42;
  --chart-grid: #374151;
  --chart-text: #FFFFFF;
  --chart-text-secondary: #E2E8F0;

  /* Dashboard colors - Strategic Highlighting System */
  --dashboard-green: #34D399;
  --dashboard-amber: #F59E0B;
  --dashboard-red: #F87171;
  --dashboard-purple: #1A1F2E;
  --dashboard-button-bg: #1A1F2E;
  --dashboard-button-text: #FFFFFF;
}

@layer components {

  /* Premium Scrollbar Styles for Airport Operations */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.5) rgba(241, 245, 249, 0.3);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.3);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 4px;
    border: 1px solid rgba(241, 245, 249, 0.2);
    transition: all 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
    border-color: rgba(148, 163, 184, 0.3);
  }

  .scrollbar-thin::-webkit-scrollbar-corner {
    background: rgba(241, 245, 249, 0.3);
  }

  /* Dark mode scrollbar - Purple Theme */
  .dark .scrollbar-thin {
    scrollbar-color: rgba(139, 92, 246, 0.6) rgba(45, 27, 61, 0.4);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(45, 27, 61, 0.4);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.6);
    border-color: rgba(45, 27, 61, 0.3);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(167, 139, 250, 0.8);
    border-color: rgba(139, 92, 246, 0.4);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-corner {
    background: rgba(45, 27, 61, 0.4);
  }

  /* Smooth scrolling for all elements */
  * {
    scroll-behavior: smooth;
  }

  /* Leaflet Map Animations */
  @keyframes pulse {

    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }

    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  @keyframes glow {
    0% {
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(59, 130, 246, 0.3);
    }

    100% {
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 8px rgba(59, 130, 246, 0.6);
    }
  }

  .enhanced-marker {
    z-index: 1000 !important;
  }

  @keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
      transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
      transform: translate3d(0, -8px, 0);
    }

    70% {
      transform: translate3d(0, -4px, 0);
    }

    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  .custom-flight-marker {
    background: transparent !important;
    border: none !important;
  }

  .aircraft-marker {
    background: transparent !important;
    border: none !important;
  }

  /* Leaflet Map Container Fixes */
  .leaflet-container {
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
    background: #f8fafc !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .dark .leaflet-container {
    background: #1a1625 !important;
  }

  /* Simple Leaflet styling for NOC - Purple Theme */
  .leaflet-container {
    background: #1a1625 !important;
  }

  /* Ensure map tiles load properly */
  .leaflet-tile-pane {
    z-index: 1 !important;
  }

  .leaflet-map-pane {
    z-index: 1 !important;
  }

  .leaflet-overlay-pane {
    z-index: 2 !important;
  }

  .leaflet-marker-pane {
    z-index: 3 !important;
  }

  .leaflet-popup-pane {
    z-index: 4 !important;
  }

  .leaflet-control-container {
    z-index: 5 !important;
  }

  /* Leaflet popup styling */
  .leaflet-popup-content-wrapper {
    border-radius: 8px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    background: white !important;
    color: #1f2937 !important;
  }

  .dark .leaflet-popup-content-wrapper {
    background: #312e4a !important;
    color: #F1F5F9 !important;
  }

  .leaflet-popup-tip {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    background: white !important;
  }

  .dark .leaflet-popup-tip {
    background: #312e4a !important;
  }

  /* Flight path styling */
  .leaflet-interactive {
    cursor: pointer !important;
  }

  /* Map debugging - remove in production */
  .map-debug {
    border: 2px solid #ef4444 !important;
    background: rgba(239, 68, 68, 0.1) !important;
  }

  /* Ensure map container is visible */
  .flight-map-container {
    min-height: 320px !important;
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    border-radius: 12px !important;
  }

  /* Fix for Leaflet attribution */
  .leaflet-control-attribution {
    background: rgba(255, 255, 255, 0.8) !important;
    font-size: 10px !important;
  }

  .dark .leaflet-control-attribution {
    background: rgba(49, 46, 74, 0.8) !important;
    color: #E2E8F0 !important;
  }

  /* Card styling */
  .card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    transition: background-color var(--transition-normal),
      border-color var(--transition-normal),
      box-shadow var(--transition-normal),
      transform var(--transition-normal);
  }

  .card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: rgba(139, 92, 246, 0.3);
  }

  .dark .card {
    box-shadow: var(--shadow-sm);
  }

  .dark .card:hover {
    box-shadow: var(--shadow);
    border-color: rgba(167, 139, 250, 0.4);
  }

  /* Text truncation */
  .truncate-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .truncate-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .truncate-3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* Professional Navy Blue Theme Utilities */
  .navy-gradient-bg {
    background: linear-gradient(135deg, #1A1F2E 0%, #252B42 50%, #344054 100%);
  }

  .navy-gradient-text {
    background: linear-gradient(135deg, #60A5FA 0%, #93C5FD 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .navy-glow {
    box-shadow: 0 0 20px rgba(26, 31, 46, 0.3);
  }

  .dark .navy-glow {
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.4);
  }

  .navy-border-glow {
    border: 1px solid rgba(26, 31, 46, 0.3);
    box-shadow: 0 0 0 1px rgba(26, 31, 46, 0.1);
  }

  .dark .navy-border-glow {
    border: 1px solid rgba(96, 165, 250, 0.4);
    box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.2);
  }

  /* Enhanced button styles for navy blue theme */
  .btn-navy {
    background: linear-gradient(135deg, #1A1F2E 0%, #252B42 100%);
    color: #FFFFFF;
    border: 1px solid rgba(26, 31, 46, 0.3);
    transition: all 0.2s ease;
  }

  .btn-navy:hover {
    background: linear-gradient(135deg, #252B42 0%, #344054 100%);
    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
    transform: translateY(-1px);
  }

  .btn-navy:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
  }

  /* Professional card variants */
  .card-navy {
    background: linear-gradient(135deg, rgba(26, 31, 46, 0.05) 0%, rgba(37, 43, 66, 0.05) 100%);
    border: 1px solid rgba(26, 31, 46, 0.2);
  }

  .dark .card-navy {
    background: linear-gradient(135deg, rgba(26, 31, 46, 0.1) 0%, rgba(37, 43, 66, 0.1) 100%);
    border: 1px solid rgba(96, 165, 250, 0.3);
  }

  .card-navy:hover {
    border-color: rgba(26, 31, 46, 0.4);
    box-shadow: 0 8px 25px rgba(26, 31, 46, 0.15);
  }

  .dark .card-navy:hover {
    border-color: rgba(96, 165, 250, 0.5);
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  }
}

/* Button styling */
button,
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  transition: all var(--transition-normal);
  cursor: pointer;
}

button:focus,
.btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

button:disabled,
.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(166, 217, 51, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:active {
  background-color: var(--color-primary-active);
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-border-light);
  border-color: rgba(166, 217, 51, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px -1px rgba(166, 217, 51, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.04);
}

.btn-secondary:active {
  background-color: var(--color-border);
  transform: translateY(0);
}

.btn-text {
  background-color: transparent;
  color: var(--color-primary);
  border: none;
  padding: 0.25rem 0.5rem;
}

.btn-text:hover {
  background-color: rgba(166, 217, 51, 0.05);
}

.btn-text:active {
  background-color: rgba(166, 217, 51, 0.1);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-base);
}

/* Form elements */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-control {
  display: block;
  width: 100%;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: all var(--transition-normal);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: var(--shadow-focus);
  background-color: rgba(166, 217, 51, 0.02);
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--color-border-light);
  opacity: 0.65;
}

.form-control::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

.form-text {
  margin-top: 0.25rem;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.form-error {
  margin-top: 0.25rem;
  font-size: var(--font-size-xs);
  color: var(--color-error);
}

/* Table styling */
.table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.5rem;
  overflow: hidden;
}

th,
td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--color-border);
  text-align: left;
}

th {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  background-color: var(--color-border-light);
  position: sticky;
  top: 0;
  z-index: 10;
}

td {
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover td {
  background-color: rgba(166, 217, 51, 0.05);
}

.dark tr:hover td {
  background-color: rgba(166, 217, 51, 0.08);
}

/* Numeric data alignment */
.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

/* Dashboard specific styles */
.dashboard-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: rgba(166, 217, 51, 0.3);
}

.dashboard-card:hover::before {
  opacity: 0.6;
}

.dark .dashboard-card {
  background: linear-gradient(135deg, #2D3561 0%, rgba(45, 53, 97, 0.9) 50%, rgba(58, 65, 118, 0.8) 100%);
  border: 1px solid #3A4176;
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.dark .dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: rgba(79, 142, 247, 0.5);
  background: linear-gradient(135deg, #2D3561 0%, rgba(45, 53, 97, 0.95) 50%, rgba(58, 65, 118, 0.9) 100%);
  transform: translateY(-2px);
}

.chart-container {
  background-color: var(--chart-bg);
  border-radius: 0.5rem;
  padding: 1rem;
  position: relative;
  z-index: 10;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all var(--transition-normal);
}

.dark .chart-container {
  background: linear-gradient(135deg, #2D3561 0%, rgba(45, 53, 97, 0.9) 50%, rgba(30, 33, 57, 0.8) 100%);
  border: 1px solid #3A4176;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.chart-container:hover {
  box-shadow: var(--shadow);
}

.dark .chart-container:hover {
  box-shadow: var(--shadow-lg);
  border-color: rgba(79, 142, 247, 0.3);
  background: linear-gradient(135deg, #2D3561 0%, rgba(45, 53, 97, 0.95) 50%, rgba(30, 33, 57, 0.9) 100%);
}

/* Temporarily disabled to test if this is causing black lines
.dark .chart-container canvas {
  filter: brightness(0.95);
}
*/

/* Force chart colors to be applied correctly */
.chart-color-test canvas {
  /* Ensure no CSS is overriding chart colors */
  filter: none !important; /* Remove any filters that might affect colors */
}

.chart-color-test .chartjs-render-monitor {
  /* Ensure chart renders with correct colors */
  filter: none !important; /* Remove any filters that might affect colors */
}

/* Global canvas color fix */
canvas {
  /* Ensure canvas elements don't have default black colors */
}

/* Chart.js specific fixes */
.chartjs-render-monitor {
  filter: none !important; /* Remove any filters */
}

.dashboard-content {
  position: relative;
  z-index: 5;
}

.dashboard-element {
  position: relative;
  z-index: 10;
}

/* Link styling */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast), box-shadow var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
  text-decoration-color: rgba(166, 217, 51, 0.4);
  text-underline-offset: 2px;
}

a:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

a:active {
  color: var(--color-primary-active);
}

/* Accessibility focus styles */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: var(--shadow-focus);
  transition: outline-color 0.2s ease, box-shadow 0.2s ease;
}

/* Theme toggle button */
.theme-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.theme-toggle-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  color: var(--color-text);
  transition: all var(--transition-normal);
}

.theme-toggle-button:hover {
  background-color: rgba(166, 217, 51, 0.1);
  transform: scale(1.05);
}

.theme-toggle-button:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

.dark .theme-toggle-button:hover {
  background-color: rgba(166, 217, 51, 0.15);
}

.theme-icon {
  transition: transform var(--transition-normal);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg,
      var(--color-border-light) 25%,
      var(--color-border) 37%,
      var(--color-border-light) 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 0.25rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}

.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-right-color: var(--color-primary-light);
  box-shadow: 0 0 10px rgba(166, 217, 51, 0.1);
  animation: spinner 0.6s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

/* Text utilities */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.theme-toggle-button:hover .theme-icon {
  transform: rotate(30deg);
}

/* Enhanced dark mode styling for professional appearance */
.dark {
  /* Enhanced body styling for sophisticated navy blue and blue-gray dark mode */
}

.dark body {
  background: linear-gradient(135deg, #344054 0%, #1A1F2E 25%, #252B42 50%, #374151 100%);
  color: var(--color-text);
  min-height: 100vh;
}

/* Professional button styling for navy blue and blue-gray dark mode */
.dark button {
  background: linear-gradient(135deg, #1A1F2E 0%, rgba(26, 31, 46, 0.9) 50%, rgba(37, 43, 66, 0.8) 100%);
  border: 1px solid #374151;
  color: #FFFFFF;
  transition: all var(--transition-normal);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.dark button:hover {
  background: linear-gradient(135deg, rgba(37, 43, 66, 0.95) 0%, rgba(52, 64, 84, 0.9) 50%, rgba(96, 165, 250, 0.8) 100%);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: var(--shadow-md), 0 0 20px rgba(96, 165, 250, 0.2);
  transform: translateY(-1px);
}

/* Enhanced form controls for dark mode */
.dark input,
.dark select,
.dark textarea {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(30, 41, 59, 0.8) 100%);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  transition: all var(--transition-normal);
}

.dark input:focus,
.dark select:focus,
.dark textarea:focus {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-focus);
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(30, 41, 59, 0.9) 100%);
}

/* Enhanced navigation styling for dark mode */
.dark nav,
.dark .sidebar {
  background: linear-gradient(180deg, var(--color-surface) 0%, rgba(30, 41, 59, 0.95) 100%);
  border-right: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
}

/* Professional table styling for dark mode */
.dark table {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
}

.dark th {
  background: linear-gradient(135deg, var(--color-card-bg) 0%, rgba(51, 65, 85, 0.8) 100%);
  border-bottom: 2px solid var(--color-border);
  color: var(--color-text);
}

.dark td {
  border-bottom: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

.dark tr:hover td {
  background: linear-gradient(90deg, transparent, rgba(166, 217, 51, 0.1), transparent);
}

/* Enhanced sidebar styling for sophisticated navy blue and blue-gray dark mode */
.dark .bg-surface {
  background: linear-gradient(180deg, #1A1F2E 0%, rgba(26, 31, 46, 0.98) 25%, rgba(37, 43, 66, 0.95) 100%);
  border-right: 1px solid #374151;
  box-shadow: 4px 0 25px rgba(0, 0, 0, 0.4), 2px 0 15px rgba(15, 20, 25, 0.3);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Enhanced navigation item styling */
.dark nav a,
.dark nav button {
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.dark nav a::before,
.dark nav button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 142, 247, 0.15), rgba(99, 102, 241, 0.1), transparent);
  transition: left var(--transition-normal);
  z-index: -1;
}

.dark nav a:hover::before,
.dark nav button:hover::before {
  left: 0;
}

/* Enhanced profile section styling */
.dark .bg-gradient-to-br {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  box-shadow: 0 4px 8px rgba(166, 217, 51, 0.2);
}

/* Enhanced theme toggle positioning */
.dark .theme-toggle-button {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

/* Professional depth and visual hierarchy effects for dark mode */
.dark {
  /* Enhanced layering system */
  --layer-base: 0;
  --layer-surface: 10;
  --layer-elevated: 20;
  --layer-overlay: 30;
  --layer-modal: 40;

  /* Enhanced depth shadows with navy blue and blue-gray tints */
  --depth-1: 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(15, 20, 25, 0.2);
  --depth-2: 0 4px 6px rgba(0, 0, 0, 0.6), 0 2px 4px rgba(15, 20, 25, 0.25);
  --depth-3: 0 10px 15px rgba(0, 0, 0, 0.7), 0 4px 6px rgba(15, 20, 25, 0.3);
  --depth-4: 0 20px 25px rgba(0, 0, 0, 0.8), 0 10px 10px rgba(15, 20, 25, 0.25);

  /* Sophisticated navy blue and blue-gray glow effects */
  --glow-primary: 0 0 20px rgba(96, 165, 250, 0.2);
  --glow-secondary: 0 0 20px rgba(26, 31, 46, 0.15);
  --glow-accent: 0 0 15px rgba(52, 64, 84, 0.1);
}

/* Enhanced card depth system */
.dark .bg-card,
.dark .dashboard-card,
.dark .chart-container {
  position: relative;
  z-index: var(--layer-surface);
  box-shadow: var(--depth-2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.dark .bg-card:hover,
.dark .dashboard-card:hover,
.dark .chart-container:hover {
  z-index: var(--layer-elevated);
  box-shadow: var(--depth-3);
  transform: translateY(-2px);
}

/* Enhanced button depth */
.dark button:not(.theme-toggle-button) {
  position: relative;
  z-index: var(--layer-surface);
  box-shadow: var(--depth-1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.dark button:not(.theme-toggle-button):hover {
  z-index: var(--layer-elevated);
  box-shadow: var(--depth-2);
}

.dark button:not(.theme-toggle-button):active {
  box-shadow: inset var(--depth-1);
  transform: translateY(1px);
}

/* Enhanced form control depth */
.dark input:focus,
.dark select:focus,
.dark textarea:focus {
  z-index: var(--layer-elevated);
  box-shadow: var(--depth-2), var(--glow-primary);
}

/* Enhanced navigation depth */
.dark .bg-surface {
  z-index: var(--layer-elevated);
  box-shadow: var(--depth-3);
}

/* Subtle animation enhancements */
.dark * {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states with depth */
.dark *:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: var(--depth-2), var(--glow-primary);
}

/* Professional loading states */
.dark .skeleton {
  background: linear-gradient(90deg,
      rgba(51, 65, 85, 0.8) 25%,
      rgba(71, 85, 105, 0.6) 37%,
      rgba(51, 65, 85, 0.8) 63%);
  background-size: 400% 100%;
  animation: skeleton-loading-dark 1.4s ease infinite;
}

@keyframes skeleton-loading-dark {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* Enhanced spinner for dark mode */
.dark .spinner {
  border: 2px solid rgba(71, 85, 105, 0.3);
  border-top-color: var(--color-primary);
  border-right-color: var(--color-primary-light);
  box-shadow: var(--glow-primary);
}

/* Professional text hierarchy for dark mode */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: var(--color-text);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark .text-text-secondary {
  color: var(--color-text-secondary);
  opacity: 0.9;
}

.dark .text-text-tertiary {
  color: var(--color-text-tertiary);
  opacity: 0.8;
}

/* Enhanced metric displays */
.dark .text-3xl,
.dark .text-2xl {
  font-weight: 700;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Professional status indicators with navy blue and blue-gray theme */
.dark .text-green-500 {
  color: #34D399; /* Bright teal-green from reference */
  text-shadow: 0 0 12px rgba(52, 211, 153, 0.4);
}

.dark .text-red-500 {
  color: #F87171; /* Softer red for blue backgrounds */
  text-shadow: 0 0 12px rgba(248, 113, 113, 0.4);
}

.dark .text-amber-500,
.dark .text-orange-500,
.dark .text-yellow-500 {
  color: #FBBF24; /* High contrast amber */
  text-shadow: 0 0 12px rgba(251, 191, 36, 0.4);
}

/* Enhanced progress bars and indicators with navy blue and blue-gray theme */
.dark .bg-green-500 {
  background: linear-gradient(90deg, #34D399 0%, rgba(52, 211, 153, 0.9) 50%, rgba(20, 184, 166, 0.8) 100%);
  box-shadow: 0 0 15px rgba(52, 211, 153, 0.4);
}

.dark .bg-red-500 {
  background: linear-gradient(90deg, #F87171 0%, rgba(248, 113, 113, 0.9) 50%, rgba(239, 68, 68, 0.8) 100%);
  box-shadow: 0 0 15px rgba(248, 113, 113, 0.4);
}

.dark .bg-amber-500,
.dark .bg-orange-500,
.dark .bg-yellow-500 {
  background: linear-gradient(90deg, #FBBF24 0%, rgba(251, 191, 36, 0.9) 50%, rgba(245, 158, 11, 0.8) 100%);
  box-shadow: 0 0 15px rgba(251, 191, 36, 0.4);
}

/* Enhanced border colors for dark mode */
.dark .border-green-500 {
  border-color: var(--dashboard-green);
  box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.2);
}

.dark .border-red-500 {
  border-color: var(--dashboard-red);
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.2);
}

.dark .border-amber-500,
.dark .border-orange-500,
.dark .border-yellow-500 {
  border-color: var(--dashboard-amber);
  box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);
}

/* Professional scrollbar styling for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: var(--color-surface);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-border) 0%, rgba(71, 85, 105, 0.8) 100%);
  border-radius: 4px;
  border: 1px solid var(--color-border);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(71, 85, 105, 0.9) 0%, rgba(100, 116, 139, 0.8) 100%);
}

/* Final polish - subtle animations */
.dark * {
  scroll-behavior: smooth;
}

.dark .transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}