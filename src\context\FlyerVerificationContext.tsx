import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from 'react-hot-toast';
import {
  DocumentScanResult,
  VerificationRecord,
  TravelRestriction,
  documentVerificationService,
  travelRestrictionsService,
  analyticsService,
  handleApiError
} from '../services/flyerVerificationService';

interface VerificationStats {
  totalVerifications: number;
  successfulVerifications: number;
  failedVerifications: number;
  pendingVerifications: number;
  accuracyRate: number;
  averageProcessingTime: number;
  riskDistribution: Record<string, number>;
}

interface VerificationFilters {
  startDate?: string;
  endDate?: string;
  status?: string;
  riskLevel?: string;
  limit?: number;
  offset?: number;
}

interface TravelRestrictionFilters {
  country?: string;
  nationality?: string;
  restrictionType?: string;
  severity?: string;
  isActive?: boolean;
}

interface TravelRestrictionCheckResult {
  isRestricted: boolean;
  restrictions: TravelRestriction[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface ComplianceReportOptions {
  startDate: string;
  endDate: string;
  includeAuditTrail: boolean;
  includeVendorLiability: boolean;
  format: 'pdf' | 'excel';
}

export interface FlyerVerificationContextType {
  // State
  isLoading: boolean;
  currentScan: DocumentScanResult | null;
  verificationHistory: VerificationRecord[];
  travelRestrictions: TravelRestriction[];
  stats: VerificationStats;

  // Document Verification Actions
  scanDocument: (file: File, documentType: 'passport' | 'visa', passengerName?: string) => Promise<DocumentScanResult>;
  clearCurrentScan: () => void;
  retryVerification: (id: string) => Promise<DocumentScanResult>;

  // Verification History Actions
  loadVerificationHistory: (filters?: VerificationFilters) => Promise<void>;
  getVerificationById: (id: string) => Promise<VerificationRecord | null>;

  // Travel Restrictions Actions
  loadTravelRestrictions: (filters?: TravelRestrictionFilters) => Promise<void>;
  createTravelRestriction: (restriction: Omit<TravelRestriction, 'id' | 'lastUpdated' | 'updatedBy'>) => Promise<TravelRestriction>;
  updateTravelRestriction: (id: string, updates: Partial<TravelRestriction>) => Promise<TravelRestriction>;
  deleteTravelRestriction: (id: string) => Promise<void>;
  checkTravelRestrictions: (nationality: string, destinationCountry: string) => Promise<TravelRestrictionCheckResult | null>;
  refreshRestrictionsData: () => Promise<void>;

  // Analytics Actions
  loadStats: (period?: '24h' | '7d' | '30d' | '90d') => Promise<void>;
  generateComplianceReport: (options: ComplianceReportOptions) => Promise<void>;

  // Error handling
  error: string | null;
  clearError: () => void;
}

export const FlyerVerificationContext = createContext<FlyerVerificationContextType | undefined>(undefined);

interface FlyerVerificationProviderProps {
  children: ReactNode;
}

export const FlyerVerificationProvider: React.FC<FlyerVerificationProviderProps> = ({ children }) => {
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [currentScan, setCurrentScan] = useState<DocumentScanResult | null>(null);
  const [verificationHistory, setVerificationHistory] = useState<VerificationRecord[]>([]);
  const [travelRestrictions, setTravelRestrictions] = useState<TravelRestriction[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    totalVerifications: 0,
    successfulVerifications: 0,
    failedVerifications: 0,
    pendingVerifications: 0,
    accuracyRate: 0,
    averageProcessingTime: 0,
    riskDistribution: {}
  });
  const [error, setError] = useState<string | null>(null);

  // Error handling
  const handleError = (error: unknown, defaultMessage: string) => {
    const verificationError = handleApiError(error);
    setError(verificationError.message);
    toast.error(verificationError.message);
    console.error(defaultMessage, verificationError);
  };

  const clearError = () => {
    setError(null);
  };

  // Document Verification Actions
  const scanDocument = async (file: File, documentType: 'passport' | 'visa', passengerName?: string): Promise<DocumentScanResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await documentVerificationService.scanDocument({
        file,
        documentType,
        passengerName
      });
      
      setCurrentScan(result);

      // Refresh stats
      await loadStats();
      
      return result;
    } catch (error) {
      handleError(error, 'Failed to scan document');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearCurrentScan = () => {
    setCurrentScan(null);
  };

  const retryVerification = async (id: string): Promise<DocumentScanResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await documentVerificationService.retryVerification(id);
      setCurrentScan(result);
      await loadStats();
      return result;
    } catch (error) {
      handleError(error, 'Failed to retry verification');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Verification History Actions
  const loadVerificationHistory = async (filters?: VerificationFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await documentVerificationService.getVerificationHistory(filters);
      setVerificationHistory(response.records);
    } catch (error) {
      handleError(error, 'Failed to load verification history');
    } finally {
      setIsLoading(false);
    }
  };

  const getVerificationById = async (id: string): Promise<VerificationRecord | null> => {
    try {
      return await documentVerificationService.getVerificationById(id);
    } catch (error) {
      handleError(error, 'Failed to get verification record');
      return null;
    }
  };

  // Travel Restrictions Actions
  const loadTravelRestrictions = async (filters?: TravelRestrictionFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      const restrictions = await travelRestrictionsService.getRestrictions(filters);
      setTravelRestrictions(restrictions);
    } catch (error) {
      handleError(error, 'Failed to load travel restrictions');
    } finally {
      setIsLoading(false);
    }
  };

  const createTravelRestriction = async (restriction: Omit<TravelRestriction, 'id' | 'lastUpdated' | 'updatedBy'>): Promise<TravelRestriction> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const newRestriction = await travelRestrictionsService.createRestriction(restriction);
      setTravelRestrictions(prev => [newRestriction, ...prev]);
      

      
      toast.success('Travel restriction created successfully');
      return newRestriction;
    } catch (error) {
      handleError(error, 'Failed to create travel restriction');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateTravelRestriction = async (id: string, updates: Partial<TravelRestriction>): Promise<TravelRestriction> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedRestriction = await travelRestrictionsService.updateRestriction(id, updates);
      setTravelRestrictions(prev => prev.map(r => r.id === id ? updatedRestriction : r));
      

      
      toast.success('Travel restriction updated successfully');
      return updatedRestriction;
    } catch (error) {
      handleError(error, 'Failed to update travel restriction');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTravelRestriction = async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await travelRestrictionsService.deleteRestriction(id);
      setTravelRestrictions(prev => prev.filter(r => r.id !== id));
      

      
      toast.success('Travel restriction deleted successfully');
    } catch (error) {
      handleError(error, 'Failed to delete travel restriction');
    } finally {
      setIsLoading(false);
    }
  };

  const checkTravelRestrictions = async (nationality: string, destinationCountry: string) => {
    try {
      return await travelRestrictionsService.checkRestrictions(nationality, destinationCountry);
    } catch (error) {
      handleError(error, 'Failed to check travel restrictions');
      return null;
    }
  };

  const refreshRestrictionsData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await travelRestrictionsService.refreshRestrictionsData();
      await loadTravelRestrictions();
      toast.success('Travel restrictions data refreshed');
    } catch (error) {
      handleError(error, 'Failed to refresh restrictions data');
    } finally {
      setIsLoading(false);
    }
  };



  // Analytics Actions
  const loadStats = async (period?: '24h' | '7d' | '30d' | '90d') => {
    try {
      const statsData = await analyticsService.getVerificationStats(period);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
      // Don't show error to user for stats loading failures
    }
  };

  const generateComplianceReport = async (options: ComplianceReportOptions): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      await analyticsService.generateComplianceReport(options);
      toast.success('Compliance report generated successfully');
    } catch (error) {
      handleError(error, 'Failed to generate compliance report');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize data on mount with error handling
  useEffect(() => {
    const initializeData = async () => {
      try {
        await loadStats();
        await loadVerificationHistory({ limit: 10 });
        await loadTravelRestrictions({ isActive: true });
      } catch (error) {
        console.warn('Failed to load initial data, using fallback data:', error);
        // Set fallback data for offline/mock mode
        setStats({
          totalVerifications: 1247,
          successfulVerifications: 1198,
          failedVerifications: 23,
          pendingVerifications: 26,
          accuracyRate: 96.1,
          averageProcessingTime: 2.4,
          riskDistribution: { low: 85, medium: 12, high: 3 }
        });
      }
    };

    initializeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Intentionally empty to run only on mount

  const value: FlyerVerificationContextType = {
    // State
    isLoading,
    currentScan,
    verificationHistory,
    travelRestrictions,
    stats,

    // Document Verification Actions
    scanDocument,
    clearCurrentScan,
    retryVerification,

    // Verification History Actions
    loadVerificationHistory,
    getVerificationById,

    // Travel Restrictions Actions
    loadTravelRestrictions,
    createTravelRestriction,
    updateTravelRestriction,
    deleteTravelRestriction,
    checkTravelRestrictions,
    refreshRestrictionsData,

    // Analytics Actions
    loadStats,
    generateComplianceReport,

    // Error handling
    error,
    clearError
  };

  return (
    <FlyerVerificationContext.Provider value={value}>
      {children}
    </FlyerVerificationContext.Provider>
  );
};

export default FlyerVerificationProvider;
