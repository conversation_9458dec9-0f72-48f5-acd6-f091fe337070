import React, { useState, useMemo, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import {
  FileText,
  Clock,
  Users,
  Building,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  Filter,
  Search,
  Calendar,
  Activity,
  TrendingUp,
  TrendingDown,
  ArrowRight,
  Bell,
  Zap,
  Target,
  GitBranch,
  Eye,
  Edit,
  MoreHorizontal,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

// Enhanced Policy Types
interface PolicyDependency {
  id: string;
  name: string;
  type: 'prerequisite' | 'dependent' | 'related';
  status: 'compliant' | 'non_compliant' | 'pending';
}

interface PolicyWorkflow {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'completed' | 'failed';
  progress: number;
  nextAction: string;
  dueDate: Date;
  assignee: string;
}

interface PolicyLifecycle {
  stage: 'draft' | 'review' | 'approved' | 'active' | 'deprecated';
  version: string;
  createdDate: Date;
  lastModified: Date;
  approvedBy?: string;
  expiryDate?: Date;
  renewalRequired: boolean;
}

interface EnhancedPolicy {
  id: string;
  name: string;
  description: string;
  status: 'compliant' | 'non_compliant' | 'pending';
  lastUpdated: string;
  department: string;
  owner: string;
  score: number;
  nextReview: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  lifecycle: PolicyLifecycle;
  dependencies: PolicyDependency[];
  workflows: PolicyWorkflow[];
  realTimeStatus: {
    isOnline: boolean;
    lastSync: Date;
    healthScore: number;
    alerts: number;
  };
  complianceMetrics: {
    adherenceRate: number;
    violationCount: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    lastAudit: Date;
  };
}

interface EnhancedPolicyCardsProps {
  className?: string;
  policies: EnhancedPolicy[];
  onPolicyUpdate?: (policyId: string, updates: Partial<EnhancedPolicy>) => void;
  onWorkflowTrigger?: (policyId: string, workflowId: string) => void;
}

// Mock data generator for enhanced policies
const generateEnhancedPolicies = (): EnhancedPolicy[] => [
  {
    id: '1',
    name: 'Data Protection Policy',
    description: 'GDPR compliance framework for data handling',
    status: 'compliant',
    lastUpdated: '2024-01-15',
    department: 'Legal & Compliance',
    owner: 'Sarah Johnson',
    score: 95,
    nextReview: '2024-04-15',
    priority: 'high',
    category: 'Data Privacy',
    lifecycle: {
      stage: 'active',
      version: '2.1',
      createdDate: new Date('2023-06-01'),
      lastModified: new Date('2024-01-15'),
      approvedBy: 'Chief Compliance Officer',
      expiryDate: new Date('2024-12-31'),
      renewalRequired: false
    },
    dependencies: [
      { id: 'dep-1', name: 'Privacy Policy', type: 'prerequisite', status: 'compliant' },
      { id: 'dep-2', name: 'Security Framework', type: 'related', status: 'compliant' }
    ],
    workflows: [
      {
        id: 'wf-1',
        name: 'Quarterly Review',
        status: 'active',
        progress: 75,
        nextAction: 'Stakeholder Approval',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        assignee: 'Sarah Johnson'
      }
    ],
    realTimeStatus: {
      isOnline: true,
      lastSync: new Date(Date.now() - 5 * 60 * 1000),
      healthScore: 98,
      alerts: 0
    },
    complianceMetrics: {
      adherenceRate: 95.2,
      violationCount: 2,
      riskLevel: 'low',
      lastAudit: new Date('2024-01-10')
    }
  },
  {
    id: '2',
    name: 'Privacy Policy',
    description: 'User data privacy guidelines and procedures',
    status: 'non_compliant',
    lastUpdated: '2024-01-14',
    department: 'Privacy Office',
    owner: 'Michael Chen',
    score: 72,
    nextReview: '2024-03-14',
    priority: 'high',
    category: 'Privacy',
    lifecycle: {
      stage: 'review',
      version: '1.8',
      createdDate: new Date('2023-03-15'),
      lastModified: new Date('2024-01-14'),
      renewalRequired: true
    },
    dependencies: [
      { id: 'dep-3', name: 'Cookie Policy', type: 'dependent', status: 'pending' },
      { id: 'dep-4', name: 'Terms of Service', type: 'related', status: 'non_compliant' }
    ],
    workflows: [
      {
        id: 'wf-2',
        name: 'Compliance Remediation',
        status: 'active',
        progress: 45,
        nextAction: 'Legal Review',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        assignee: 'Michael Chen'
      },
      {
        id: 'wf-3',
        name: 'Policy Update',
        status: 'paused',
        progress: 20,
        nextAction: 'Content Review',
        dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        assignee: 'Legal Team'
      }
    ],
    realTimeStatus: {
      isOnline: true,
      lastSync: new Date(Date.now() - 2 * 60 * 1000),
      healthScore: 72,
      alerts: 3
    },
    complianceMetrics: {
      adherenceRate: 78.5,
      violationCount: 8,
      riskLevel: 'medium',
      lastAudit: new Date('2024-01-08')
    }
  },
  {
    id: '3',
    name: 'Security Policy',
    description: 'Information security guidelines and protocols',
    status: 'pending',
    lastUpdated: '2024-01-13',
    department: 'IT Security',
    owner: 'David Wilson',
    score: 85,
    nextReview: '2024-02-13',
    priority: 'medium',
    category: 'Security',
    lifecycle: {
      stage: 'draft',
      version: '3.0-beta',
      createdDate: new Date('2024-01-01'),
      lastModified: new Date('2024-01-13'),
      renewalRequired: false
    },
    dependencies: [
      { id: 'dep-5', name: 'Access Control Policy', type: 'prerequisite', status: 'compliant' },
      { id: 'dep-6', name: 'Incident Response Plan', type: 'related', status: 'pending' }
    ],
    workflows: [
      {
        id: 'wf-4',
        name: 'Initial Assessment',
        status: 'active',
        progress: 60,
        nextAction: 'Technical Review',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        assignee: 'David Wilson'
      }
    ],
    realTimeStatus: {
      isOnline: true,
      lastSync: new Date(Date.now() - 1 * 60 * 1000),
      healthScore: 85,
      alerts: 1
    },
    complianceMetrics: {
      adherenceRate: 82.3,
      violationCount: 4,
      riskLevel: 'medium',
      lastAudit: new Date('2024-01-05')
    }
  }
];

export const EnhancedPolicyCards: React.FC<EnhancedPolicyCardsProps> = ({ 
  className = '', 
  policies: propPolicies,
  onPolicyUpdate,
  onWorkflowTrigger 
}) => {
  const { mode } = useTheme();
  const [policies, setPolicies] = useState<EnhancedPolicy[]>(propPolicies || generateEnhancedPolicies());
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'compliant' | 'non_compliant' | 'pending'>('all');
  const [selectedPriority, setSelectedPriority] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [selectedLifecycleStage, setSelectedLifecycleStage] = useState<'all' | 'draft' | 'review' | 'approved' | 'active' | 'deprecated'>('all');
  const [expandedPolicy, setExpandedPolicy] = useState<string | null>(null);

  // Real-time updates simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setPolicies(prevPolicies => 
        prevPolicies.map(policy => ({
          ...policy,
          realTimeStatus: {
            ...policy.realTimeStatus,
            lastSync: new Date(),
            healthScore: Math.max(50, policy.realTimeStatus.healthScore + (Math.random() - 0.5) * 2)
          }
        }))
      );
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Filter policies based on search and filters
  const filteredPolicies = useMemo(() => {
    return policies.filter(policy => {
      const matchesSearch = searchTerm === '' ||
        policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.category.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = selectedStatus === 'all' || policy.status === selectedStatus;
      const matchesPriority = selectedPriority === 'all' || policy.priority === selectedPriority;
      const matchesLifecycle = selectedLifecycleStage === 'all' || policy.lifecycle.stage === selectedLifecycleStage;

      return matchesSearch && matchesStatus && matchesPriority && matchesLifecycle;
    });
  }, [policies, searchTerm, selectedStatus, selectedPriority, selectedLifecycleStage]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'non_compliant':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-amber-500" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'non_compliant':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPriorityColorClass = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getLifecycleStageColorClass = (stage: string) => {
    switch (stage) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'review':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'approved':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'deprecated':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const handleWorkflowAction = (policyId: string, workflowId: string, action: 'play' | 'pause' | 'restart') => {
    setPolicies(prevPolicies =>
      prevPolicies.map(policy =>
        policy.id === policyId
          ? {
              ...policy,
              workflows: policy.workflows.map(workflow =>
                workflow.id === workflowId
                  ? {
                      ...workflow,
                      status: action === 'play' ? 'active' : action === 'pause' ? 'paused' : 'active',
                      progress: action === 'restart' ? 0 : workflow.progress
                    }
                  : workflow
              )
            }
          : policy
      )
    );

    if (onWorkflowTrigger) {
      onWorkflowTrigger(policyId, workflowId);
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPolicies(generateEnhancedPolicies());
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && policies.length === 0) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingSkeleton key={i} className="h-64" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <FileText className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Enhanced Policy Management</h1>
              <p className="text-text-secondary">
                Real-time policy tracking with lifecycle management and workflow automation
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh policies"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
            <input
              type="text"
              placeholder="Search policies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            />
          </div>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="all">All Status</option>
            <option value="compliant">Compliant</option>
            <option value="non_compliant">Non-Compliant</option>
            <option value="pending">Pending</option>
          </select>

          <select
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="all">All Priority</option>
            <option value="high">High Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="low">Low Priority</option>
          </select>

          <select
            value={selectedLifecycleStage}
            onChange={(e) => setSelectedLifecycleStage(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="all">All Lifecycle</option>
            <option value="draft">Draft</option>
            <option value="review">Review</option>
            <option value="approved">Approved</option>
            <option value="active">Active</option>
            <option value="deprecated">Deprecated</option>
          </select>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Total Policies</p>
                <p className="text-2xl font-bold text-text">{policies.length}</p>
              </div>
              <FileText className="w-8 h-8 text-primary" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Compliant</p>
                <p className="text-2xl font-bold text-green-500">
                  {policies.filter(p => p.status === 'compliant').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Active Workflows</p>
                <p className="text-2xl font-bold text-blue-500">
                  {policies.reduce((sum, p) => sum + p.workflows.filter(w => w.status === 'active').length, 0)}
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Avg Health Score</p>
                <p className="text-2xl font-bold text-text">
                  {Math.round(policies.reduce((sum, p) => sum + p.realTimeStatus.healthScore, 0) / policies.length)}
                </p>
              </div>
              <Target className="w-8 h-8 text-primary" />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Policy Cards */}
      <div className="space-y-4">
        {filteredPolicies.length === 0 ? (
          <div className="bg-surface rounded-lg p-12 text-center">
            <FileText className="w-16 h-16 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text mb-2">No Policies Found</h3>
            <p className="text-text-secondary">No policies match your current filter criteria.</p>
          </div>
        ) : (
          filteredPolicies.map((policy) => (
            <div key={policy.id} className="bg-surface rounded-lg border border-border hover:shadow-lg transition-all duration-200">
              {/* Policy Header */}
              <div className="p-6 border-b border-border">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(policy.status)}
                      <h3 className="text-xl font-semibold text-text">{policy.name}</h3>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(policy.status)}`}>
                          {policy.status.replace('_', ' ').charAt(0).toUpperCase() + policy.status.replace('_', ' ').slice(1)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColorClass(policy.priority)}`}>
                          {policy.priority.charAt(0).toUpperCase() + policy.priority.slice(1)} Priority
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLifecycleStageColorClass(policy.lifecycle.stage)}`}>
                          {policy.lifecycle.stage.charAt(0).toUpperCase() + policy.lifecycle.stage.slice(1)}
                        </span>
                      </div>
                    </div>

                    <p className="text-text-secondary mb-3">{policy.description}</p>

                    <div className="flex items-center gap-6 text-sm text-text-secondary">
                      <span className="flex items-center gap-1">
                        <Building className="w-4 h-4" />
                        {policy.department}
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        {policy.owner}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        Next Review: {new Date(policy.nextReview).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {/* Real-time Status Indicator */}
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${policy.realTimeStatus.isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
                      <span className="text-xs text-text-secondary">
                        {policy.realTimeStatus.isOnline ? 'Online' : 'Offline'}
                      </span>
                    </div>

                    {/* Health Score */}
                    <div className="text-right">
                      <p className="text-lg font-bold text-text">{Math.round(policy.realTimeStatus.healthScore)}</p>
                      <p className="text-xs text-text-secondary">Health Score</p>
                    </div>

                    {/* Alerts */}
                    {policy.realTimeStatus.alerts > 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/20 rounded-full">
                        <Bell className="w-3 h-3 text-red-500" />
                        <span className="text-xs text-red-500 font-medium">{policy.realTimeStatus.alerts}</span>
                      </div>
                    )}

                    <button
                      onClick={() => setExpandedPolicy(expandedPolicy === policy.id ? null : policy.id)}
                      className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
                    >
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Compliance Metrics Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-text-secondary">Compliance Score</span>
                    <span className="text-text font-medium">{policy.score}%</span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        policy.score >= 90 ? 'bg-green-500' :
                        policy.score >= 70 ? 'bg-amber-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${policy.score}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedPolicy === policy.id && (
                <div className="p-6 space-y-6">
                  {/* Lifecycle Information */}
                  <div>
                    <h4 className="text-lg font-semibold text-text mb-3 flex items-center gap-2">
                      <GitBranch className="w-5 h-5" />
                      Lifecycle Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Version</p>
                        <p className="text-lg font-semibold text-text">{policy.lifecycle.version}</p>
                      </div>
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Created</p>
                        <p className="text-lg font-semibold text-text">
                          {policy.lifecycle.createdDate.toLocaleDateString()}
                        </p>
                      </div>
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Last Modified</p>
                        <p className="text-lg font-semibold text-text">
                          {policy.lifecycle.lastModified.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Dependencies */}
                  <div>
                    <h4 className="text-lg font-semibold text-text mb-3 flex items-center gap-2">
                      <GitBranch className="w-5 h-5" />
                      Policy Dependencies
                    </h4>
                    <div className="space-y-2">
                      {policy.dependencies.map((dep) => (
                        <div key={dep.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(dep.status)}
                            <span className="text-text font-medium">{dep.name}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              dep.type === 'prerequisite' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                              dep.type === 'dependent' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' :
                              'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                            }`}>
                              {dep.type.charAt(0).toUpperCase() + dep.type.slice(1)}
                            </span>
                          </div>
                          <ArrowRight className="w-4 h-4 text-text-secondary" />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Active Workflows */}
                  <div>
                    <h4 className="text-lg font-semibold text-text mb-3 flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      Active Workflows
                    </h4>
                    <div className="space-y-3">
                      {policy.workflows.map((workflow) => (
                        <div key={workflow.id} className="bg-card rounded-lg p-4 border border-border">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h5 className="text-lg font-semibold text-text">{workflow.name}</h5>
                              <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                                <span>Assignee: {workflow.assignee}</span>
                                <span>Due: {workflow.dueDate.toLocaleDateString()}</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  workflow.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                  workflow.status === 'paused' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400' :
                                  workflow.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                                  'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                }`}>
                                  {workflow.status.charAt(0).toUpperCase() + workflow.status.slice(1)}
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handleWorkflowAction(policy.id, workflow.id, 'play')}
                                className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                                title="Start/Resume workflow"
                              >
                                <Play className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleWorkflowAction(policy.id, workflow.id, 'pause')}
                                className="p-2 text-amber-600 hover:bg-amber-100 dark:hover:bg-amber-900/20 rounded-lg transition-colors"
                                title="Pause workflow"
                              >
                                <Pause className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleWorkflowAction(policy.id, workflow.id, 'restart')}
                                className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                                title="Restart workflow"
                              >
                                <RotateCcw className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-text-secondary">Progress</span>
                              <span className="text-text font-medium">{workflow.progress}%</span>
                            </div>
                            <div className="w-full bg-border rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  workflow.status === 'active' ? 'bg-blue-500' :
                                  workflow.status === 'completed' ? 'bg-green-500' :
                                  workflow.status === 'failed' ? 'bg-red-500' : 'bg-gray-500'
                                }`}
                                style={{ width: `${workflow.progress}%` }}
                              />
                            </div>
                            <p className="text-sm text-text-secondary">
                              Next Action: {workflow.nextAction}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Compliance Metrics */}
                  <div>
                    <h4 className="text-lg font-semibold text-text mb-3 flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Compliance Metrics
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Adherence Rate</p>
                        <p className="text-2xl font-bold text-text">{policy.complianceMetrics.adherenceRate.toFixed(1)}%</p>
                      </div>
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Violations</p>
                        <p className="text-2xl font-bold text-red-500">{policy.complianceMetrics.violationCount}</p>
                      </div>
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Risk Level</p>
                        <span className={`px-2 py-1 rounded-full text-sm font-medium ${
                          policy.complianceMetrics.riskLevel === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          policy.complianceMetrics.riskLevel === 'medium' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400' :
                          policy.complianceMetrics.riskLevel === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {policy.complianceMetrics.riskLevel.charAt(0).toUpperCase() + policy.complianceMetrics.riskLevel.slice(1)}
                        </span>
                      </div>
                      <div className="bg-card rounded-lg p-4 border border-border">
                        <p className="text-sm text-text-secondary">Last Audit</p>
                        <p className="text-sm font-semibold text-text">
                          {policy.complianceMetrics.lastAudit.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-3 pt-4 border-t border-border">
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors">
                      <Eye className="w-4 h-4" />
                      View Details
                    </button>
                    <button className="flex items-center gap-2 px-4 py-2 bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
                      <Edit className="w-4 h-4" />
                      Edit Policy
                    </button>
                    <button className="flex items-center gap-2 px-4 py-2 bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
                      <Settings className="w-4 h-4" />
                      Configure
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};
