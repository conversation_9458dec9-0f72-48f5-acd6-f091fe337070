import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { <PERSON>, Scatter, Line, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  AlertTriangle,
  Shield,
  TrendingUp,
  TrendingDown,
  Activity,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Search,
  Calendar,
  Clock,
  Users,
  Building,
  Target,
  Eye,
  FileText,
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Layers,
  History,
  User,
  MapPin,
  Thermometer
} from 'lucide-react';

// Risk Assessment Types
interface RiskFactor {
  id: string;
  name: string;
  category: 'operational' | 'financial' | 'compliance' | 'strategic' | 'technical';
  probability: number; // 1-5 scale
  impact: number; // 1-5 scale
  riskScore: number; // calculated: probability * impact
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  status: 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred';
  owner: string;
  department: string;
  lastAssessed: Date;
  nextReview: Date;
  mitigationActions: Array<{
    id: string;
    action: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignee: string;
    dueDate: Date;
    effectiveness: number; // 1-5 scale
  }>;
  historicalScores: Array<{
    date: string;
    probability: number;
    impact: number;
    score: number;
  }>;
}

interface AuditLogEntry {
  id: string;
  timestamp: Date;
  user: string;
  action: 'created' | 'updated' | 'assessed' | 'mitigated' | 'reviewed' | 'approved';
  entityType: 'risk' | 'mitigation' | 'assessment' | 'policy';
  entityId: string;
  entityName: string;
  details: string;
  changes?: Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }>;
  ipAddress?: string;
  userAgent?: string;
}

interface RiskHeatMapData {
  probability: number;
  impact: number;
  count: number;
  risks: string[];
}

interface RiskAssessmentData {
  overview: {
    totalRisks: number;
    criticalRisks: number;
    highRisks: number;
    mediumRisks: number;
    lowRisks: number;
    averageRiskScore: number;
    riskTrend: 'increasing' | 'decreasing' | 'stable';
    lastAssessment: Date;
    nextScheduledAssessment: Date;
  };
  riskFactors: RiskFactor[];
  heatMapData: RiskHeatMapData[];
  auditTrail: AuditLogEntry[];
  categoryBreakdown: {
    operational: { count: number; averageScore: number; trend: string };
    financial: { count: number; averageScore: number; trend: string };
    compliance: { count: number; averageScore: number; trend: string };
    strategic: { count: number; averageScore: number; trend: string };
    technical: { count: number; averageScore: number; trend: string };
  };
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    pendingAssessments: number;
    recentChanges: number;
  };
}

interface RiskAssessmentDashboardProps {
  className?: string;
}

// Mock data generator
const generateRiskAssessmentData = (): RiskAssessmentData => {
  const riskFactors: RiskFactor[] = [
    {
      id: 'risk-001',
      name: 'Data Breach Vulnerability',
      category: 'technical',
      probability: 4,
      impact: 5,
      riskScore: 20,
      riskLevel: 'critical',
      status: 'assessed',
      owner: 'John Smith',
      department: 'IT Security',
      lastAssessed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
      mitigationActions: [
        {
          id: 'mit-001',
          action: 'Implement multi-factor authentication',
          status: 'in_progress',
          assignee: 'Security Team',
          dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          effectiveness: 4
        },
        {
          id: 'mit-002',
          action: 'Conduct security awareness training',
          status: 'completed',
          assignee: 'HR Department',
          dueDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          effectiveness: 3
        }
      ],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        probability: 3 + Math.random() * 2,
        impact: 4 + Math.random(),
        score: (3 + Math.random() * 2) * (4 + Math.random())
      }))
    },
    {
      id: 'risk-002',
      name: 'Regulatory Compliance Failure',
      category: 'compliance',
      probability: 3,
      impact: 4,
      riskScore: 12,
      riskLevel: 'high',
      status: 'mitigated',
      owner: 'Sarah Johnson',
      department: 'Legal & Compliance',
      lastAssessed: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
      mitigationActions: [
        {
          id: 'mit-003',
          action: 'Update compliance policies',
          status: 'completed',
          assignee: 'Legal Team',
          dueDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          effectiveness: 4
        }
      ],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        probability: 2 + Math.random() * 2,
        impact: 3 + Math.random() * 2,
        score: (2 + Math.random() * 2) * (3 + Math.random() * 2)
      }))
    },
    {
      id: 'risk-003',
      name: 'Market Volatility Impact',
      category: 'financial',
      probability: 4,
      impact: 3,
      riskScore: 12,
      riskLevel: 'high',
      status: 'identified',
      owner: 'Michael Chen',
      department: 'Finance',
      lastAssessed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      mitigationActions: [
        {
          id: 'mit-004',
          action: 'Diversify investment portfolio',
          status: 'pending',
          assignee: 'Investment Team',
          dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
          effectiveness: 3
        }
      ],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        probability: 3 + Math.random() * 2,
        impact: 2 + Math.random() * 2,
        score: (3 + Math.random() * 2) * (2 + Math.random() * 2)
      }))
    },
    {
      id: 'risk-004',
      name: 'Key Personnel Departure',
      category: 'operational',
      probability: 2,
      impact: 4,
      riskScore: 8,
      riskLevel: 'medium',
      status: 'assessed',
      owner: 'David Wilson',
      department: 'Human Resources',
      lastAssessed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000),
      mitigationActions: [
        {
          id: 'mit-005',
          action: 'Implement succession planning',
          status: 'in_progress',
          assignee: 'HR Team',
          dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
          effectiveness: 4
        }
      ],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        probability: 1 + Math.random() * 2,
        impact: 3 + Math.random() * 2,
        score: (1 + Math.random() * 2) * (3 + Math.random() * 2)
      }))
    },
    {
      id: 'risk-005',
      name: 'Strategic Partnership Failure',
      category: 'strategic',
      probability: 2,
      impact: 3,
      riskScore: 6,
      riskLevel: 'medium',
      status: 'accepted',
      owner: 'Lisa Anderson',
      department: 'Business Development',
      lastAssessed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + 42 * 24 * 60 * 60 * 1000),
      mitigationActions: [],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        probability: 1 + Math.random() * 2,
        impact: 2 + Math.random() * 2,
        score: (1 + Math.random() * 2) * (2 + Math.random() * 2)
      }))
    }
  ];

  // Generate heat map data
  const heatMapData: RiskHeatMapData[] = [];
  for (let probability = 1; probability <= 5; probability++) {
    for (let impact = 1; impact <= 5; impact++) {
      const risks = riskFactors.filter(r => 
        Math.round(r.probability) === probability && Math.round(r.impact) === impact
      );
      if (risks.length > 0) {
        heatMapData.push({
          probability,
          impact,
          count: risks.length,
          risks: risks.map(r => r.name)
        });
      }
    }
  }

  // Generate audit trail
  const auditTrail: AuditLogEntry[] = Array.from({ length: 50 }, (_, i) => ({
    id: `audit-${i + 1}`,
    timestamp: new Date(Date.now() - i * 2 * 60 * 60 * 1000),
    user: ['John Smith', 'Sarah Johnson', 'Michael Chen', 'David Wilson', 'Lisa Anderson'][Math.floor(Math.random() * 5)],
    action: ['created', 'updated', 'assessed', 'mitigated', 'reviewed', 'approved'][Math.floor(Math.random() * 6)] as any,
    entityType: ['risk', 'mitigation', 'assessment', 'policy'][Math.floor(Math.random() * 4)] as any,
    entityId: `entity-${i + 1}`,
    entityName: riskFactors[Math.floor(Math.random() * riskFactors.length)].name,
    details: `Risk assessment ${['updated', 'reviewed', 'approved', 'mitigated'][Math.floor(Math.random() * 4)]} with new scoring methodology`,
    ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }));

  const criticalRisks = riskFactors.filter(r => r.riskLevel === 'critical').length;
  const highRisks = riskFactors.filter(r => r.riskLevel === 'high').length;
  const mediumRisks = riskFactors.filter(r => r.riskLevel === 'medium').length;
  const lowRisks = riskFactors.filter(r => r.riskLevel === 'low').length;
  const averageRiskScore = riskFactors.reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.length;

  return {
    overview: {
      totalRisks: riskFactors.length,
      criticalRisks,
      highRisks,
      mediumRisks,
      lowRisks,
      averageRiskScore,
      riskTrend: 'stable',
      lastAssessment: new Date(Date.now() - 24 * 60 * 60 * 1000),
      nextScheduledAssessment: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    },
    riskFactors,
    heatMapData,
    auditTrail,
    categoryBreakdown: {
      operational: { 
        count: riskFactors.filter(r => r.category === 'operational').length,
        averageScore: riskFactors.filter(r => r.category === 'operational').reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.filter(r => r.category === 'operational').length || 0,
        trend: 'stable'
      },
      financial: { 
        count: riskFactors.filter(r => r.category === 'financial').length,
        averageScore: riskFactors.filter(r => r.category === 'financial').reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.filter(r => r.category === 'financial').length || 0,
        trend: 'increasing'
      },
      compliance: { 
        count: riskFactors.filter(r => r.category === 'compliance').length,
        averageScore: riskFactors.filter(r => r.category === 'compliance').reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.filter(r => r.category === 'compliance').length || 0,
        trend: 'decreasing'
      },
      strategic: { 
        count: riskFactors.filter(r => r.category === 'strategic').length,
        averageScore: riskFactors.filter(r => r.category === 'strategic').reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.filter(r => r.category === 'strategic').length || 0,
        trend: 'stable'
      },
      technical: { 
        count: riskFactors.filter(r => r.category === 'technical').length,
        averageScore: riskFactors.filter(r => r.category === 'technical').reduce((sum, r) => sum + r.riskScore, 0) / riskFactors.filter(r => r.category === 'technical').length || 0,
        trend: 'increasing'
      }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      pendingAssessments: Math.floor(Math.random() * 10),
      recentChanges: Math.floor(Math.random() * 5)
    }
  };
};

const RiskAssessmentDashboard: React.FC<RiskAssessmentDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<RiskAssessmentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'operational' | 'financial' | 'compliance' | 'strategic' | 'technical'>('all');
  const [selectedRiskLevel, setSelectedRiskLevel] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred'>('all');
  const [expandedRisk, setExpandedRisk] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'heatmap' | 'audit'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [auditSearchTerm, setAuditSearchTerm] = useState('');
  const [showAllAuditEntries, setShowAllAuditEntries] = useState(false);

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1500));
        const data = generateRiskAssessmentData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load risk assessment data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            pendingAssessments: Math.floor(Math.random() * 10),
            recentChanges: Math.floor(Math.random() * 5)
          }
        };
      });
    }, 45000); // Update every 45 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter risks based on criteria
  const filteredRisks = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.riskFactors.filter(risk => {
      const matchesCategory = selectedCategory === 'all' || risk.category === selectedCategory;
      const matchesRiskLevel = selectedRiskLevel === 'all' || risk.riskLevel === selectedRiskLevel;
      const matchesStatus = selectedStatus === 'all' || risk.status === selectedStatus;
      const matchesSearch = searchTerm === '' ||
        risk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        risk.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
        risk.department.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesCategory && matchesRiskLevel && matchesStatus && matchesSearch;
    });
  }, [dashboardData, selectedCategory, selectedRiskLevel, selectedStatus, searchTerm]);

  // Filter audit trail
  const filteredAuditTrail = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.auditTrail.filter(entry => {
      const matchesSearch = auditSearchTerm === '' ||
        entry.user.toLowerCase().includes(auditSearchTerm.toLowerCase()) ||
        entry.entityName.toLowerCase().includes(auditSearchTerm.toLowerCase()) ||
        entry.details.toLowerCase().includes(auditSearchTerm.toLowerCase()) ||
        entry.action.toLowerCase().includes(auditSearchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [dashboardData, auditSearchTerm]);

  // Get displayed audit trail entries (10 most recent or all if expanded)
  const displayedAuditTrail = useMemo(() => {
    const maxEntries = showAllAuditEntries ? filteredAuditTrail.length : 10;
    return filteredAuditTrail.slice(0, maxEntries);
  }, [filteredAuditTrail, showAllAuditEntries]);

  // Generate risk heat map chart data
  const heatMapChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      datasets: [{
        label: 'Risk Heat Map',
        data: dashboardData.heatMapData.map(item => ({
          x: item.probability,
          y: item.impact,
          r: Math.max(5, item.count * 8) // Bubble size based on count
        })),
        backgroundColor: dashboardData.heatMapData.map(item => {
          const riskScore = item.probability * item.impact;
          if (riskScore >= 16) return 'rgba(248, 113, 113, 0.8)'; // Critical - Red
          if (riskScore >= 12) return 'rgba(251, 191, 36, 0.8)';  // High - Amber
          if (riskScore >= 6) return 'rgba(59, 130, 246, 0.8)';   // Medium - Blue
          return 'rgba(52, 211, 153, 0.8)';                       // Low - Green
        }),
        borderColor: dashboardData.heatMapData.map(item => {
          const riskScore = item.probability * item.impact;
          if (riskScore >= 16) return 'rgb(248, 113, 113)';
          if (riskScore >= 12) return 'rgb(251, 191, 36)';
          if (riskScore >= 6) return 'rgb(59, 130, 246)';
          return 'rgb(52, 211, 153)';
        }),
        borderWidth: 2,
      }]
    };
  }, [dashboardData]);

  // Generate category breakdown chart
  const categoryBreakdownData = useMemo(() => {
    if (!dashboardData) return null;

    const categories = Object.entries(dashboardData.categoryBreakdown);

    return {
      labels: categories.map(([key]) => key.charAt(0).toUpperCase() + key.slice(1)),
      datasets: [{
        label: 'Average Risk Score',
        data: categories.map(([, value]) => value.averageScore),
        backgroundColor: [
          'rgba(139, 92, 246, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(248, 113, 113, 0.8)'
        ],
        borderColor: [
          'rgb(139, 92, 246)',
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(245, 158, 11)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'medium':
        return <AlertCircle className="w-5 h-5 text-blue-500" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Shield className="w-5 h-5 text-gray-500" />;
    }
  };

  const getRiskLevelColorClass = (level: string) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'mitigated':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'assessed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'identified':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'accepted':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'transferred':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <FileText className="w-4 h-4 text-blue-500" />;
      case 'updated':
        return <Settings className="w-4 h-4 text-amber-500" />;
      case 'assessed':
        return <Target className="w-4 h-4 text-purple-500" />;
      case 'mitigated':
        return <Shield className="w-4 h-4 text-green-500" />;
      case 'reviewed':
        return <Eye className="w-4 h-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateRiskAssessmentData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRiskClick = useCallback((riskId: string) => {
    setExpandedRisk(expandedRisk === riskId ? null : riskId);
  }, [expandedRisk]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-80" />
            <LoadingSkeleton className="h-80" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Risk Assessment Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <AlertTriangle className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Risk Assessment & Audit Trail</h1>
              <p className="text-text-secondary">
                Comprehensive risk management with heat maps, automated scoring, and detailed audit logging
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • {dashboardData.realTimeUpdates.pendingAssessments} pending
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total Risks</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.totalRisks}</p>
                </div>
                <Shield className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Critical</p>
                  <p className="text-2xl font-bold text-red-500">{dashboardData.overview.criticalRisks}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">High Risk</p>
                  <p className="text-2xl font-bold text-amber-500">{dashboardData.overview.highRisks}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-amber-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Medium Risk</p>
                  <p className="text-2xl font-bold text-blue-500">{dashboardData.overview.mediumRisks}</p>
                </div>
                <AlertCircle className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Avg Risk Score</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.averageRiskScore.toFixed(1)}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-500" />
              </div>
            </div>
          </div>
        )}

        {/* View Tabs */}
        <div className="flex items-center gap-2 mb-6">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'overview'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('heatmap')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'heatmap'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Thermometer className="w-4 h-4 inline mr-2" />
            Heat Map
          </button>
          <button
            onClick={() => setActiveView('audit')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'audit'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <History className="w-4 h-4 inline mr-2" />
            Audit Trail
          </button>
        </div>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && (
        <>
          {/* Category Breakdown Chart */}
          <div className="bg-surface rounded-lg p-6">
            <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Risk Category Breakdown
            </h3>
            {categoryBreakdownData && (
              <div className="h-64">
                <Bar
                  data={categoryBreakdownData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        backgroundColor: chartTheme.tooltipBg,
                        titleColor: chartTheme.textColor,
                        bodyColor: chartTheme.textColor,
                        borderColor: chartTheme.borderColor,
                        borderWidth: 1,
                        callbacks: {
                          label: (context) => `Average Score: ${context.parsed.y.toFixed(1)}`
                        }
                      }
                    },
                    scales: {
                      x: {
                        grid: {
                          color: chartTheme.gridColor,
                        },
                        ticks: {
                          color: chartTheme.textSecondary,
                        }
                      },
                      y: {
                        grid: {
                          color: chartTheme.gridColor,
                        },
                        ticks: {
                          color: chartTheme.textSecondary,
                        },
                        min: 0,
                        max: 25
                      }
                    },
                    onClick: (event, elements) => {
                      if (elements.length > 0) {
                        const index = elements[0].index;
                        const categories = ['operational', 'financial', 'compliance', 'strategic', 'technical'];
                        setSelectedCategory(categories[index] as any);
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>

          {/* Filters and Risk List */}
          <div className="bg-surface rounded-lg p-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
                <input
                  type="text"
                  placeholder="Search risks, owners, or departments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                />
              </div>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="all">All Categories</option>
                <option value="operational">Operational</option>
                <option value="financial">Financial</option>
                <option value="compliance">Compliance</option>
                <option value="strategic">Strategic</option>
                <option value="technical">Technical</option>
              </select>

              <select
                value={selectedRiskLevel}
                onChange={(e) => setSelectedRiskLevel(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="all">All Risk Levels</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="all">All Status</option>
                <option value="identified">Identified</option>
                <option value="assessed">Assessed</option>
                <option value="mitigated">Mitigated</option>
                <option value="accepted">Accepted</option>
                <option value="transferred">Transferred</option>
              </select>
            </div>

            {/* Risk Cards */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-text">Risk Factors ({filteredRisks.length})</h3>

              {filteredRisks.length === 0 ? (
                <div className="text-center py-8">
                  <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                  <p className="text-text-secondary">No risks found matching your criteria.</p>
                </div>
              ) : (
                filteredRisks.map((risk) => (
                  <div key={risk.id} className="bg-card rounded-lg border border-border overflow-hidden">
                    {/* Risk Header */}
                    <div
                      className="p-4 cursor-pointer hover:bg-border/30 transition-colors"
                      onClick={() => handleRiskClick(risk.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getRiskLevelIcon(risk.riskLevel)}
                          <div>
                            <h4 className="text-lg font-semibold text-text">{risk.name}</h4>
                            <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                              <span className="capitalize">{risk.category}</span>
                              <span className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {risk.owner}
                              </span>
                              <span className="flex items-center gap-1">
                                <Building className="w-3 h-3" />
                                {risk.department}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          <div className="text-center">
                            <p className="text-2xl font-bold text-text">{risk.riskScore}</p>
                            <p className="text-xs text-text-secondary">Risk Score</p>
                          </div>

                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColorClass(risk.riskLevel)}`}>
                              {risk.riskLevel.charAt(0).toUpperCase() + risk.riskLevel.slice(1)}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(risk.status)}`}>
                              {risk.status.charAt(0).toUpperCase() + risk.status.slice(1)}
                            </span>

                            {expandedRisk === risk.id ? (
                              <ChevronDown className="w-5 h-5 text-text-secondary" />
                            ) : (
                              <ChevronRight className="w-5 h-5 text-text-secondary" />
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Risk Matrix */}
                      <div className="mt-4 grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-text-secondary">Probability</p>
                          <div className="flex items-center gap-2">
                            <div className="flex-1 bg-border rounded-full h-2">
                              <div
                                className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                                style={{ width: `${(risk.probability / 5) * 100}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium text-text">{risk.probability}/5</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-text-secondary">Impact</p>
                          <div className="flex items-center gap-2">
                            <div className="flex-1 bg-border rounded-full h-2">
                              <div
                                className="h-2 bg-red-500 rounded-full transition-all duration-300"
                                style={{ width: `${(risk.impact / 5) * 100}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium text-text">{risk.impact}/5</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Expanded Risk Details */}
                    {expandedRisk === risk.id && (
                      <div className="border-t border-border p-4 bg-background/50">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Mitigation Actions */}
                          <div>
                            <h5 className="text-lg font-semibold text-text mb-3">Mitigation Actions</h5>
                            {risk.mitigationActions.length === 0 ? (
                              <p className="text-text-secondary">No mitigation actions defined.</p>
                            ) : (
                              <div className="space-y-3">
                                {risk.mitigationActions.map((action) => (
                                  <div key={action.id} className="bg-card rounded-lg p-3 border border-border">
                                    <div className="flex items-start justify-between mb-2">
                                      <h6 className="font-medium text-text">{action.action}</h6>
                                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        action.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                        action.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                                        'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                                      }`}>
                                        {action.status.replace('_', ' ')}
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between text-sm text-text-secondary">
                                      <span>Assignee: {action.assignee}</span>
                                      <span>Due: {action.dueDate.toLocaleDateString()}</span>
                                      <span>Effectiveness: {action.effectiveness}/5</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Historical Trend */}
                          <div>
                            <h5 className="text-lg font-semibold text-text mb-3">Risk Score Trend</h5>
                            <div className="h-48">
                              <Line
                                data={{
                                  labels: risk.historicalScores.slice(-6).map(h => new Date(h.date).toLocaleDateString('en-US', { month: 'short' })),
                                  datasets: [{
                                    label: 'Risk Score',
                                    data: risk.historicalScores.slice(-6).map(h => h.score),
                                    borderColor: 'rgb(79, 142, 247)',
                                    backgroundColor: 'rgba(79, 142, 247, 0.1)',
                                    borderWidth: 3,
                                    fill: true,
                                    tension: 0.4,
                                  }]
                                }}
                                options={{
                                  responsive: true,
                                  maintainAspectRatio: false,
                                  plugins: {
                                    legend: {
                                      display: false
                                    },
                                    tooltip: {
                                      backgroundColor: chartTheme.tooltipBg,
                                      titleColor: chartTheme.textColor,
                                      bodyColor: chartTheme.textColor,
                                      borderColor: chartTheme.borderColor,
                                      borderWidth: 1,
                                    }
                                  },
                                  scales: {
                                    x: {
                                      grid: {
                                        color: chartTheme.gridColor,
                                      },
                                      ticks: {
                                        color: chartTheme.textSecondary,
                                      }
                                    },
                                    y: {
                                      grid: {
                                        color: chartTheme.gridColor,
                                      },
                                      ticks: {
                                        color: chartTheme.textSecondary,
                                      }
                                    }
                                  }
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </>
      )}

      {/* Heat Map View */}
      {activeView === 'heatmap' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Thermometer className="w-5 h-5" />
            Risk Heat Map
          </h3>
          <p className="text-text-secondary mb-6">
            Interactive risk heat map showing probability vs impact. Bubble size represents the number of risks in each category.
          </p>

          {heatMapChartData && (
            <div className="h-96">
              <Scatter
                data={heatMapChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        title: () => 'Risk Details',
                        label: (context) => {
                          const dataIndex = context.dataIndex;
                          const heatMapItem = dashboardData?.heatMapData[dataIndex];
                          if (heatMapItem) {
                            return [
                              `Probability: ${heatMapItem.probability}/5`,
                              `Impact: ${heatMapItem.impact}/5`,
                              `Risk Score: ${heatMapItem.probability * heatMapItem.impact}`,
                              `Count: ${heatMapItem.count} risks`,
                              ...heatMapItem.risks.map(risk => `• ${risk}`)
                            ];
                          }
                          return [];
                        }
                      }
                    }
                  },
                  scales: {
                    x: {
                      title: {
                        display: true,
                        text: 'Probability',
                        color: chartTheme.textColor
                      },
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        stepSize: 1,
                        min: 0,
                        max: 6
                      }
                    },
                    y: {
                      title: {
                        display: true,
                        text: 'Impact',
                        color: chartTheme.textColor
                      },
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        stepSize: 1,
                        min: 0,
                        max: 6
                      }
                    }
                  }
                }}
              />
            </div>
          )}

          {/* Heat Map Legend */}
          <div className="mt-6 flex items-center justify-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-green-500"></div>
              <span className="text-sm text-text-secondary">Low Risk (1-5)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-blue-500"></div>
              <span className="text-sm text-text-secondary">Medium Risk (6-11)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-amber-500"></div>
              <span className="text-sm text-text-secondary">High Risk (12-15)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-red-500"></div>
              <span className="text-sm text-text-secondary">Critical Risk (16-25)</span>
            </div>
          </div>
        </div>
      )}

      {/* Audit Trail View */}
      {activeView === 'audit' && (
        <div className="bg-surface rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-text flex items-center gap-2">
                <History className="w-5 h-5" />
                Audit Trail
              </h3>
              <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-full">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <span className="text-xs text-primary font-medium">Performance Optimized</span>
              </div>
            </div>

            <div className="flex-1 max-w-md relative ml-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search audit logs..."
                value={auditSearchTerm}
                onChange={(e) => setAuditSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              />
            </div>
          </div>

          <div className="space-y-4">
            {/* Audit Trail Header with Entry Count */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm text-text-secondary">
                  Showing {displayedAuditTrail.length} of {filteredAuditTrail.length} entries
                </span>
                {!showAllAuditEntries && filteredAuditTrail.length > 10 && (
                  <span className="px-2 py-1 bg-primary/20 text-primary text-xs font-medium rounded-full">
                    Recent Only
                  </span>
                )}
              </div>
              {filteredAuditTrail.length > 10 && (
                <button
                  onClick={() => setShowAllAuditEntries(!showAllAuditEntries)}
                  className="flex items-center gap-2 px-3 py-1 bg-primary hover:bg-primary-hover text-white rounded-lg transition-all duration-300 hover:scale-105 text-sm font-medium"
                >
                  {showAllAuditEntries ? (
                    <>
                      <Eye className="w-4 h-4" />
                      Show Recent Only
                    </>
                  ) : (
                    <>
                      <History className="w-4 h-4" />
                      View All Entries
                    </>
                  )}
                </button>
              )}
            </div>

            {/* Audit Trail Entries */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {displayedAuditTrail.length === 0 ? (
                <div className="text-center py-8">
                  <History className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                  <p className="text-text-secondary">No audit entries found matching your search.</p>
                </div>
              ) : (
                displayedAuditTrail.map((entry) => (
                  <div key={entry.id} className="bg-card rounded-lg p-4 border border-border">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getActionIcon(entry.action)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-text">{entry.user}</span>
                          <span className="text-text-secondary">•</span>
                          <span className="text-sm text-text-secondary capitalize">{entry.action}</span>
                          <span className="text-text-secondary">•</span>
                          <span className="text-sm text-text-secondary capitalize">{entry.entityType}</span>
                        </div>

                        <p className="text-text font-medium mb-1">{entry.entityName}</p>
                        <p className="text-sm text-text-secondary mb-2">{entry.details}</p>

                        <div className="flex items-center gap-4 text-xs text-text-secondary">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {entry.timestamp.toLocaleString()}
                          </span>
                          {entry.ipAddress && (
                            <span className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {entry.ipAddress}
                            </span>
                          )}
                          <span className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            ID: {entry.entityId}
                          </span>
                        </div>

                        {entry.changes && entry.changes.length > 0 && (
                          <div className="mt-3 p-2 bg-background/50 rounded border border-border">
                            <p className="text-xs font-medium text-text mb-1">Changes:</p>
                            <div className="space-y-1">
                              {entry.changes.map((change, index) => (
                                <div key={index} className="text-xs text-text-secondary">
                                  <span className="font-medium">{change.field}:</span>
                                  <span className="text-red-500 line-through ml-1">{String(change.oldValue)}</span>
                                  <span className="mx-1">→</span>
                                  <span className="text-green-500">{String(change.newValue)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {!showAllAuditEntries && filteredAuditTrail.length > 10 && (
              <div className="mt-4 text-center">
                <p className="text-sm text-text-secondary">
                  Showing most recent {displayedAuditTrail.length} of {filteredAuditTrail.length} entries. Click "View All Entries" to see more.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RiskAssessmentDashboard;
