import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import complianceRulesService, {
  DPDPRule,
  GDPRRule,
  ComplianceEvent,
  RuleEvaluationResult
} from '../services/complianceRulesService';

// Import the configurationService to get settings
import { configurationService } from '../services/api';

interface ComplianceRulesContextType {
  dpdpRules: DPDPRule[];
  gdprRules: GDPRRule[];
  dpdpEnabled: boolean;
  gdprEnabled: boolean;
  toggleDPDP: (enabled: boolean) => void;
  toggleGDPR: (enabled: boolean) => void;
  evaluateEvent: (event: ComplianceEvent) => Promise<RuleEvaluationResult[]>;
  complianceHistory: RuleEvaluationResult[];
  isLoading: boolean;
  error: string | null;
}

const ComplianceRulesContext = createContext<ComplianceRulesContextType | undefined>(undefined);

export const ComplianceRulesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [dpdpRules, setDpdpRules] = useState<DPDPRule[]>([]);
  const [gdprRules, setGdprRules] = useState<GDPRRule[]>([]);
  const [dpdpEnabled, setDpdpEnabled] = useState(false);
  const [gdprEnabled, setGdprEnabled] = useState(true); // GDPR enabled by default
  const [complianceHistory, setComplianceHistory] = useState<RuleEvaluationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load rules and settings on component mount
  useEffect(() => {
    const loadRulesAndSettings = async () => {
      setIsLoading(true);
      try {
        // Load rules
        const dpdpRules = complianceRulesService.getDPDPRules();
        const gdprRules = complianceRulesService.getGDPRRules();

        setDpdpRules(dpdpRules);
        setGdprRules(gdprRules);

        // Try to load settings from configuration service
        try {
          const settings = await configurationService.getSettings();
          if (settings && settings.compliance) {
            setGdprEnabled(settings.compliance.gdprEnabled);
            setDpdpEnabled(settings.compliance.dpdpEnabled);
          }
        } catch (settingsErr) {
          console.warn('Could not load compliance settings, using defaults:', settingsErr);
        }

        setError(null);
      } catch (err) {
        setError('Failed to load compliance rules');
        console.error('Error loading compliance rules:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadRulesAndSettings();
  }, []);

  // Toggle DPDP compliance
  const toggleDPDP = async (enabled: boolean) => {
    setDpdpEnabled(enabled);

    // Update configuration settings if possible
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance.dpdpEnabled = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.warn('Could not update DPDP settings:', err);
    }
  };

  // Toggle GDPR compliance
  const toggleGDPR = async (enabled: boolean) => {
    setGdprEnabled(enabled);

    // Update configuration settings if possible
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance.gdprEnabled = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.warn('Could not update GDPR settings:', err);
    }
  };

  // Evaluate a compliance event
  const evaluateEvent = async (event: ComplianceEvent): Promise<RuleEvaluationResult[]> => {
    setIsLoading(true);
    try {
      // Only evaluate rules for enabled compliance frameworks
      if (!dpdpEnabled && !gdprEnabled) {
        return [];
      }

      // Evaluate the event against applicable rules
      const results = complianceRulesService.evaluateComplianceEvent(event);

      // Log each result
      for (const result of results) {
        await complianceRulesService.logComplianceResult(result);
      }

      // Add to history
      setComplianceHistory(prev => [...results, ...prev].slice(0, 100)); // Keep last 100 events

      return results;
    } catch (err) {
      setError('Failed to evaluate compliance event');
      console.error('Error evaluating compliance event:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: ComplianceRulesContextType = {
    dpdpRules,
    gdprRules,
    dpdpEnabled,
    gdprEnabled,
    toggleDPDP,
    toggleGDPR,
    evaluateEvent,
    complianceHistory,
    isLoading,
    error
  };

  return (
    <ComplianceRulesContext.Provider value={contextValue}>
      {children}
    </ComplianceRulesContext.Provider>
  );
};

export const useComplianceRules = () => {
  const context = useContext(ComplianceRulesContext);
  if (context === undefined) {
    throw new Error('useComplianceRules must be used within a ComplianceRulesProvider');
  }
  return context;
};
