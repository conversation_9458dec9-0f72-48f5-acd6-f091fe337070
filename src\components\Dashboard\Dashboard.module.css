.dashboardContainer {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 240px;
  background-color: #e8e8e8;
  padding: 16px 0;
  border-right: 1px solid #ddd;
}

.logo {
  padding: 0 20px;
  margin-bottom: 32px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.prae {
  color: #4F46E5;
}

.nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  color: #555;
  text-decoration: none;
  font-size: 13px;
  transition: all 0.2s;
}

.navItem:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.active {
  background-color: rgba(0, 0, 0, 0.03);
}

.mainContent {
  flex: 1;
  padding: 24px 32px;
}

.header {
  margin-bottom: 24px;
}

.header h1 {
  font-size: 14px;
  color: #666;
  font-weight: normal;
  margin-bottom: 4px;
}

.header h2 {
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.chartCard {
  background: white;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.lineChartContainer {
  height: 380px;
  position: relative;
}

.metricsContainer {
  display: flex;
  gap: 24px;
  margin-top: 32px;
  justify-content: flex-end;
}

.metric {
  text-align: center;
}

.metricValue {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.metricLabel {
  font-size: 12px;
  color: #666;
}

/* Custom styles for consent trends chart */
.chartContainer {
  width: 100%;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chartTitle {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.chart {
  position: relative;
  width: 100%;
  height: 400px;
  margin-left: 40px; /* Space for y-axis labels */
}

.gridContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 40px;
  bottom: 40px;
  border-bottom: 1px solid #ddd;
  border-left: 1px solid #ddd;
}

.hGridLine {
  position: absolute;
  left: 0;
  right: 0;
  border-top: 1px dashed #ddd;
}

.vGridLine {
  position: absolute;
  top: 0;
  bottom: 0;
  border-right: 1px dashed #ddd;
}

.yAxis {
  position: absolute;
  left: -40px;
  top: 0;
  bottom: 40px;
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.yLabel {
  text-align: right;
  padding-right: 10px;
  color: #666;
}

.xAxis {
  position: absolute;
  left: 0;
  right: 40px;
  bottom: -30px;
  display: flex;
  justify-content: space-between;
}

.xLabel {
  text-align: center;
  color: #666;
  font-weight: bold;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 50px;
}

.legendItem {
  display: flex;
  align-items: center;
}

.legendColor {
  width: 25px;
  height: 2px;
  margin-right: 5px;
}

/* Sophisticated Blue-Purple Dark Mode Styling */
:global(.dark) .dashboardContainer {
  background: linear-gradient(135deg, #0F1419 0%, #1A1F2E 25%, #1E2139 50%, #2A2D5A 100%);
}

:global(.dark) .sidebar {
  background: linear-gradient(180deg, #1A1F2E 0%, rgba(26, 31, 46, 0.98) 25%, rgba(37, 43, 66, 0.95) 100%);
  border-right: 1px solid #374151;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.4), 2px 0 15px rgba(15, 20, 25, 0.3);
}

:global(.dark) .logo {
  color: #f8fafc;
}

:global(.dark) .prae {
  color: #60A5FA; /* Strategic blue highlighting */
}

:global(.dark) .navItem {
  color: #E2E8F0; /* Light gray for navigation text */
  transition: all 0.3s ease;
}

:global(.dark) .navItem:hover {
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.15), rgba(26, 31, 46, 0.1), transparent);
  color: #FFFFFF;
  transform: translateX(4px);
}

:global(.dark) .active {
  background: linear-gradient(90deg, rgba(96, 165, 250, 0.25), rgba(26, 31, 46, 0.15), transparent);
  color: #FFFFFF;
  border-right: 2px solid #60A5FA;
}

:global(.dark) .mainContent {
  background: transparent;
}

:global(.dark) .header h1 {
  color: #94a3b8;
}

:global(.dark) .header h2 {
  color: #f8fafc;
}

:global(.dark) .chartCard {
  background: linear-gradient(135deg, #252B42 0%, rgba(37, 43, 66, 0.9) 50%, rgba(52, 64, 84, 0.8) 100%);
  border: 1px solid #374151;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(15, 20, 25, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

:global(.dark) .metricValue {
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

:global(.dark) .metricLabel {
  color: #E2E8F0;
}

:global(.dark) .chartContainer {
  background: linear-gradient(135deg, #252B42 0%, rgba(37, 43, 66, 0.9) 50%, rgba(26, 31, 46, 0.8) 100%);
  border: 1px solid #374151;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(15, 20, 25, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

:global(.dark) .chartTitle {
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

:global(.dark) .gridContainer {
  border-bottom: 1px solid #374151;
  border-left: 1px solid #374151;
}

:global(.dark) .hGridLine {
  border-top: 1px dashed #374151;
}

:global(.dark) .vGridLine {
  border-right: 1px dashed #374151;
}

:global(.dark) .yLabel,
:global(.dark) .xLabel {
  color: #E2E8F0;
}
