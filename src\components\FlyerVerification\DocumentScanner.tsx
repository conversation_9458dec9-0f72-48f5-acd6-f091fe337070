import React, { useState, useRef, useEffect } from 'react';
import { Upload, FileText, Shield, CheckCircle, XCircle, AlertTriangle, Loader, Globe, Scan } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useFlyerVerification } from '../../hooks/useFlyerVerification';
import type { TravelRestriction } from '../../services/flyerVerificationService';

interface ScanResult {
  documentType: 'passport' | 'visa' | 'unknown';
  isValid: boolean;
  confidence: number;
  extractedData: {
    fullName?: string;
    nationality?: string;
    documentNumber?: string;
    expiryDate?: string;
    issueDate?: string;
    placeOfBirth?: string;
    dateOfBirth?: string;
    gender?: string;
  };
  verificationStatus: 'approved' | 'denied' | 'pending' | 'error';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  biometricMatch?: number;
  databaseVerification?: {
    ukBorderControl: boolean;
    interpol: boolean;
    schengen: boolean;
  };
  processingSteps?: VerificationStep[];
  errors?: string[];
}

interface VerificationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processingTime?: number;
}

interface RestrictionCheckResult {
  isRestricted: boolean;
  restrictions: TravelRestriction[];
  riskLevel: string;
}

const DocumentScanner: React.FC = () => {
  const { travelRestrictions } = useFlyerVerification();
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [passportFile, setPassportFile] = useState<File | null>(null);
  const [visaFile, setVisaFile] = useState<File | null>(null);
  const [scanProgress, setScanProgress] = useState(0);
  const [verificationSteps, setVerificationSteps] = useState<VerificationStep[]>([]);
  const [travelRoute, setTravelRoute] = useState({ from: 'Dubai', to: 'United States' });
  const [passengerNationality, setPassengerNationality] = useState<string>('');
  const [restrictionCheck, setRestrictionCheck] = useState<RestrictionCheckResult | null>(null);
  const [isVerificationComplete, setIsVerificationComplete] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const passportInputRef = useRef<HTMLInputElement>(null);
  const visaInputRef = useRef<HTMLInputElement>(null);
  const passportUploadRef = useRef<HTMLInputElement>(null);
  const visaUploadRef = useRef<HTMLInputElement>(null);

  // Initialize verification steps on component mount
  useEffect(() => {
    const initialSteps: VerificationStep[] = [
      {
        id: 'passport-scan',
        title: 'Passport Document Scanning',
        description: 'OCR scanning of passport with 99.2% accuracy rate',
        status: 'pending'
      },
      {
        id: 'visa-scan',
        title: 'Visa Document Scanning',
        description: 'OCR scanning of visa document',
        status: 'pending'
      },
      {
        id: 'data-extraction',
        title: 'Data Extraction & Comparison',
        description: 'Extracting and comparing information from both documents',
        status: 'pending'
      },
      {
        id: 'final-validation',
        title: 'Final Validation',
        description: 'Completing two-document verification process',
        status: 'pending'
      }
    ];
    setVerificationSteps(initialSteps);
  }, []);

  // Document quality assessment based on file characteristics
  const calculateDocumentQuality = (file: File): number => {
    let quality = 70; // Base quality score

    // File size assessment (larger files generally indicate higher resolution)
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > 2) quality += 15; // High resolution
    else if (fileSizeMB > 0.5) quality += 10; // Medium resolution
    else quality -= 10; // Low resolution

    // File format assessment
    if (file.type === 'application/pdf') quality += 10; // PDF is preferred
    else if (file.type === 'image/png') quality += 5; // PNG is good
    else if (file.type === 'image/jpeg') quality += 0; // JPEG is acceptable
    else quality -= 15; // Other formats are poor

    // File name assessment (professional naming suggests better quality)
    if (file.name.toLowerCase().includes('passport') || file.name.toLowerCase().includes('visa')) {
      quality += 5;
    }

    return Math.min(100, Math.max(30, quality)); // Clamp between 30-100
  };

  // Deterministic verification algorithm
  const calculateVerificationScore = (
    nationality: string,
    destination: string,
    documentQuality: number,
    biometricScore: number,
    hasActiveRestrictions: boolean,
    restrictionType: string
  ) => {
    // Log the calculation for debugging
    console.log(`Calculating verification score for ${nationality} traveling to ${destination}`);

    // Base score from document quality (0-40 points)
    let score = documentQuality * 0.4;

    // Biometric score (0-30 points)
    score += biometricScore * 0.3;

    // Travel restrictions check (0-30 points, can be negative)
    if (hasActiveRestrictions) {
      if (restrictionType === 'banned') {
        score = 0; // Automatic fail for banned nationalities
      } else if (restrictionType === 'visa_required') {
        score += 20; // Reduced but still passing score
      } else if (restrictionType === 'quarantine') {
        score += 15; // Quarantine required but allowed
      } else if (restrictionType === 'health_check') {
        score += 25; // Health check required but allowed
      }
    } else {
      score += 30; // Full points for no restrictions
    }

    return Math.min(100, Math.max(0, score));
  };



  const simulateTwoDocumentVerification = async (passportFile: File, visaFile: File): Promise<ScanResult> => {
    console.log('Starting two-document verification:', passportFile.name, visaFile.name);

    // Update verification steps to processing
    setVerificationSteps(prev => prev.map(step => ({ ...step, status: 'processing' })));

    // Simulate two-document verification process (4 steps: 25%, 50%, 75%, 100%)
    const progressSteps = [
      { progress: 25, message: 'Comparing passport and visa data...', stepId: 'data-extraction' },
      { progress: 50, message: 'Validating document consistency...', stepId: 'data-extraction' },
      { progress: 75, message: 'Performing final validation checks...', stepId: 'final-validation' },
      { progress: 100, message: 'Completing two-document verification...', stepId: 'final-validation' }
    ];

    for (const step of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 1200));
      setScanProgress(step.progress);

      // Update step status
      setVerificationSteps(prev => prev.map(s =>
        s.id === step.stepId
          ? { ...s, status: step.progress === 100 ? 'completed' : 'processing' }
          : s.status === 'processing' ? { ...s, status: 'completed' } : s
      ));

      toast.loading(step.message, { id: 'scan-progress' });
    }

    toast.dismiss('scan-progress');

    // Calculate document quality scores for both files
    const passportQuality = calculateDocumentQuality(passportFile);
    const visaQuality = calculateDocumentQuality(visaFile);
    const averageQuality = (passportQuality + visaQuality) / 2;

    // Use provided nationality or simulate OCR extraction
    let nationality = passengerNationality.toUpperCase();

    // If no nationality provided, simulate OCR extraction based on realistic patterns
    if (!nationality) {
      const commonNationalities = ['INDIAN', 'BRITISH', 'AMERICAN', 'CANADIAN', 'AUSTRALIAN', 'GERMAN', 'FRENCH'];
      const restrictedNationalities = ['IRANIAN', 'SYRIAN', 'NORTH KOREAN', 'AFGHAN', 'SOMALI'];

      // 85% chance of common nationality, 15% chance of restricted
      if (Math.random() < 0.85) {
        nationality = commonNationalities[Math.floor(Math.random() * commonNationalities.length)];
      } else {
        nationality = restrictedNationalities[Math.floor(Math.random() * restrictedNationalities.length)];
      }
    }

    // Check travel restrictions using the actual restrictions database
    let travelRestrictionCheck: RestrictionCheckResult = {
      isRestricted: false,
      restrictions: [],
      riskLevel: 'low'
    };

    try {
      // Check against actual travel restrictions
      const applicableRestrictions = travelRestrictions.filter(restriction =>
        restriction.nationality.toLowerCase() === nationality.toLowerCase() &&
        restriction.country.toLowerCase() === travelRoute.to.toLowerCase() &&
        restriction.isActive
      );

      const bannedRestrictions = applicableRestrictions.filter(r => r.restrictionType === 'banned');
      const hasActiveBan = bannedRestrictions.length > 0;

      travelRestrictionCheck = {
        isRestricted: hasActiveBan,
        restrictions: applicableRestrictions,
        riskLevel: hasActiveBan ? 'critical' : applicableRestrictions.length > 0 ? 'medium' : 'low'
      };
    } catch (error) {
      console.error('Error checking travel restrictions:', error);
    }

    setRestrictionCheck(travelRestrictionCheck);

    // Simulate document comparison logic
    const documentsMatch = Math.random() > 0.1; // 90% chance documents match

    // Calculate overall verification score using average quality
    const restrictionType = travelRestrictionCheck.restrictions.length > 0
      ? travelRestrictionCheck.restrictions[0].restrictionType
      : 'none';

    const verificationScore = calculateVerificationScore(
      nationality,
      travelRoute.to,
      averageQuality,
      85, // Remove biometric score, use fixed value
      travelRestrictionCheck.isRestricted,
      restrictionType
    );

    // Determine verification outcome - requires both documents to match AND pass restrictions
    const shouldFailDueToRestrictions = travelRestrictionCheck.isRestricted && restrictionType === 'banned';
    const shouldFailDueToMismatch = !documentsMatch;
    const shouldFail = shouldFailDueToRestrictions || shouldFailDueToMismatch || verificationScore < 70;

    // Generate appropriate names and details based on nationality
    const getPassengerDetails = (nat: string): { name: string; birthPlace: string } => {
      const details: Record<string, { name: string; birthPlace: string }> = {
        'INDIAN': { name: 'RAJESH KUMAR PATEL', birthPlace: 'MUMBAI, INDIA' },
        'IRANIAN': { name: 'MOHAMMAD ALI HOSSEINI', birthPlace: 'TEHRAN, IRAN' },
        'SYRIAN': { name: 'AHMED HASSAN AL-RASHID', birthPlace: 'DAMASCUS, SYRIA' },
        'NORTH KOREAN': { name: 'KIM JONG CHOL', birthPlace: 'PYONGYANG, DPRK' },
        'BRITISH': { name: 'JAMES WILLIAM SMITH', birthPlace: 'LONDON, UK' },
        'AMERICAN': { name: 'MICHAEL ROBERT JOHNSON', birthPlace: 'NEW YORK, USA' },
        'GERMAN': { name: 'HANS MUELLER', birthPlace: 'BERLIN, GERMANY' },
        'AFGHAN': { name: 'AHMAD SHAH AHMADI', birthPlace: 'KABUL, AFGHANISTAN' }
      };
      return details[nat] || { name: 'JOHN MICHAEL SMITH', birthPlace: 'LONDON, UK' };
    };

    const passengerDetails = getPassengerDetails(nationality);

    // Calculate OCR confidence based on document quality and verification score
    const ocrConfidence = Math.min(99.2, averageQuality + (verificationScore * 0.2));

    // Generate specific restriction reasons
    const getRestrictionReason = (nat: string, dest: string): string[] => {
      if (nat === 'IRANIAN' && dest === 'United States') {
        return [
          'Executive Order 13769 - Iranian nationals prohibited from US entry',
          'Nationality-based travel restriction in effect',
          'Contact Iranian Interests Section for assistance'
        ];
      } else if (nat === 'SYRIAN' && dest === 'United States') {
        return [
          'Presidential Proclamation 9645 - Syrian nationals banned from US travel',
          'Security concerns - enhanced screening required',
          'Contact Syrian embassy for guidance'
        ];
      } else if (nat === 'NORTH KOREAN' && dest === 'United States') {
        return [
          'Diplomatic restrictions - North Korean nationals prohibited',
          'No diplomatic relations - travel not permitted',
          'Contact State Department for information'
        ];
      }
      return [
        `Travel restrictions apply to ${nat} nationals`,
        'Manual review required',
        'Contact appropriate embassy'
      ];
    };

    const mockResult: ScanResult = {
      documentType: 'passport', // Two-document verification
      isValid: !shouldFail,
      confidence: ocrConfidence,
      extractedData: {
        fullName: shouldFail && verificationScore < 50 ? undefined : passengerDetails.name,
        nationality: shouldFail && verificationScore < 50 ? undefined : nationality,
        documentNumber: shouldFail && verificationScore < 50 ? undefined : `P${Math.random().toString().substring(2, 10)}`,
        expiryDate: shouldFail && verificationScore < 50 ? undefined : '2029-12-15',
        issueDate: shouldFail && verificationScore < 50 ? undefined : '2019-12-15',
        placeOfBirth: shouldFail && verificationScore < 50 ? undefined : passengerDetails.birthPlace,
        dateOfBirth: shouldFail && verificationScore < 50 ? undefined : '1985-03-15',
        gender: shouldFail && verificationScore < 50 ? undefined : 'M'
      },
      verificationStatus: shouldFail ? 'denied' : 'approved',
      riskLevel: shouldFailDueToRestrictions ? 'critical' :
        shouldFailDueToMismatch ? 'high' :
          verificationScore < 70 ? 'high' :
            verificationScore < 85 ? 'medium' : 'low',
      databaseVerification: {
        ukBorderControl: !shouldFailDueToRestrictions,
        interpol: !shouldFailDueToRestrictions,
        schengen: !shouldFailDueToRestrictions
      },
      processingSteps: verificationSteps.map(s => ({
        ...s,
        status: shouldFail && s.id === 'database-check' ? 'failed' : 'completed'
      })),
      errors: shouldFail ?
        shouldFailDueToRestrictions ?
          getRestrictionReason(nationality, travelRoute.to) :
          shouldFailDueToMismatch ?
            ['Document mismatch detected', 'Passport and visa information do not match', 'Manual review required'] :
            verificationScore < 50 ?
              ['Document quality insufficient for verification', 'OCR confidence below threshold', 'Manual review required'] :
              ['Two-document verification failed', 'Manual review required']
        : undefined
    };

    // Mark all steps as completed
    setVerificationSteps(prev => prev.map(s => ({
      ...s,
      status: shouldFail && s.id === 'biometric-capture' ? 'failed' : 'completed'
    })));

    return mockResult;
  };

  const validateFile = (file: File): boolean => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image (JPG, PNG) or PDF file');
      return false;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return false;
    }

    return true;
  };

  const handlePassportUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !validateFile(file)) return;

    setPassportFile(file);
    setIsVerificationComplete(false); // Reset verification completion state
    toast.success('Passport uploaded successfully!');

    // Update passport scan step
    setVerificationSteps(prev => prev.map(step =>
      step.id === 'passport-scan' ? { ...step, status: 'completed' } : step
    ));
  };

  const handleVisaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !validateFile(file)) return;

    if (!passportFile) {
      toast.error('Please upload passport first before uploading visa');
      return;
    }

    setVisaFile(file);
    setIsVerificationComplete(false); // Reset verification completion to re-enable "Verified" button
    toast.success('Visa uploaded successfully!');

    // Update visa scan step
    setVerificationSteps(prev => prev.map(step =>
      step.id === 'visa-scan' ? { ...step, status: 'completed' } : step
    ));
  };

  const handleVerificationStart = async () => {
    if (!passportFile || !visaFile) {
      toast.error('Please upload both passport and visa documents before verification');
      return;
    }

    // Reset only verification-specific states, preserve uploaded files
    resetVerificationOnly();

    setIsScanning(true);
    setScanProgress(0);

    toast.loading('Starting two-document verification...', { id: 'scan-progress' });

    try {
      const result = await simulateTwoDocumentVerification(passportFile, visaFile);
      setScanResult(result);
      setShowResults(true);
      setIsVerificationComplete(true); // Mark verification as complete to reset button states

      if (result.verificationStatus === 'approved') {
        toast.success('Two-document verification completed successfully!');
      } else {
        toast.error('Two-document verification failed');
      }

    } catch (error) {
      toast.error('Verification failed. Please try again.');
      console.error('Verification error:', error);
    } finally {
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const handlePassportScan = () => {
    passportInputRef.current?.click();
  };

  const handlePassportUploadClick = () => {
    passportUploadRef.current?.click();
  };

  const handleVisaScan = () => {
    if (!passportFile) {
      toast.error('Please upload passport first before uploading visa');
      return;
    }
    visaInputRef.current?.click();
  };

  const handleVisaUploadClick = () => {
    if (!passportFile) {
      toast.error('Please upload passport first before uploading visa');
      return;
    }
    visaUploadRef.current?.click();
  };





  const resetVerificationOnly = () => {
    // Only reset verification-specific states, preserve uploaded files
    setScanResult(null);
    setScanProgress(0);
    setIsVerificationComplete(false);
    setShowResults(false);
    setRestrictionCheck(null);

    // Reset verification steps to initial state but keep file uploads
    const initialSteps: VerificationStep[] = [
      {
        id: 'passport-scan',
        title: 'Passport Document Scanning',
        description: 'OCR scanning of passport with 99.2% accuracy rate',
        status: passportFile ? 'completed' : 'pending'
      },
      {
        id: 'visa-scan',
        title: 'Visa Document Scanning',
        description: 'OCR scanning of visa document',
        status: visaFile ? 'completed' : 'pending'
      },
      {
        id: 'data-extraction',
        title: 'Data Extraction & Comparison',
        description: 'Extracting and comparing information from both documents',
        status: 'pending'
      },
      {
        id: 'final-validation',
        title: 'Final Validation',
        description: 'Completing two-document verification process',
        status: 'pending'
      }
    ];
    setVerificationSteps(initialSteps);
  };

  const getStepIcon = (step: VerificationStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-success" />;
      case 'processing':
        return <Loader className="w-6 h-6 text-primary animate-spin" />;
      case 'failed':
        return <XCircle className="w-6 h-6 text-error" />;
      default:
        return (
          <div className="w-6 h-6 rounded-full border-2 border-border bg-surface flex items-center justify-center">
            <div className="w-2 h-2 rounded-full bg-text-secondary/50"></div>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Identity Verification Branding */}
      <div className="bg-card rounded-lg p-6 border border-border">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {/* Identity Verification System Logo */}
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-text">Identity Verification System</h2>
              <p className="text-text-secondary text-lg">Advanced Two-Document Scanner & Verification</p>
              <p className="text-xs text-text-secondary mt-1">OCR Technology • 99.2% Accuracy • Passport + Visa Verification</p>
            </div>
          </div>
          <div className="text-right">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-700/30">
              <p className="text-sm font-medium text-text">Travel Route</p>
              <p className="text-lg font-bold text-primary">{travelRoute.from} → {travelRoute.to}</p>
            </div>
          </div>
        </div>

        {/* Verification Configuration */}
        <div className="mb-6 p-4 bg-surface rounded-lg border border-border">
          <h3 className="text-sm font-medium text-text mb-3 flex items-center">
            <Globe className="w-4 h-4 mr-2" />
            Verification Configuration
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-xs font-medium text-text-secondary mb-1">Passenger Nationality</label>
              <select
                value={passengerNationality}
                onChange={(e) => setPassengerNationality(e.target.value)}
                className="w-full px-3 py-2 text-sm border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-card text-text"
              >
                <option value="">Auto-detect from document</option>
                <option value="Indian">Indian</option>
                <option value="British">British</option>
                <option value="American">American</option>
                <option value="Canadian">Canadian</option>
                <option value="Australian">Australian</option>
                <option value="German">German</option>
                <option value="French">French</option>
                <option value="Iranian">Iranian</option>
                <option value="Syrian">Syrian</option>
                <option value="North Korean">North Korean</option>
                <option value="Afghan">Afghan</option>
                <option value="Somali">Somali</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-text-secondary mb-1">Departure</label>
              <select
                value={travelRoute.from}
                onChange={(e) => setTravelRoute(prev => ({ ...prev, from: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-card text-text"
              >
                <option value="Dubai">Dubai, UAE</option>
                <option value="London">London, UK</option>
                <option value="Frankfurt">Frankfurt, Germany</option>
                <option value="Paris">Paris, France</option>
                <option value="Mumbai">Mumbai, India</option>
                <option value="Tehran">Tehran, Iran</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-text-secondary mb-1">Destination</label>
              <select
                value={travelRoute.to}
                onChange={(e) => setTravelRoute(prev => ({ ...prev, to: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-card text-text"
              >
                <option value="United States">United States</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="Germany">Germany</option>
                <option value="France">France</option>
                <option value="Canada">Canada</option>
                <option value="Australia">Australia</option>
              </select>
            </div>
          </div>
          {passengerNationality && (
            <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                ℹ️ Nationality set to <strong>{passengerNationality}</strong>. Travel restrictions will be checked automatically during verification.
              </p>
            </div>
          )}
        </div>

        {/* Enhanced Two-Document Upload Area */}
        <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
          <div>
            <div className="w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="w-10 h-10 text-primary" />
            </div>
            <h3 className="text-xl font-medium text-text mb-3">Two-Document Upload Center</h3>
            <p className="text-text-secondary mb-6">
              Upload passport first, then visa for comprehensive verification
            </p>

            {/* Enhanced Document Type Buttons with Upload Options */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Passport Section */}
              <div className="flex flex-col space-y-2">
                <button
                  onClick={handlePassportScan}
                  className={`flex flex-col items-center p-4 bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 rounded-lg border-2 ${passportFile ? 'border-success dark:border-success' : 'border-primary/30 dark:border-primary/40'} hover:border-primary/50 dark:hover:border-primary/60 transition-all duration-200 group`}
                >
                  <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center mb-2 group-hover:scale-105 transition-transform">
                    <Shield className="w-5 h-5 text-white" />
                  </div>
                  <h4 className="font-semibold text-text text-sm">Scan Passport</h4>
                  {passportFile && !isVerificationComplete && <p className="text-xs text-green-600">✓ Uploaded</p>}
                  {passportFile && isVerificationComplete && <p className="text-xs text-blue-600">✓ Click to replace</p>}
                </button>
                <button
                  onClick={handlePassportUploadClick}
                  className={`flex flex-col items-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg border-2 ${passportFile ? 'border-green-300 dark:border-green-600' : 'border-blue-200 dark:border-blue-700/30'} hover:border-blue-300 dark:hover:border-blue-600/50 transition-all duration-200 group`}
                >
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-105 transition-transform">
                    <Upload className="w-5 h-5 text-white" />
                  </div>
                  <h4 className="font-semibold text-text text-sm">Upload Passport</h4>
                  {passportFile && !isVerificationComplete && <p className="text-xs text-green-600">✓ {passportFile.name}</p>}
                  {passportFile && isVerificationComplete && <p className="text-xs text-blue-600">✓ Click to replace</p>}
                </button>
              </div>

              {/* Visa Section */}
              <div className="flex flex-col space-y-2">
                <button
                  onClick={handleVisaScan}
                  disabled={!passportFile}
                  className={`flex flex-col items-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border-2 ${visaFile ? 'border-green-300 dark:border-green-600' : 'border-green-200 dark:border-green-700/30'} ${!passportFile ? 'opacity-50 cursor-not-allowed' : 'hover:border-green-300 dark:hover:border-green-600/50'} transition-all duration-200 group`}
                >
                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mb-2 group-hover:scale-105 transition-transform">
                    <FileText className="w-5 h-5 text-white" />
                  </div>
                  <h4 className="font-semibold text-text text-sm">Scan Visa</h4>
                  {visaFile && !isVerificationComplete && <p className="text-xs text-green-600">✓ Uploaded</p>}
                  {visaFile && isVerificationComplete && <p className="text-xs text-blue-600">✓ Click to replace</p>}
                </button>
                <button
                  onClick={handleVisaUploadClick}
                  disabled={!passportFile}
                  className={`flex flex-col items-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border-2 ${visaFile ? 'border-green-300 dark:border-green-600' : 'border-green-200 dark:border-green-700/30'} ${!passportFile ? 'opacity-50 cursor-not-allowed' : 'hover:border-green-300 dark:hover:border-green-600/50'} transition-all duration-200 group`}
                >
                  <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-105 transition-transform">
                    <Upload className="w-5 h-5 text-white" />
                  </div>
                  <h4 className="font-semibold text-text text-sm">Upload Visa</h4>
                  {visaFile && !isVerificationComplete && <p className="text-xs text-green-600">✓ {visaFile.name}</p>}
                  {visaFile && isVerificationComplete && <p className="text-xs text-blue-600">✓ Click to replace</p>}
                </button>
              </div>

              {/* Verification Section */}
              <div className="flex flex-col justify-center">
                <button
                  onClick={handleVerificationStart}
                  disabled={!passportFile || !visaFile || isScanning || isVerificationComplete}
                  className={`flex flex-col items-center p-6 bg-gradient-to-br ${passportFile && visaFile && !isScanning && !isVerificationComplete
                    ? 'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700/30 hover:border-green-300 dark:hover:border-green-600/50'
                    : 'from-secondary/5 to-secondary/10 dark:from-secondary/20 dark:to-secondary/30 border-secondary/20 dark:border-secondary/30 opacity-50 cursor-not-allowed'
                    } rounded-lg border-2 transition-all duration-200 group`}
                >
                  <div className={`w-12 h-12 ${passportFile && visaFile && !isScanning && !isVerificationComplete ? 'bg-green-500' : 'bg-secondary'
                    } rounded-lg flex items-center justify-center mb-3 group-hover:scale-105 transition-transform`}>
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-text mb-1">Verified</h4>
                  <p className="text-xs text-text-secondary text-center">
                    {isScanning
                      ? 'Verification in progress...'
                      : isVerificationComplete
                        ? 'Verification completed'
                        : passportFile && visaFile
                          ? 'Ready to verify both documents'
                          : 'Upload both documents first'
                    }
                  </p>
                  {passportFile && visaFile && !isScanning && !isVerificationComplete && (
                    <p className="text-xs text-green-600 mt-1 font-medium">
                      ✓ Click to start verification
                    </p>
                  )}
                  {isVerificationComplete && (
                    <p className="text-xs text-purple-600 mt-1 font-medium">
                      ✓ Upload new documents to verify again
                    </p>
                  )}
                </button>
              </div>
            </div>

            {/* Hidden File Inputs */}
            <input
              ref={passportInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handlePassportUpload}
              className="hidden"
            />
            <input
              ref={passportUploadRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handlePassportUpload}
              className="hidden"
            />
            <input
              ref={visaInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handleVisaUpload}
              className="hidden"
            />
            <input
              ref={visaUploadRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handleVisaUpload}
              className="hidden"
            />


            <div className="text-center">
              <p className="text-xs text-text-secondary">
                Supported formats: JPG, PNG, PDF (max 10MB) • Two-Document Verification • Real-time Processing
              </p>
              {passportFile && visaFile && !isVerificationComplete && (
                <p className="text-sm text-green-600 font-medium mt-2">
                  ✓ Both documents uploaded. Click "Verified" to start verification.
                </p>
              )}
              {isVerificationComplete && (
                <p className="text-sm text-blue-600 font-medium mt-2">
                  ✓ Verification completed. Upload new documents to verify again.
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Always Visible Verification Progress */}
      <div className="bg-card rounded-lg p-6 border border-border">
        <div className="flex items-center space-x-3 mb-6">
          {isScanning ? (
            <Loader className="w-6 h-6 text-primary animate-spin" />
          ) : (
            <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <Scan className="w-3 h-3 text-white" />
            </div>
          )}
          <div>
            <h3 className="text-lg font-medium text-text">
              {isScanning ? 'Advanced Document Verification' : 'Verification System Ready'}
            </h3>
            <p className="text-text-secondary">
              {isScanning ? 'Processing with 99.2% accuracy OCR technology' : 'Upload a document to begin verification process'}
            </p>
          </div>
        </div>

        <div className="w-full bg-surface rounded-full h-3 mb-6">
          <div
            className={`h-3 rounded-full transition-all duration-500 ${isScanning
              ? 'bg-gradient-to-r from-primary to-primary-light'
              : 'bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700'
              }`}
            style={{ width: `${scanProgress}%` }}
          ></div>
        </div>

        <div className="text-center mb-6">
          <p className={`text-2xl font-bold ${isScanning ? 'text-primary' : 'text-text-secondary'}`}>
            {scanProgress}%
          </p>
          <p className="text-text-secondary">
            {isScanning ? 'Verification Progress' : 'Ready to Begin'}
          </p>
        </div>

        {/* Always Visible Verification Steps */}
        {verificationSteps.length > 0 && (
          <div className="space-y-3">
            {verificationSteps.map((step) => (
              <div
                key={step.id}
                className={`flex items-center space-x-4 p-3 rounded-lg transition-all duration-300 ${step.status === 'processing' ? 'bg-primary/10 border border-primary/20' :
                  step.status === 'completed' ? 'bg-success/10 border border-success/20' :
                    step.status === 'failed' ? 'bg-error/10 border border-error/20' :
                      'bg-surface border border-border/50'
                  }`}
              >
                {getStepIcon(step)}
                <div className="flex-1">
                  <h4 className="font-medium text-text">{step.title}</h4>
                  <p className="text-sm text-text-secondary">{step.description}</p>
                </div>
                {step.status === 'processing' && (
                  <div className="text-xs text-primary font-medium">Processing...</div>
                )}
                {step.status === 'pending' && !isScanning && (
                  <div className="text-xs text-text-secondary font-medium">Ready</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>



      {/* Enhanced Scan Results */}
      {scanResult && !isScanning && showResults && (
        <div className="space-y-6">
          {/* Prominent Verification Status Banner */}
          <div className={`bg-card rounded-xl border-4 shadow-2xl ${scanResult.verificationStatus === 'approved'
            ? 'p-6 border-success bg-gradient-to-br from-success/15 via-success/10 to-success/5'
            : 'p-6 border-error bg-gradient-to-br from-error/15 via-error/10 to-error/5'
            }`}>
            <div className="text-center">
              <div className={`rounded-full flex items-center justify-center mx-auto shadow-lg ${scanResult.verificationStatus === 'approved'
                ? 'w-16 h-16 mb-4 bg-gradient-to-br from-success/30 to-success/20'
                : 'w-16 h-16 mb-4 bg-gradient-to-br from-error/30 to-error/20'
                }`}>
                {scanResult.verificationStatus === 'approved' ? (
                  <CheckCircle className="w-10 h-10 text-success drop-shadow-sm" />
                ) : (
                  <XCircle className="w-10 h-10 text-error drop-shadow-sm" />
                )}
              </div>
              <h1 className={`font-black tracking-wide ${scanResult.verificationStatus === 'approved'
                ? 'text-3xl mb-2 text-success'
                : 'text-2xl mb-2 text-error'
                }`}>
                VERIFICATION {scanResult.verificationStatus === 'approved' ? 'APPROVED' : 'DENIED'}
              </h1>
              <p className={`font-semibold text-text ${scanResult.verificationStatus === 'approved' ? 'text-lg mb-4' : 'text-lg mb-4'
                }`}>
                Two-Document Identity Verification Complete
              </p>
              <div className={`flex items-center justify-center text-base ${scanResult.verificationStatus === 'approved' ? 'space-x-6' : 'space-x-6'
                }`}>
                <div className="flex flex-col items-center">
                  <span className="font-medium text-text-secondary">OCR Confidence</span>
                  <span className={`font-bold text-primary ${scanResult.verificationStatus === 'approved' ? 'text-xl' : 'text-xl'
                    }`}>
                    {scanResult.confidence.toFixed(1)}%
                  </span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="font-medium text-text-secondary">Risk Level</span>
                  <span className={`font-bold ${scanResult.riskLevel === 'low' ? 'text-success' :
                    scanResult.riskLevel === 'medium' ? 'text-warning' : 'text-error'
                    } ${scanResult.verificationStatus === 'approved' ? 'text-xl' : 'text-xl'}`}>
                    {scanResult.riskLevel.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Verification Information */}
          <div className="bg-card rounded-lg p-6 border border-border">

            {/* Travel Restrictions Status - Commented out as requested */}
            {/* {restrictionCheck && (
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-text mb-3 flex items-center">
                  <Shield className="w-5 h-5 mr-2" />
                  Travel Restrictions Check
                </h4>
                <div className={`p-4 rounded-lg border ${
                  restrictionCheck.isRestricted
                    ? 'bg-error/10 border-error/20'
                    : 'bg-success/10 border-success/20'
                }`}>
                  <div className="flex items-center space-x-3 mb-2">
                    {restrictionCheck.isRestricted ? (
                      <XCircle className="w-6 h-6 text-error" />
                    ) : (
                      <CheckCircle className="w-6 h-6 text-success" />
                    )}
                    <div>
                      <p className={`font-semibold ${
                        restrictionCheck.isRestricted ? 'text-error' : 'text-success'
                      }`}>
                        {restrictionCheck.isRestricted ? 'TRAVEL RESTRICTED' : 'TRAVEL APPROVED'}
                      </p>
                      <p className="text-sm text-text-secondary">
                        Route: {travelRoute.from} → {travelRoute.to}
                      </p>
                    </div>
                  </div>
                  {restrictionCheck.isRestricted && restrictionCheck.restrictions.length > 0 && (
                    <div className="mt-3 p-3 bg-error/5 rounded border border-error/10">
                      <p className="text-sm font-medium text-error mb-1">Restriction Details:</p>
                      {restrictionCheck.restrictions.map((restriction: { description: string }, index: number) => (
                        <p key={index} className="text-sm text-error">
                          • {restriction.description}
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )} */}


          </div>

          {/* Extracted Document Information */}
          {scanResult.verificationStatus === 'approved' && scanResult.extractedData && (
            <div className="bg-card rounded-lg p-6 border border-border">
              <h4 className="text-lg font-semibold text-text mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Extracted Document Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Full Name</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.fullName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Nationality</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.nationality}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Document Number</label>
                    <p className="text-text font-mono font-semibold">{scanResult.extractedData.documentNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Gender</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.gender}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Date of Birth</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.dateOfBirth}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Issue Date</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.issueDate}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Expiry Date</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.expiryDate}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">Place of Birth</label>
                    <p className="text-text font-semibold">{scanResult.extractedData.placeOfBirth}</p>
                  </div>
                </div>
              </div>
            </div>
          )}



          {/* Error Display */}
          {scanResult.errors && scanResult.errors.length > 0 && (
            <div className="bg-card rounded-lg p-6 border border-error/20">
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="w-6 h-6 text-error" />
                <h4 className="text-lg font-semibold text-error">Verification Errors</h4>
              </div>
              <div className="bg-error/10 rounded-lg p-4">
                <ul className="list-disc list-inside space-y-2">
                  {scanResult.errors.map((error, index) => (
                    <li key={index} className="text-error text-sm font-medium">{error}</li>
                  ))}
                </ul>
              </div>
              <div className="mt-4 p-3 bg-warning/10 rounded-lg border border-warning/20">
                <p className="text-warning text-sm font-medium">
                  ⚠️ Manual review required. Please contact security personnel for assistance.
                </p>
              </div>
            </div>
          )}

          {/* Verification Summary */}
          <div className={`bg-card rounded-lg p-6 border ${scanResult.verificationStatus === 'approved'
            ? 'border-success/20'
            : 'border-error/20'
            }`}>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${scanResult.verificationStatus === 'approved'
                ? 'bg-success/10'
                : 'bg-error/10'
                }`}>
                {scanResult.verificationStatus === 'approved' ? (
                  <CheckCircle className="w-8 h-8 text-success" />
                ) : (
                  <XCircle className="w-8 h-8 text-error" />
                )}
              </div>

              <h4 className={`text-xl font-bold mb-2 ${scanResult.verificationStatus === 'approved' ? 'text-success' : 'text-error'
                }`}>
                {scanResult.verificationStatus === 'approved' ? 'Verification Complete!' : 'Verification Failed'}
              </h4>

              <p className="text-text-secondary mb-4">
                {scanResult.verificationStatus === 'approved'
                  ? 'Identity has been successfully verified. Passenger cleared for travel.'
                  : 'Verification could not be completed. Manual review required.'
                }
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-text">{scanResult.confidence.toFixed(1)}%</p>
                  <p className="text-xs text-text-secondary">OCR Confidence</p>
                </div>
                <div className="text-center">
                  <p className={`text-2xl font-bold ${scanResult.riskLevel === 'low' ? 'text-success' :
                    scanResult.riskLevel === 'medium' ? 'text-warning' :
                      'text-error'
                    }`}>
                    {scanResult.riskLevel.toUpperCase()}
                  </p>
                  <p className="text-xs text-text-secondary">Risk Level</p>
                </div>
              </div>

              {scanResult.verificationStatus === 'approved' && (
                <div className="flex items-center justify-center space-x-6 text-sm text-success">
                  <span className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>Passport Verified</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>Visa Verified</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>Travel Approved</span>
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Final Travel Decision */}
          <div className={`bg-card rounded-lg p-6 border-2 ${scanResult.verificationStatus === 'approved' && !restrictionCheck?.isRestricted
            ? 'border-success bg-success/5'
            : 'border-error bg-error/5'
            }`}>
            <div className="text-center">
              <h4 className="text-lg font-semibold text-text mb-2">Final Travel Decision</h4>
              <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-lg font-bold ${scanResult.verificationStatus === 'approved' && !restrictionCheck?.isRestricted
                ? 'bg-success text-white'
                : 'bg-error text-white'
                }`}>
                {scanResult.verificationStatus === 'approved' && !restrictionCheck?.isRestricted ? (
                  <>
                    <CheckCircle className="w-6 h-6" />
                    <span>TRAVEL APPROVED</span>
                  </>
                ) : (
                  <>
                    <XCircle className="w-6 h-6" />
                    <span>TRAVEL DENIED</span>
                  </>
                )}
              </div>
              <p className="text-text-secondary mt-2">
                Route: {travelRoute.from} → {travelRoute.to}
              </p>
              {scanResult.extractedData?.nationality && (
                <p className="text-text-secondary">
                  Passenger: {scanResult.extractedData.nationality} National
                </p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex justify-center gap-4">
              <button
                onClick={() => toast.success('Results saved to verification history')}
                className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors font-medium"
              >
                Save Results
              </button>
              <button
                onClick={() => toast.success('Verification report generated')}
                className="px-6 py-3 bg-secondary text-white rounded-lg hover:bg-secondary-hover transition-colors font-medium"
              >
                Generate Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentScanner;
