import { useState, useEffect, useMemo } from 'react';
import { useCompliance } from '../../context/ComplianceContext';
import { useTheme } from '../../context/ThemeContext';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js/auto';
import React, { Suspense, lazy } from 'react';
import { optimizedChartDefaults } from '../../utils/chartOptimizations';

import { SiteReliabilityGuardian } from '../SiteReliability/SiteReliabilityGuardian';
import { ServiceLevelObjectivesDashboard } from '../SLO/ServiceLevelObjectivesDashboard';
import { ServiceLevelObjectivesClassic } from '../SLO/ServiceLevelObjectivesClassic';
import { EnhancedComplianceMetrics } from '../compliance/EnhancedComplianceMetrics';
import RiskAssessmentDashboard from '../Risk/RiskAssessmentDashboard';
import { SecurityOverviewDashboard } from '../Security/SecurityOverviewDashboard';
import { RegulatoryFrameworkDashboard } from '../Regulatory/RegulatoryFrameworkDashboard';

// Import compliance dashboard components
import { ComplianceByPolicy } from '../ComplianceByPolicy';
import { ViolationsSummary } from '../ViolationsSummary';

// Lazy load compliance dashboard components
const Analytics = lazy(() => import('../Analytics'));
const PolicyDistributionChart = lazy(() => import('../PolicyDistributionChart'));

import {
  Shield,
  Target,
  Archive,
  BarChart3,
  AlertTriangle,
  Scale,
  Database,
  Download,
  CheckCircle,
  Clock,
  FileText,
  HelpCircle,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Eye
} from 'lucide-react';

// Loading fallback component for lazy-loaded components
const LoadingFallback = () => (
  <div className="animate-pulse bg-surface rounded-xl h-[400px] flex items-center justify-center">
    <div className="text-text-secondary">Loading...</div>
  </div>
);



// GDPR Data Types
interface DataSubjectRequest {
  id: string;
  type: 'erasure' | 'portability' | 'access' | 'rectification';
  email: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  submittedAt: Date;
  dueDate: Date;
  progress: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: string;
  description: string;
}

interface SecurityIncident {
  id: string;
  title: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedAt: Date;
  affectedUsers: number;
  status: 'detected' | 'investigating' | 'contained' | 'resolved';
  category: 'data_breach' | 'unauthorized_access' | 'malware' | 'phishing' | 'system_failure';
  description: string;
  assignee: string;
  estimatedResolution: Date;
}

interface ConsentCategory {
  id: string;
  name: string;
  description: string;
  totalUsers: number;
  consentedUsers: number;
  consentRate: number;
  trend: 'up' | 'down' | 'stable';
  weeklyChange: number;
  lastUpdated: Date;
}

interface ImpactAssessment {
  id: string;
  title: string;
  type: 'DPIA' | 'LIA' | 'TIA';
  status: 'draft' | 'review' | 'approved' | 'rejected';
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  completionPercentage: number;
  assignee: string;
  reviewer: string;
  createdAt: Date;
  dueDate: Date;
  mitigationActions: number;
  completedActions: number;
}

const EnterpriseDashboard = () => {
  console.log('🚀 EnterpriseDashboard component mounting/rendering');

  const { trendData, distribution, isLoading, metrics, policies, fetchData } = useCompliance();
  const { mode } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeTab, setActiveTab] = useState<'site_reliability' | 'slo' | 'slo_classic' | 'compliance_metrics' | 'risk_assessment' | 'security_overview' | 'regulatory_framework' | 'data_protection'>('data_protection');
  const [isComplianceLoading, setIsComplianceLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // GDPR dummy data state
  const [dataSubjectRequests] = useState<DataSubjectRequest[]>(generateDataSubjectRequests());
  const [securityIncidents] = useState<SecurityIncident[]>(generateSecurityIncidents());
  const [consentCategories] = useState<ConsentCategory[]>(generateConsentCategories());
  const [impactAssessments] = useState<ImpactAssessment[]>(generateImpactAssessments());

  console.log('🔍 Component state:', { activeTab, isLoading });

  // Generate dummy data for GDPR components
  const generateDataSubjectRequests = (): DataSubjectRequest[] => [
    {
      id: 'dsr-001',
      type: 'erasure',
      email: '<EMAIL>',
      status: 'in_progress',
      submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
      progress: 15,
      priority: 'urgent',
      assignee: 'Privacy Team',
      description: 'Request for complete data erasure under GDPR Article 17'
    },
    {
      id: 'dsr-002',
      type: 'portability',
      email: '<EMAIL>',
      status: 'in_progress',
      submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
      progress: 65,
      priority: 'medium',
      assignee: 'Data Team',
      description: 'Data portability request for customer profile and transaction history'
    },
    {
      id: 'dsr-003',
      type: 'access',
      email: '<EMAIL>',
      status: 'pending',
      submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 26 * 24 * 60 * 60 * 1000),
      progress: 0,
      priority: 'low',
      assignee: 'Support Team',
      description: 'Subject access request for personal data processing activities'
    },
    {
      id: 'dsr-004',
      type: 'rectification',
      email: '<EMAIL>',
      status: 'completed',
      submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
      progress: 100,
      priority: 'medium',
      assignee: 'Data Team',
      description: 'Request to correct inaccurate personal information'
    },
    {
      id: 'dsr-005',
      type: 'erasure',
      email: '<EMAIL>',
      status: 'pending',
      submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 24 * 24 * 60 * 60 * 1000),
      progress: 0,
      priority: 'high',
      assignee: 'Privacy Team',
      description: 'Right to be forgotten request for marketing data'
    }
  ];

  const generateSecurityIncidents = (): SecurityIncident[] => [
    {
      id: 'inc-001',
      title: 'Suspicious Login Activity Detected',
      severity: 'medium',
      detectedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      affectedUsers: 12,
      status: 'investigating',
      category: 'unauthorized_access',
      description: 'Multiple failed login attempts from unusual geographic locations',
      assignee: 'Security Operations',
      estimatedResolution: new Date(Date.now() + 6 * 60 * 60 * 1000)
    },
    {
      id: 'inc-002',
      title: 'Phishing Email Campaign',
      severity: 'high',
      detectedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
      affectedUsers: 247,
      status: 'contained',
      category: 'phishing',
      description: 'Targeted phishing emails sent to employees with malicious attachments',
      assignee: 'Incident Response Team',
      estimatedResolution: new Date(Date.now() + 2 * 60 * 60 * 1000)
    },
    {
      id: 'inc-003',
      title: 'Database Access Anomaly',
      severity: 'critical',
      detectedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
      affectedUsers: 1543,
      status: 'resolved',
      category: 'data_breach',
      description: 'Unauthorized access attempt to customer database detected and blocked',
      assignee: 'Security Team Lead',
      estimatedResolution: new Date(Date.now() - 2 * 60 * 60 * 1000)
    }
  ];

  const generateConsentCategories = (): ConsentCategory[] => [
    {
      id: 'consent-001',
      name: 'Marketing Communications',
      description: 'Email marketing, newsletters, and promotional content',
      totalUsers: 14720,
      consentedUsers: 12847,
      consentRate: 87.3,
      trend: 'up',
      weeklyChange: 3.2,
      lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 'consent-002',
      name: 'Analytics & Performance',
      description: 'Website analytics, performance tracking, and user behavior analysis',
      totalUsers: 14720,
      consentedUsers: 10621,
      consentRate: 72.1,
      trend: 'down',
      weeklyChange: -1.1,
      lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: 'consent-003',
      name: 'Essential Functions',
      description: 'Core platform functionality and security features',
      totalUsers: 14720,
      consentedUsers: 13956,
      consentRate: 94.8,
      trend: 'stable',
      weeklyChange: 0.8,
      lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 'consent-004',
      name: 'Third-party Integrations',
      description: 'Social media plugins, external analytics, and partner services',
      totalUsers: 14720,
      consentedUsers: 8932,
      consentRate: 60.7,
      trend: 'up',
      weeklyChange: 2.4,
      lastUpdated: new Date(Date.now() - 45 * 60 * 1000)
    }
  ];

  const generateImpactAssessments = (): ImpactAssessment[] => [
    {
      id: 'ia-001',
      title: 'Customer Data Processing DPIA',
      type: 'DPIA',
      status: 'review',
      riskScore: 7.2,
      riskLevel: 'medium',
      completionPercentage: 85,
      assignee: 'Privacy Officer',
      reviewer: 'Legal Counsel',
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      mitigationActions: 12,
      completedActions: 8
    },
    {
      id: 'ia-002',
      title: 'AI-Powered Analytics Implementation',
      type: 'DPIA',
      status: 'draft',
      riskScore: 8.9,
      riskLevel: 'high',
      completionPercentage: 45,
      assignee: 'Data Protection Team',
      reviewer: 'Chief Privacy Officer',
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
      mitigationActions: 18,
      completedActions: 6
    },
    {
      id: 'ia-003',
      title: 'Employee Monitoring System',
      type: 'LIA',
      status: 'approved',
      riskScore: 6.1,
      riskLevel: 'medium',
      completionPercentage: 100,
      assignee: 'HR Privacy Lead',
      reviewer: 'Legal Team',
      createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
      dueDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      mitigationActions: 8,
      completedActions: 8
    }
  ];

  // Compliance Dashboard Chart Data
  const lineChartData = {
    labels: ['Nov-24', 'Dec-24', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25', 'Jun-25'],
    datasets: [
      {
        label: 'Non-Compliant',
        data: [0.2, 1.8, 1.5, 1.0, 0.2, 1.8, 1.7, 2.8],
        borderColor: '#ef4444',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Pending Review',
        data: [1.8, 1.2, 0.9, 1.5, 2.0, 1.5, 0.5, 1.6],
        borderColor: '#f59e0b',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Compliant',
        data: [0.2, 0.5, 0.5, 0.5, 2.4, 1.6, 1.1, 0.9],
        borderColor: '#10b981',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
    ],
  };

  // Chart options for compliance trends
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Compliance Trends',
        align: 'center' as const,
        font: {
          size: 16,
          family: 'Arial, sans-serif',
          weight: 'normal' as const,
        },
        padding: {
          bottom: 30,
        },
        color: mode === 'dark' ? '#F8FAFC' : '#111827',
      },
    },
    scales: {
      y: {
        min: 0,
        max: 4,
        ticks: {
          stepSize: 1,
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 8,
        },
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          drawBorder: false,
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        border: {
          display: false,
        },
        title: {
          display: true,
          text: 'Consent Rate',
          font: {
            size: 12,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: { top: 0, bottom: 10 },
        },
      },
      x: {
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        ticks: {
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 5,
        },
        border: {
          display: false,
        },
      },
    },
  };

  // Debug logging for tab state changes
  useEffect(() => {
    console.log('🔍 Active tab changed to:', activeTab);
  }, [activeTab]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Compliance data loading effect
  useEffect(() => {
    let mounted = true;

    const loadComplianceData = async () => {
      if (!isInitialLoad) return;

      try {
        setIsComplianceLoading(true);
        await fetchData();
      } catch (error) {
        console.error('Error loading compliance data:', error);
      } finally {
        if (mounted) {
          setIsComplianceLoading(false);
          setIsInitialLoad(false);
        }
      }
    };

    loadComplianceData();
    return () => {
      mounted = false;
    };
  }, [fetchData, isInitialLoad]);

  const handleTabClick = (tabName: 'site_reliability' | 'slo' | 'slo_classic' | 'compliance_metrics' | 'risk_assessment' | 'security_overview' | 'regulatory_framework' | 'data_protection') => {
    console.log('🔍 Tab clicked:', tabName);
    setActiveTab(tabName);
  };



  const chartData = useMemo(() => {
    if (!trendData || !trendData.labels || trendData.labels.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }

    return trendData;
  }, [trendData]);

  const doughnutData = useMemo(() => {
    if (!distribution || !distribution.datasets || distribution.datasets.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }

    return distribution;
  }, [distribution]);



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  console.log('🔍 Chart data:', {
    hasChartData: !!chartData.datasets.length,
    hasDoughnutData: !!doughnutData.datasets.length,
    isLoading,
    mode
  });

  return (
    <div className="flex-1 p-8 bg-background min-h-screen">
      {/* Premium Enterprise Header with Advanced Branding */}
      <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-surface via-card to-surface'} rounded-2xl p-8 mb-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'} overflow-hidden`}>
        {/* Premium Background Pattern - Only in light mode */}
        {mode === 'light' && (
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20"></div>
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-primary/10 to-transparent"></div>
          </div>
        )}

        {/* Premium Content */}
        <div className="relative z-10">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <div className="relative">
                  <div className={`w-4 h-4 ${mode === 'dark' ? 'bg-text-secondary' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse shadow-lg shadow-primary/50'}`}></div>
                  {mode === 'light' && (
                    <div className="absolute inset-0 w-4 h-4 bg-primary rounded-full animate-ping opacity-20"></div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <h2 className={`${mode === 'dark' ? 'text-text-secondary' : 'text-primary'} text-sm font-bold tracking-wider uppercase`}>Enterprise Compliance Platform</h2>
                  <div className={`px-3 py-1 ${mode === 'dark' ? 'bg-card border border-border text-text-secondary' : 'bg-primary/20 text-primary border border-primary/30'} text-xs font-bold rounded-full`}>
                    Premium
                  </div>
                </div>
              </div>

              <h1 className={`text-5xl font-bold ${mode === 'dark' ? 'text-text' : 'bg-gradient-to-r from-text via-primary to-secondary bg-clip-text text-transparent'} mb-4 leading-tight`}>
                Compliance Command Center
              </h1>

              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-text-secondary" />
                  <span className="text-text-secondary">{formatDate(currentTime)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-text-secondary" />
                  <span className="text-text-secondary">{formatTime(currentTime)}</span>
                </div>
                <div className={`flex items-center gap-2 px-3 py-1 bg-surface border border-border rounded-lg`}>
                  <div className={`w-2 h-2 bg-text-secondary rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                  <span className="text-text-secondary font-semibold">Real-time Monitoring</span>
                </div>
              </div>
            </div>

            {/* Premium Metrics Dashboard */}
            <div className="flex items-center gap-8">
              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Overall Score</div>
                <div className="relative">
                  <div className={`text-4xl font-bold ${mode === 'dark' ? 'text-text' : 'text-primary'} mb-1`}>98.5%</div>
                  {mode === 'light' && (
                    <div className="absolute -inset-2 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg -z-10"></div>
                  )}
                </div>
                <div className={`flex items-center justify-center gap-1 text-xs text-text-secondary`}>
                  <TrendingUp className="w-3 h-3" />
                  <span>+2.3% this month</span>
                </div>
              </div>

              <div className="w-px h-16 bg-border"></div>

              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Active Frameworks</div>
                <div className="text-2xl font-bold text-text mb-1">12</div>
                <div className="text-xs text-text-secondary">Compliance Standards</div>
              </div>

              <div className="w-px h-16 bg-border"></div>

              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Risk Level</div>
                <div className="text-2xl font-bold text-text mb-1">Low</div>
                <div className="text-xs text-text-secondary">3 Open Issues</div>
              </div>
            </div>
          </div>

          {/* Premium Action Bar */}
          <div className="flex items-center justify-between pt-6 border-t border-border/50">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Users className="w-4 h-4" />
                <span>Enterprise License</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Shield className="w-4 h-4" />
                <span>SOC 2 Certified</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <CheckCircle className="w-4 h-4" />
                <span>ISO 27001 Compliant</span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button className={`flex items-center gap-2 px-4 py-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:shadow-lg hover:scale-105'}`}>
                <HelpCircle className="w-4 h-4" />
                <span className="text-sm font-semibold">Help Center</span>
              </button>
              <button className={`flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:shadow-lg hover:scale-105'}`}>
                <Download className="w-4 h-4" />
                <span className="text-sm font-semibold">Export Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`bg-surface rounded-xl ${mode === 'dark' ? '' : 'shadow-2xl'} border border-border overflow-hidden`}>
        {/* Premium Enterprise Navigation with Advanced Animations */}
        <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-r from-surface via-card to-surface'} border-b border-border ${mode === 'dark' ? '' : 'shadow-lg'} overflow-hidden`}>
          {/* Premium Navigation Background - Only in light mode */}
          {mode === 'light' && (
            <div className="absolute inset-0 opacity-3">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5"></div>
            </div>
          )}

          <div className="relative z-10 px-8 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h3 className="text-3xl font-bold text-text mb-3">
                  Compliance Operations Center
                </h3>
                <p className="text-text-secondary text-base">Advanced enterprise compliance monitoring and management platform</p>
              </div>

              <div className="flex items-center gap-6">
                <div className="flex items-center gap-3 px-4 py-3 bg-surface border border-border rounded-xl">
                  <div className="relative">
                    <div className={`w-3 h-3 bg-text-secondary rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    {mode === 'light' && (
                      <div className="absolute inset-0 w-3 h-3 bg-primary rounded-full animate-ping opacity-20"></div>
                    )}
                  </div>
                  <span className="text-sm font-bold text-text-secondary">All Systems Operational</span>
                </div>

                <div className="text-right">
                  <div className="text-xs text-text-secondary uppercase tracking-wider mb-1">Platform Status</div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-bold text-text-secondary">Enterprise Ready</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Navigation Grid with Advanced Interactions */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Premium GDPR & Data Protection - FLAGSHIP FEATURE */}
              <div className="space-y-6 lg:col-span-2">
                <div className="relative">
                  {/* Premium Header with Gradient Background - Only in light mode */}
                  {mode === 'light' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent rounded-2xl"></div>
                  )}
                  <div className="relative p-6 rounded-2xl border border-border">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative">
                        <div className="p-3 bg-surface rounded-xl">
                          <Database className="w-8 h-8 text-text-secondary" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-text-secondary rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-background">1</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-2xl font-bold text-text">
                          GDPR Data Protection Suite
                        </h4>
                        <p className="text-text-secondary">Advanced privacy management and compliance automation</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="px-4 py-2 bg-surface border border-border text-text-secondary text-sm font-bold rounded-full">
                          FLAGSHIP
                        </div>
                        <div className="px-3 py-1 bg-surface border border-border text-text-secondary text-xs font-bold rounded-full">
                          AI-POWERED
                        </div>
                      </div>
                    </div>

                    {/* Premium Feature Highlights */}
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Automated Data Mapping</div>
                          <div className="text-xs text-text-secondary">AI-powered discovery</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <Shield className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Breach Detection</div>
                          <div className="text-xs text-text-secondary">Real-time monitoring</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <Users className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Consent Management</div>
                          <div className="text-xs text-text-secondary">Granular controls</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <FileText className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Impact Assessments</div>
                          <div className="text-xs text-text-secondary">Automated PIAs</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Data Protection Tab Button */}
                <button
                  onClick={() => handleTabClick('data_protection')}
                  className={`group relative w-full p-6 rounded-lg text-left transition-all duration-300 ${activeTab === 'data_protection'
                    ? 'bg-surface border border-border text-text'
                    : 'bg-card border border-border text-text hover:bg-surface'
                    }`}
                >

                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-4">
                      <div className={`p-3 rounded-lg transition-all duration-300 ${activeTab === 'data_protection'
                        ? 'bg-text/10 text-text'
                        : 'bg-text/5 text-text group-hover:bg-text/10'
                      }`}>
                        <Database className="w-6 h-6" />
                      </div>

                      <div className="flex-1">
                        <h3 className="text-xl font-semibold mb-1 text-text">
                          GDPR Compliance Center
                        </h3>
                        <p className="text-text-secondary text-sm">
                          Enterprise-grade data protection with AI-powered automation, real-time monitoring, and comprehensive compliance reporting.
                        </p>
                      </div>

                      {activeTab === 'data_protection' && (
                        <div className="w-2 h-2 bg-text/40 rounded-full"></div>
                      )}
                    </div>

                    {/* Feature Tags */}
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        GDPR Article 30 Compliant
                      </span>
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        <TrendingUp className="w-3 h-3" />
                        Real-time Analytics
                      </span>
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        <Shield className="w-3 h-3" />
                        Enterprise Security
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              {/* Compliance & Risk Management */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-6">
                  <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                    <Scale className={`w-5 h-5 ${mode === 'dark' ? 'text-text-secondary' : 'text-secondary'}`} />
                  </div>
                  <div>
                    <h4 className="text-base font-bold text-text">Compliance & Risk</h4>
                    <p className="text-sm text-text-secondary">Regulatory oversight</p>
                  </div>
                </div>

                <button
                  onClick={() => handleTabClick('compliance_metrics')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'compliance_metrics'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'compliance_metrics'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <BarChart3 className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Compliance Metrics</span>
                      <span className="text-xs text-text-secondary">Interactive Analytics</span>
                    </div>
                    {activeTab === 'compliance_metrics' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('risk_assessment')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'risk_assessment'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'risk_assessment'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <AlertTriangle className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Risk Assessment</span>
                      <span className="text-xs text-text-secondary">Risk & Audit Trail</span>
                    </div>
                    {activeTab === 'risk_assessment' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('regulatory_framework')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'regulatory_framework'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'regulatory_framework'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <Scale className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Regulatory Framework</span>
                      <span className="text-xs text-text-secondary">Multi-Framework</span>
                    </div>
                    {activeTab === 'regulatory_framework' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>
              </div>

              {/* Security & Infrastructure */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-accent-purple/10 rounded-lg">
                    <Shield className="w-5 h-5 text-accent-purple" />
                  </div>
                  <div>
                    <h4 className="text-base font-bold text-text">Security & Infrastructure</h4>
                    <p className="text-sm text-text-secondary">Threat monitoring & reliability</p>
                  </div>
                </div>

                <button
                  onClick={() => handleTabClick('security_overview')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'security_overview'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-accent-purple/20 via-accent-purple/15 to-accent-purple/10 border-2 border-accent-purple/40 text-text shadow-xl shadow-accent-purple/20 ring-1 ring-accent-purple/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-accent-purple/40 hover:shadow-xl hover:shadow-accent-purple/10 hover:ring-1 hover:ring-accent-purple/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'security_overview'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-accent-purple/20 text-accent-purple'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-accent-purple/15 group-hover:text-accent-purple'
                    }`}>
                      <Shield className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Security Overview</span>
                      <span className="text-xs text-text-secondary">Threat Detection & Response</span>
                    </div>
                    {activeTab === 'security_overview' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-accent-purple'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('site_reliability')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'site_reliability'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'site_reliability'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Shield className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Site Reliability</span>
                      <span className="text-xs text-text-secondary">Guardian Dashboard</span>
                    </div>
                    {activeTab === 'site_reliability' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('slo')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'slo'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'slo'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Target className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">SLO Dashboard</span>
                      <span className="text-xs text-text-secondary">Service Level Objectives</span>
                    </div>
                    {activeTab === 'slo' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('slo_classic')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'slo_classic'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'slo_classic'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Archive className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">SLO Classic</span>
                      <span className="text-xs text-text-secondary">Legacy SLA Management</span>
                    </div>
                    {activeTab === 'slo_classic' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Professional GDPR Dashboard Content */}
        <div className="bg-background min-h-[700px] p-8 rounded-b-xl relative overflow-hidden">
          {/* Subtle background pattern for enterprise feel */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10"></div>
          </div>

          {/* Tab-Specific Content */}
          <div className="relative z-10">
            {activeTab === 'data_protection' && (
              <div className="space-y-8">
                {/* Premium GDPR Command Center Header */}
                <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'} overflow-hidden`}>
                  {/* Premium Background Effects - Only in light mode */}
                  {mode === 'light' && (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20"></div>
                      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-primary/15 to-transparent"></div>
                    </div>
                  )}

                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-8">
                      <div className="flex items-center gap-6">
                        <div className="relative">
                          <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/25 to-primary/15'} rounded-2xl ${mode === 'dark' ? '' : 'shadow-xl'}`}>
                            <Database className="w-10 h-10 text-primary" />
                          </div>
                          <div className={`absolute -top-2 -right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center ${mode === 'dark' ? '' : 'animate-bounce'}`}>
                            <CheckCircle className="w-4 h-4 text-white" />
                          </div>
                        </div>
                        <div>
                          <h2 className={`text-4xl font-bold ${mode === 'dark' ? 'text-text' : 'bg-gradient-to-r from-text via-primary to-secondary bg-clip-text text-transparent'} mb-2`}>
                            GDPR Command Center
                          </h2>
                          <p className="text-text-secondary text-lg">Enterprise-grade data protection and privacy compliance platform</p>
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="relative mb-4">
                          <div className="text-6xl font-bold text-primary">98.5%</div>
                          {mode === 'light' && (
                            <div className="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl -z-10"></div>
                          )}
                        </div>
                        <div className="text-text-secondary text-sm mb-2">Overall Compliance Score</div>
                        <div className="flex items-center justify-center gap-2 text-primary">
                          <TrendingUp className="w-4 h-4" />
                          <span className="text-sm font-semibold">+3.2% this quarter</span>
                        </div>
                      </div>
                    </div>

                    {/* Premium Action Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <button className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-br from-primary/15 to-primary/5 hover:from-primary/25 hover:to-primary/15 border border-primary/20 hover:border-primary/40'} rounded-2xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:-translate-y-1 hover:shadow-2xl hover:shadow-primary/20'}`}>
                        {mode === 'light' && (
                          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        )}
                        <div className="relative z-10">
                          <div className={`p-3 ${mode === 'dark' ? 'bg-text/10 group-hover:bg-text/15' : 'bg-primary/20 group-hover:bg-primary/30'} rounded-xl mb-4 transition-colors duration-300`}>
                            <Users className={`w-8 h-8 ${mode === 'dark' ? 'text-text' : 'text-primary'}`} />
                          </div>
                          <h3 className="text-lg font-bold text-text mb-2">Data Subject Requests</h3>
                          <p className="text-text-secondary text-sm mb-4">Automated processing of privacy requests with AI-powered validation</p>
                          <div className={`flex items-center gap-2 ${mode === 'dark' ? 'text-text' : 'text-primary'}`}>
                            <span className="text-xs font-semibold">{dataSubjectRequests.filter(r => r.status !== 'completed').length} Active Requests</span>
                            <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                          </div>
                        </div>
                      </button>

                      <button className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-br from-secondary/15 to-secondary/5 hover:from-secondary/25 hover:to-secondary/15 border border-secondary/20 hover:border-secondary/40'} rounded-2xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:-translate-y-1 hover:shadow-2xl hover:shadow-secondary/20'}`}>
                        {mode === 'light' && (
                          <div className="absolute inset-0 bg-gradient-to-br from-secondary/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        )}
                        <div className="relative z-10">
                          <div className={`p-3 ${mode === 'dark' ? 'bg-text/10 group-hover:bg-text/15' : 'bg-secondary/20 group-hover:bg-secondary/30'} rounded-xl mb-4 transition-colors duration-300`}>
                            <AlertTriangle className={`w-8 h-8 ${mode === 'dark' ? 'text-text' : 'text-secondary'}`} />
                          </div>
                          <h3 className="text-lg font-bold text-text mb-2">Breach Detection</h3>
                          <p className="text-text-secondary text-sm mb-4">Real-time monitoring with automated regulatory notification workflows</p>
                          <div className={`flex items-center gap-2 ${mode === 'dark' ? 'text-text' : 'text-secondary'}`}>
                            <span className="text-xs font-semibold">{securityIncidents.filter(i => i.status !== 'resolved').length} Active Incidents</span>
                            {securityIncidents.filter(i => i.status !== 'resolved').length === 0 ? (
                              <CheckCircle className="w-3 h-3" />
                            ) : (
                              <AlertTriangle className="w-3 h-3" />
                            )}
                          </div>
                        </div>
                      </button>

                      <button className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-br from-accent-purple/15 to-accent-purple/5 hover:from-accent-purple/25 hover:to-accent-purple/15 border border-accent-purple/20 hover:border-accent-purple/40'} rounded-2xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:-translate-y-1 hover:shadow-2xl hover:shadow-accent-purple/20'}`}>
                        {mode === 'light' && (
                          <div className="absolute inset-0 bg-gradient-to-br from-accent-purple/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        )}
                        <div className="relative z-10">
                          <div className={`p-3 ${mode === 'dark' ? 'bg-text/10 group-hover:bg-text/15' : 'bg-accent-purple/20 group-hover:bg-accent-purple/30'} rounded-xl mb-4 transition-colors duration-300`}>
                            <CheckCircle className={`w-8 h-8 ${mode === 'dark' ? 'text-text' : 'text-accent-purple'}`} />
                          </div>
                          <h3 className="text-lg font-bold text-text mb-2">Consent Center</h3>
                          <p className="text-text-secondary text-sm mb-4">Granular consent management with preference center integration</p>
                          <div className={`flex items-center gap-2 ${mode === 'dark' ? 'text-text' : 'text-accent-purple'}`}>
                            <span className="text-xs font-semibold">{(consentCategories.reduce((sum, cat) => sum + cat.consentRate, 0) / consentCategories.length).toFixed(1)}% Avg Consent Rate</span>
                            <TrendingUp className="w-3 h-3" />
                          </div>
                        </div>
                      </button>

                      <button className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-br from-primary/15 to-primary/5 hover:from-primary/25 hover:to-primary/15 border border-primary/20 hover:border-primary/40'} rounded-2xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:-translate-y-1 hover:shadow-2xl hover:shadow-primary/20'}`}>
                        {mode === 'light' && (
                          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        )}
                        <div className="relative z-10">
                          <div className={`p-3 ${mode === 'dark' ? 'bg-text/10 group-hover:bg-text/15' : 'bg-primary/20 group-hover:bg-primary/30'} rounded-xl mb-4 transition-colors duration-300`}>
                            <FileText className={`w-8 h-8 ${mode === 'dark' ? 'text-text' : 'text-primary'}`} />
                          </div>
                          <h3 className="text-lg font-bold text-text mb-2">Impact Assessments</h3>
                          <p className="text-text-secondary text-sm mb-4">Automated DPIA workflows with risk scoring and mitigation tracking</p>
                          <div className={`flex items-center gap-2 ${mode === 'dark' ? 'text-text' : 'text-primary'}`}>
                            <span className="text-xs font-semibold">{impactAssessments.filter(ia => ia.status !== 'approved').length} In Progress</span>
                            <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Premium Data Management Dashboard */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Advanced Data Subject Requests */}
                  <div className={`lg:col-span-2 ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'}`}>
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
                          <Users className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-text">Data Subject Requests</h3>
                          <p className="text-text-secondary">AI-powered request processing and validation</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className={`px-4 py-2 ${mode === 'dark' ? 'bg-card border border-border text-text' : 'bg-gradient-to-r from-primary/20 to-primary/10 text-primary border border-primary/30'} text-sm font-bold rounded-full`}>
                          {dataSubjectRequests.filter(r => r.status !== 'completed').length} Active
                        </span>
                        <button className={`p-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-110'}`}>
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {dataSubjectRequests.slice(0, 3).map((request) => {
                        const getRequestIcon = (type: string) => {
                          switch (type) {
                            case 'erasure': return <Users className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-primary'}`} />;
                            case 'portability': return <FileText className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-secondary'}`} />;
                            case 'access': return <Eye className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-accent-purple'}`} />;
                            case 'rectification': return <CheckCircle className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-primary'}`} />;
                            default: return <Users className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-primary'}`} />;
                          }
                        };

                        const getStatusColor = (status: string) => {
                          switch (status) {
                            case 'urgent': return mode === 'dark' ? 'bg-text/20 text-text' : 'bg-red-100 text-red-800';
                            case 'high': return mode === 'dark' ? 'bg-text/20 text-text' : 'bg-orange-100 text-orange-800';
                            case 'medium': return mode === 'dark' ? 'bg-text/20 text-text' : 'bg-yellow-100 text-yellow-800';
                            case 'low': return mode === 'dark' ? 'bg-text/20 text-text' : 'bg-green-100 text-green-800';
                            default: return mode === 'dark' ? 'bg-text/20 text-text' : 'bg-gray-100 text-gray-800';
                          }
                        };

                        const getProgressColor = (type: string) => {
                          switch (type) {
                            case 'erasure': return 'bg-primary';
                            case 'portability': return 'bg-secondary';
                            case 'access': return 'bg-accent-purple';
                            case 'rectification': return 'bg-primary';
                            default: return 'bg-primary';
                          }
                        };

                        const getButtonColor = (type: string) => {
                          switch (type) {
                            case 'erasure': return 'bg-primary hover:bg-primary-hover';
                            case 'portability': return 'bg-secondary hover:bg-secondary-hover';
                            case 'access': return 'bg-accent-purple hover:bg-accent-purple-hover';
                            case 'rectification': return 'bg-primary hover:bg-primary-hover';
                            default: return 'bg-primary hover:bg-primary-hover';
                          }
                        };

                        const timeAgo = (date: Date) => {
                          const now = new Date();
                          const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
                          if (diffInHours < 1) return 'Less than 1 hour ago';
                          if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
                          const diffInDays = Math.floor(diffInHours / 24);
                          return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
                        };

                        const daysUntilDue = (date: Date) => {
                          const now = new Date();
                          const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                          return diffInDays;
                        };

                        return (
                          <div key={request.id} className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-primary/30 hover:shadow-lg'} rounded-xl transition-all duration-300`}>
                            {mode === 'light' && (
                              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            )}
                            <div className="relative z-10 flex items-center gap-4">
                              <div className="relative">
                                <div className={`w-12 h-12 ${mode === 'dark' ? 'bg-text/10' : 'bg-gradient-to-br from-primary/25 to-primary/15'} rounded-xl flex items-center justify-center`}>
                                  {getRequestIcon(request.type)}
                                </div>
                                {request.priority === 'urgent' && (
                                  <div className={`absolute -top-1 -right-1 w-4 h-4 ${mode === 'dark' ? 'bg-text' : 'bg-red-500'} rounded-full flex items-center justify-center`}>
                                    <span className={`text-xs font-bold ${mode === 'dark' ? 'text-background' : 'text-white'}`}>!</span>
                                  </div>
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h4 className="text-lg font-bold text-text capitalize">{request.type.replace('_', ' ')} Request</h4>
                                  <span className={`px-3 py-1 ${getStatusColor(request.priority)} text-xs font-bold rounded-full uppercase`}>
                                    {request.priority}
                                  </span>
                                </div>
                                <p className="text-text-secondary mb-2">{request.email} • Submitted {timeAgo(request.submittedAt)}</p>
                                <div className="flex items-center gap-4">
                                  <div className="flex items-center gap-2">
                                    <Clock className="w-4 h-4 text-text-secondary" />
                                    <span className="text-sm text-text-secondary">Due in {daysUntilDue(request.dueDate)} days</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <div className="w-24 bg-border rounded-full h-2">
                                      <div className={`${getProgressColor(request.type)} h-2 rounded-full transition-all duration-500`} style={{width: `${request.progress}%`}}></div>
                                    </div>
                                    <span className={`text-sm font-semibold ${mode === 'dark' ? 'text-text' : 'text-primary'}`}>{request.progress}%</span>
                                  </div>
                                </div>
                              </div>
                              <button className={`px-4 py-2 ${getButtonColor(request.type)} text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105'}`}>
                                {request.status === 'pending' ? 'Start' : request.status === 'in_progress' ? 'Continue' : 'Review'}
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <button className={`w-full mt-6 px-6 py-3 ${mode === 'dark' ? 'bg-primary hover:bg-primary-hover' : 'bg-gradient-to-r from-primary to-primary-hover'} text-white rounded-xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:shadow-lg'} font-semibold`}>
                      View All Requests & Analytics
                    </button>
                  </div>

                  {/* Premium Consent Analytics */}
                  <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'}`}>
                    <div className="flex items-center gap-3 mb-6">
                      <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-accent-purple/20 to-accent-purple/10'} rounded-xl`}>
                        <CheckCircle className={`w-6 h-6 ${mode === 'dark' ? 'text-text' : 'text-accent-purple'}`} />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-text">Consent Analytics</h3>
                        <p className="text-text-secondary text-sm">Real-time consent tracking</p>
                      </div>
                    </div>

                    <div className="space-y-6">
                      {consentCategories.map((category, index) => {
                        const getBarColor = (index: number) => {
                          const colors = ['primary', 'secondary', 'accent-purple', 'primary'];
                          return colors[index % colors.length];
                        };

                        const getTrendIcon = (trend: string) => {
                          switch (trend) {
                            case 'up': return <TrendingUp className="w-3 h-3 text-white" />;
                            case 'down': return <TrendingDown className="w-3 h-3 text-white" />;
                            default: return <div className="w-3 h-3 bg-white rounded-full"></div>;
                          }
                        };

                        const barColor = getBarColor(index);
                        const textColor = mode === 'dark' ? 'text-text' : `text-${barColor}`;

                        return (
                          <div key={category.id} className="relative">
                            <div className="flex items-center justify-between mb-3">
                              <span className="text-sm font-semibold text-text">{category.name}</span>
                              <span className={`text-lg font-bold ${textColor}`}>{category.consentRate.toFixed(1)}%</span>
                            </div>
                            <div className="relative w-full bg-border rounded-full h-3">
                              <div
                                className={`absolute inset-0 ${mode === 'dark' ? `bg-${barColor}` : `bg-gradient-to-r from-${barColor} to-${barColor}-light`} h-3 rounded-full transition-all duration-1000`}
                                style={{width: `${category.consentRate}%`}}
                              ></div>
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                {getTrendIcon(category.trend)}
                              </div>
                            </div>
                            <div className="flex items-center justify-between mt-2 text-xs text-text-secondary">
                              <span>{category.consentedUsers.toLocaleString()} users</span>
                              <span className={category.weeklyChange >= 0 ? 'text-green-500' : 'text-red-500'}>
                                {category.weeklyChange >= 0 ? '+' : ''}{category.weeklyChange.toFixed(1)}% this week
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <button className={`w-full mt-6 px-6 py-3 ${mode === 'dark' ? 'bg-primary hover:bg-primary-hover' : 'bg-gradient-to-r from-accent-purple to-accent-purple-hover'} text-white rounded-xl transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105 hover:shadow-lg'} font-semibold`}>
                      Manage Consent Preferences
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'compliance_metrics' && (
              <div className="space-y-6">
                {/* Compliance Metrics Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-secondary/20 rounded-lg">
                        <BarChart3 className="w-6 h-6 text-secondary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Compliance Metrics & Analytics</h2>
                        <p className="text-text-secondary">Interactive dashboards and KPI tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                      Export Report
                    </button>
                  </div>
                </div>

                {/* Enhanced Compliance Metrics Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <EnhancedComplianceMetrics />
                </div>
              </div>
            )}

            {activeTab === 'risk_assessment' && (
              <div className="space-y-6">
                {/* Risk Assessment Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-accent-purple/20 rounded-lg">
                        <AlertTriangle className="w-6 h-6 text-accent-purple" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Risk Assessment & Management</h2>
                        <p className="text-text-secondary">Risk matrices, assessments, and mitigation tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-accent-purple hover:bg-accent-purple-hover text-white rounded-lg transition-colors">
                      <AlertTriangle className="w-4 h-4" />
                      New Assessment
                    </button>
                  </div>
                </div>

                {/* Risk Assessment Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <RiskAssessmentDashboard />
                </div>
              </div>
            )}

            {activeTab === 'regulatory_framework' && (
              <div className="space-y-6">
                {/* Regulatory Framework Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Scale className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Regulatory Framework Management</h2>
                        <p className="text-text-secondary">Multi-framework compliance and regulatory change tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <Scale className="w-4 h-4" />
                      Add Framework
                    </button>
                  </div>
                </div>

                {/* Regulatory Framework Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <RegulatoryFrameworkDashboard />
                </div>
              </div>
            )}

            {activeTab === 'security_overview' && (
              <div className="space-y-6">
                {/* Security Overview Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-accent-purple/20 rounded-lg">
                        <Shield className="w-6 h-6 text-accent-purple" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Security Overview & Threat Management</h2>
                        <p className="text-text-secondary">Threat detection, incident response, and security controls</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-accent-purple hover:bg-accent-purple-hover text-white rounded-lg transition-colors">
                      <Shield className="w-4 h-4" />
                      Security Scan
                    </button>
                  </div>
                </div>

                {/* Security Overview Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <SecurityOverviewDashboard />
                </div>
              </div>
            )}

            {activeTab === 'site_reliability' && (
              <div className="space-y-6">
                {/* Site Reliability Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Shield className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Site Reliability Guardian</h2>
                        <p className="text-text-secondary">Infrastructure monitoring and system health management</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <TrendingUp className="w-4 h-4" />
                      View Metrics
                    </button>
                  </div>
                </div>

                {/* Site Reliability Guardian Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <SiteReliabilityGuardian />
                </div>
              </div>
            )}

            {activeTab === 'slo' && (
              <div className="space-y-6">
                {/* SLO Dashboard Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Target className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Service Level Objectives Dashboard</h2>
                        <p className="text-text-secondary">Performance targets and availability metrics</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <Target className="w-4 h-4" />
                      New SLO
                    </button>
                  </div>
                </div>

                {/* SLO Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ServiceLevelObjectivesDashboard />
                </div>
              </div>
            )}

            {activeTab === 'slo_classic' && (
              <div className="space-y-6">
                {/* SLO Classic Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-secondary/20 rounded-lg">
                        <Archive className="w-6 h-6 text-secondary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">SLO Classic Management</h2>
                        <p className="text-text-secondary">Legacy SLA management and historical performance data</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-colors">
                      <Archive className="w-4 h-4" />
                      Legacy Reports
                    </button>
                  </div>
                </div>

                {/* SLO Classic Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ServiceLevelObjectivesClassic />
                </div>
              </div>
            )}
          </div>

          {/* Compliance Dashboard Section - Always Visible */}
          <div className="mt-12 space-y-8">
            {/* Compliance Overview Header */}
            <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'} overflow-hidden`}>
              {/* Premium Background Effects - Only in light mode */}
              {mode === 'light' && (
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute inset-0 bg-gradient-to-br from-secondary/20 via-transparent to-primary/20"></div>
                  <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-secondary/15 to-transparent"></div>
                </div>
              )}

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-secondary/20 to-secondary/10'} rounded-2xl ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                        <BarChart3 className="w-10 h-10 text-secondary" />
                      </div>
                      <div className={`absolute -top-2 -right-2 w-6 h-6 bg-secondary rounded-full flex items-center justify-center ${mode === 'dark' ? '' : 'animate-bounce'}`}>
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div>
                      <h2 className={`text-4xl font-bold ${mode === 'dark' ? 'text-text' : 'bg-gradient-to-r from-text via-secondary to-primary bg-clip-text text-transparent'} mb-2`}>
                        Compliance Overview
                      </h2>
                      <p className="text-text-secondary text-lg">Comprehensive compliance analytics and policy management platform</p>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="relative mb-4">
                      <div className="text-6xl font-bold text-secondary">{metrics?.compliantPercentage || 75}%</div>
                      {mode === 'light' && (
                        <div className="absolute -inset-4 bg-gradient-to-r from-secondary/20 to-primary/20 rounded-2xl -z-10"></div>
                      )}
                    </div>
                    <div className="text-text-secondary text-sm mb-2">Overall Compliance Score</div>
                    <div className="flex items-center justify-center gap-2 text-secondary">
                      <TrendingUp className="w-4 h-4" />
                      <span className="text-sm font-semibold">+2.3% this month</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Compliance Metrics Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}>
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Compliant</h3>
                    <p className="text-sm text-text-secondary">Total compliant policies</p>
                  </div>
                  <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/30 group-hover:bg-green-100 dark:group-hover:bg-green-900/50 transition-colors">
                    <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <p className="text-3xl font-bold text-green-500">{metrics?.compliantPercentage || 75}%</p>
                  <p className="text-sm text-text-secondary mb-1">of total policies</p>
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-green-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.compliantPercentage || 75}%` }}
                  />
                </div>
              </div>

              <div className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}>
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Non-Compliant</h3>
                    <p className="text-sm text-text-secondary">Policies requiring attention</p>
                  </div>
                  <div className="p-2 rounded-full bg-red-50 dark:bg-red-900/30 group-hover:bg-red-100 dark:group-hover:bg-red-900/50 transition-colors">
                    <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <p className="text-3xl font-bold text-red-500">{metrics?.nonCompliantPercentage || 15}%</p>
                  <p className="text-sm text-text-secondary mb-1">of total policies</p>
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-red-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.nonCompliantPercentage || 15}%` }}
                  />
                </div>
              </div>

              <div className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}>
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Pending Review</h3>
                    <p className="text-sm text-text-secondary">Awaiting assessment</p>
                  </div>
                  <div className="p-2 rounded-full bg-orange-50 dark:bg-orange-900/30 group-hover:bg-orange-100 dark:group-hover:bg-orange-900/50 transition-colors">
                    <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <p className="text-3xl font-bold text-orange-500">{metrics?.pendingPercentage || 10}%</p>
                  <p className="text-sm text-text-secondary mb-1">of total policies</p>
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-orange-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.pendingPercentage || 10}%` }}
                  />
                </div>
              </div>
            </div>

            {/* Policy Management and Violations Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                <ComplianceByPolicy />
              </div>
              <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                <ViolationsSummary />
              </div>
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-card rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
                <div style={{
                  height: '400px',
                  position: 'relative',
                  marginLeft: '40px',
                  marginRight: '20px'
                }}>
                  <Line
                    data={lineChartData}
                    options={chartOptions}
                  />
                </div>

                {/* Chart Legend */}
                <div className="flex justify-center gap-8 mt-6">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-green-500"></div>
                    <span className="text-[11px] text-text-secondary">Compliant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-red-500"></div>
                    <span className="text-[11px] text-text-secondary">Non-Compliant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-yellow-500"></div>
                    <span className="text-[11px] text-text-secondary">Pending Review</span>
                  </div>
                </div>
              </div>
              <div className="bg-card rounded-xl p-6 border border-border shadow-lg">
                <Suspense fallback={<LoadingFallback />}>
                  <PolicyDistributionChart />
                </Suspense>
              </div>
            </div>

            {/* Analytics Section */}
            <div className="bg-card rounded-lg shadow p-6">
              <Suspense fallback={<LoadingFallback />}>
                <Analytics />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseDashboard;
