import React, { useState, useEffect } from 'react';
import { X, Download, FileText, Eye, RefreshCw, CheckCircle } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';
import { toast } from 'react-toastify';
import greenLevelReportService, { GreenLevelReportData } from '../../services/greenLevelReportService';
import { ConsentLevelData } from '../../services/privacyDashboardService';

interface GreenLevelReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: ConsentLevelData;
}

const GreenLevelReportModal: React.FC<GreenLevelReportModalProps> = ({ isOpen, onClose, data }) => {
  const { mode } = useTheme();
  const [reportData, setReportData] = useState<GreenLevelReportData | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isExporting, setIsExporting] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'generating' | 'ready' | 'error'>('generating');

  useEffect(() => {
    if (isOpen) {
      generateReport();
    }
  }, [isOpen]);

  const generateReport = async () => {
    setIsGenerating(true);
    setError(null);
    setStep('generating');

    try {
      const report = await greenLevelReportService.generateReport(data);
      setReportData(report);
      setStep('ready');
      toast.success('Report generated successfully!');
    } catch (err) {
      setError('Failed to generate report');
      setStep('error');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    if (!reportData) return;

    setIsExporting(format);
    try {
      await greenLevelReportService.exportReport(reportData, format);
    } finally {
      setIsExporting(null);
    }
  };

  const handlePreview = async () => {
    if (!reportData) return;

    try {
      await greenLevelReportService.previewReport(reportData);
    } catch (err) {
      toast.error('Failed to preview report');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-2xl font-bold text-text">Green Level Detailed Report</h2>
            <p className="text-text-secondary">Comprehensive compliance analysis and insights</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-surface rounded-md transition-colors text-text-secondary hover:text-text"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'generating' && (
            <div className="flex flex-col items-center justify-center py-16">
              {/* Progress Circle */}
              <div className="relative w-24 h-24 mb-8">
                <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                    className="text-gray-200 dark:text-gray-700"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                    strokeDasharray="251.2"
                    strokeDashoffset="62.8"
                    className="text-primary transition-all duration-1000 ease-out"
                    style={{ color: 'var(--dashboard-green)' }}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <RefreshCw className="w-8 h-8 animate-spin" style={{ color: 'var(--dashboard-green)' }} />
                </div>
              </div>

              {/* Progress Steps */}
              <div className="w-full max-w-md mb-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: 'var(--dashboard-green)' }}></div>
                    <span className="text-sm font-medium text-text">Analyzing Data</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2 bg-gray-300 dark:bg-gray-600 animate-pulse"></div>
                    <span className="text-sm text-text-secondary">Generating Insights</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2 bg-gray-300 dark:bg-gray-600"></div>
                    <span className="text-sm text-text-secondary">Finalizing Report</span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-1000 ease-out"
                    style={{
                      backgroundColor: 'var(--dashboard-green)',
                      width: '33%'
                    }}
                  ></div>
                </div>
              </div>

              <h3 className="text-xl font-semibold text-text mb-3">Generating Comprehensive Report</h3>
              <p className="text-text-secondary text-center max-w-md leading-relaxed">
                Analyzing compliance data, calculating trends, and generating actionable insights for executive review...
              </p>
            </div>
          )}

          {step === 'error' && (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
                  <X className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-semibold text-text mb-2">Report Generation Failed</h3>
                <p className="text-text-secondary mb-6">{error}</p>
                <button
                  onClick={generateReport}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {step === 'ready' && reportData && (
            <div className="space-y-6">
              {/* Success Message */}
              <div className="flex items-center p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-200">Report Ready</h4>
                  <p className="text-green-600 dark:text-green-300 text-sm">
                    Your comprehensive Green Level compliance report has been generated successfully.
                  </p>
                </div>
              </div>

              {/* Report Summary */}
              <div className="bg-gradient-to-br from-surface to-surface/80 rounded-xl p-8 border border-border/50 shadow-lg">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
                       style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                    <CheckCircle className="w-6 h-6" style={{ color: 'var(--dashboard-green)' }} />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-text">Executive Summary</h3>
                    <p className="text-text-secondary">Key performance indicators and compliance metrics</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border border-border/30 hover:border-border/60 transition-all duration-300 hover:shadow-md">
                      <div className="absolute top-4 right-4">
                        <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--dashboard-green)' }}></div>
                      </div>
                      <div className="text-3xl font-bold mb-3" style={{ color: 'var(--dashboard-green)' }}>
                        {reportData.executiveSummary.complianceRate.toFixed(1)}%
                      </div>
                      <div className="text-text-secondary text-sm font-medium mb-2">Compliance Rate</div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{
                            backgroundColor: 'var(--dashboard-green)',
                            width: `${reportData.executiveSummary.complianceRate}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border border-border/30 hover:border-border/60 transition-all duration-300 hover:shadow-md">
                      <div className="absolute top-4 right-4">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      </div>
                      <div className="text-3xl font-bold text-text mb-3">
                        {reportData.executiveSummary.totalSubjects.toLocaleString()}
                      </div>
                      <div className="text-text-secondary text-sm font-medium">Total Subjects</div>
                      <div className="text-xs text-text-secondary mt-1">Active compliance records</div>
                    </div>
                  </div>

                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border border-border/30 hover:border-border/60 transition-all duration-300 hover:shadow-md">
                      <div className="absolute top-4 right-4">
                        <div className={`w-2 h-2 rounded-full ${
                          reportData.executiveSummary.riskLevel === 'Low' ? 'bg-green-500' :
                          reportData.executiveSummary.riskLevel === 'Medium' ? 'bg-yellow-500' : 'bg-red-500'
                        }`}></div>
                      </div>
                      <div className={`text-3xl font-bold mb-3 ${
                        reportData.executiveSummary.riskLevel === 'Low' ? 'text-green-600 dark:text-green-400' :
                        reportData.executiveSummary.riskLevel === 'Medium' ? 'text-yellow-600 dark:text-yellow-400' :
                        'text-red-600 dark:text-red-400'
                      }`}>
                        {reportData.executiveSummary.riskLevel}
                      </div>
                      <div className="text-text-secondary text-sm font-medium">Risk Level</div>
                      <div className="text-xs text-text-secondary mt-1">Overall assessment</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Report Sections Preview */}
              <div className="bg-surface rounded-lg p-6">
                <h3 className="text-lg font-semibold text-text mb-4">Report Contents</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-card rounded-lg">
                    <span className="text-text">Executive Summary</span>
                    <span className="text-text-secondary text-sm">Status, metrics, risk assessment</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-card rounded-lg">
                    <span className="text-text">Detailed Statistics</span>
                    <span className="text-text-secondary text-sm">Consent breakdown, performance metrics</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-card rounded-lg">
                    <span className="text-text">Recent Activity</span>
                    <span className="text-text-secondary text-sm">30-day activity summary</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-card rounded-lg">
                    <span className="text-text">Trends & Projections</span>
                    <span className="text-text-secondary text-sm">Growth analysis and forecasts</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-card rounded-lg">
                    <span className="text-text">Recommendations</span>
                    <span className="text-text-secondary text-sm">{reportData.recommendations.length} actionable insights</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-surface rounded-xl p-6 border border-border/50">
                <h4 className="text-lg font-semibold text-text mb-4">Export Options</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button
                    onClick={handlePreview}
                    className="group flex flex-col items-center p-4 border-2 border-border/30 rounded-xl hover:border-border/60 transition-all duration-300 hover:shadow-md bg-card"
                  >
                    <div className="w-12 h-12 rounded-xl bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200">
                      <Eye className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="font-medium text-text text-sm">Preview Report</span>
                    <span className="text-xs text-text-secondary mt-1">View in browser</span>
                  </button>

                  <button
                    onClick={() => handleExport('pdf')}
                    disabled={isExporting === 'pdf'}
                    className="group flex flex-col items-center p-4 border-2 rounded-xl transition-all duration-300 hover:shadow-md bg-card disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      borderColor: isExporting === 'pdf' ? 'var(--dashboard-green)' : 'rgba(229, 231, 235, 0.3)',
                      backgroundColor: isExporting === 'pdf' ? 'rgba(34, 197, 94, 0.05)' : ''
                    }}
                  >
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200"
                         style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                      {isExporting === 'pdf' ? (
                        <RefreshCw className="w-6 h-6 animate-spin" style={{ color: 'var(--dashboard-green)' }} />
                      ) : (
                        <Download className="w-6 h-6" style={{ color: 'var(--dashboard-green)' }} />
                      )}
                    </div>
                    <span className="font-medium text-text text-sm">Export PDF</span>
                    <span className="text-xs text-text-secondary mt-1">Executive format</span>
                  </button>

                  <button
                    onClick={() => handleExport('excel')}
                    disabled={isExporting === 'excel'}
                    className="group flex flex-col items-center p-4 border-2 border-border/30 rounded-xl hover:border-border/60 transition-all duration-300 hover:shadow-md bg-card disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="w-12 h-12 rounded-xl bg-green-50 dark:bg-green-900/20 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200">
                      {isExporting === 'excel' ? (
                        <RefreshCw className="w-6 h-6 text-green-600 dark:text-green-400 animate-spin" />
                      ) : (
                        <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
                      )}
                    </div>
                    <span className="font-medium text-text text-sm">Export Excel</span>
                    <span className="text-xs text-text-secondary mt-1">Data analysis</span>
                  </button>

                  <button
                    onClick={() => handleExport('csv')}
                    disabled={isExporting === 'csv'}
                    className="group flex flex-col items-center p-4 border-2 border-border/30 rounded-xl hover:border-border/60 transition-all duration-300 hover:shadow-md bg-card disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="w-12 h-12 rounded-xl bg-purple-50 dark:bg-purple-900/20 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200">
                      {isExporting === 'csv' ? (
                        <RefreshCw className="w-6 h-6 text-purple-600 dark:text-purple-400 animate-spin" />
                      ) : (
                        <Download className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                      )}
                    </div>
                    <span className="font-medium text-text text-sm">Export CSV</span>
                    <span className="text-xs text-text-secondary mt-1">Raw data</span>
                  </button>
                </div>
              </div>

              {/* Report Details */}
              <div className="bg-surface rounded-lg p-6">
                <h3 className="text-lg font-semibold text-text mb-4">Key Insights</h3>
                <div className="space-y-3">
                  {reportData.recommendations.slice(0, 3).map((rec, index) => (
                    <div key={index} className="flex items-start p-3 bg-card rounded-lg">
                      <div className={`w-2 h-2 rounded-full mt-2 mr-3 ${
                        rec.priority === 'High' ? 'bg-red-500' :
                        rec.priority === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`} />
                      <div>
                        <div className="font-medium text-text">{rec.category}</div>
                        <div className="text-text-secondary text-sm">{rec.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GreenLevelReportModal;
