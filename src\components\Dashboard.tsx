import React, { useState, useEffect } from 'react';
import { Line, Doughnut } from 'react-chartjs-2';
// Using chart.js/auto for automatic registration

// Using chart.js/auto should auto-register all components
// No manual registration needed

// Removed unused interface

const Dashboard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const chartId = React.useId();
  const [chartKey, setChartKey] = useState(0);
  const chartRef = React.useRef<any>(null);

  // Add state for current time
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Force chart re-render to ensure colors are applied
  useEffect(() => {
    const timeout = setTimeout(() => {
      setChartKey(prev => prev + 1);
    }, 100);
    return () => clearTimeout(timeout);
  }, []);

  // Debug chart after render
  useEffect(() => {
    const debugTimeout = setTimeout(() => {
      if (chartRef.current) {
        console.log('=== CHART DEBUG INFO ===');
        console.log('Chart instance:', chartRef.current);
        console.log('Chart data:', chartRef.current.data);
        console.log('Chart datasets:', chartRef.current.data?.datasets);
        console.log('Chart canvas:', chartRef.current.canvas);
        console.log('Chart context:', chartRef.current.ctx);

        // Try to inspect the actual rendered colors
        if (chartRef.current.data?.datasets) {
          chartRef.current.data.datasets.forEach((dataset: any, index: number) => {
            console.log(`Dataset ${index}:`, {
              label: dataset.label,
              borderColor: dataset.borderColor,
              backgroundColor: dataset.backgroundColor,
              pointBackgroundColor: dataset.pointBackgroundColor,
            });
          });
        }
      }
    }, 2000); // Wait 2 seconds for chart to fully render

    return () => clearTimeout(debugTimeout);
  }, [chartKey]);

  // Define colors explicitly to ensure they're applied - using multiple formats for compatibility
  const CHART_COLORS = {
    red: {
      rgb: 'rgb(239, 68, 68)',
      hex: '#EF4444',
      rgba: 'rgba(239, 68, 68, 1)'
    },
    amber: {
      rgb: 'rgb(245, 158, 11)',
      hex: '#F59E0B',
      rgba: 'rgba(245, 158, 11, 1)'
    },
    green: {
      rgb: 'rgb(16, 185, 129)',
      hex: '#10B981',
      rgba: 'rgba(16, 185, 129, 1)'
    }
  };

  // Debug: Log colors to console to verify they're being set
  console.log('Chart colors being applied:', CHART_COLORS);

  // Removed custom plugin - using direct color configuration instead

  // TESTING MULTIPLE COLOR FORMATS - one of these MUST work
  const lineChartData = {
    labels: ['Nov-23', 'Dec-23', 'Jan-24'],
    datasets: [
      {
        label: 'Red Level (Non-Compliant)',
        data: [0.2, 1.8, 2.8],
        borderColor: 'red', // Named CSS color
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
        pointBackgroundColor: 'red',
        pointBorderColor: 'white',
        borderWidth: 10, // VERY thick line
        pointRadius: 12,
        pointHoverRadius: 15,
        pointBorderWidth: 4,
        fill: false,
        tension: 0,
      },
      {
        label: 'Amber Level (Review Required)',
        data: [1.7, 0.8, 1.4],
        borderColor: 'orange', // Named CSS color
        backgroundColor: 'rgba(255, 165, 0, 0.1)',
        pointBackgroundColor: 'orange',
        pointBorderColor: 'white',
        borderWidth: 10, // VERY thick line
        pointRadius: 12,
        pointHoverRadius: 15,
        pointBorderWidth: 4,
        fill: false,
        tension: 0,
      },
      {
        label: 'Green Level (Compliant)',
        data: [0.2, 2.3, 0.8],
        borderColor: 'green', // Named CSS color
        backgroundColor: 'rgba(0, 255, 0, 0.1)',
        pointBackgroundColor: 'green',
        pointBorderColor: 'white',
        borderWidth: 10, // VERY thick line
        pointRadius: 12,
        pointHoverRadius: 15,
        pointBorderWidth: 4,
        fill: false,
        tension: 0,
      },
    ],
  };

  // Debug: Log the chart data to console
  console.log('TESTING NAMED COLORS - Chart data:', lineChartData);
  console.log('Dataset 0 borderColor:', lineChartData.datasets[0].borderColor);
  console.log('Dataset 1 borderColor:', lineChartData.datasets[1].borderColor);
  console.log('Dataset 2 borderColor:', lineChartData.datasets[2].borderColor);

  // Removed unused chartOptions - using inline configuration

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleDownload = () => {
    // Implement report download functionality
    console.log('Downloading report...');
  };

  if (isLoading) {
    return (
      <div className="flex-1 p-8 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const donutData = {
    labels: ['Green Level Data Consent', 'Amber Level Data Consent', 'Red Level Data Consent'],
    datasets: [
      {
        data: [31, 58, 11],
        backgroundColor: [
          '#4CAF50',  // Green
          '#FFA500',  // Amber
          '#FF4B4B',  // Red
        ],
        hoverBackgroundColor: [
          '#45a049',  // Darker Green
          '#f59e0b',  // Darker Amber
          '#e53e3e',  // Darker Red
        ],
        borderWidth: 0,
      },
    ],
  };

  const donutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label || '';
            const value = context.raw || 0;
            return `${label}: ${value}%`;
          },
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
      },
    },
    cutout: '75%',
    rotation: -90,
    circumference: 360,
  };

  return (
    <div className="flex-1 p-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-gray-600">Customer privacy management portal</h2>
          <h1 className="text-2xl font-bold">Enterprise Dashboard</h1>
        </div>
      </div>

      <div className="flex">
        {/* Chart Section */}
        <div className="w-2/3">
          <h3 className="text-center font-bold mb-4">Consent management trends</h3>
          <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="text-right text-sm text-gray-500 mb-2">
              {currentTime.toLocaleDateString()} {currentTime.toLocaleTimeString()}
            </div>
            <div
              style={{ height: '400px' }}
              className="chart-color-test"
            >
              <Line
                ref={chartRef}
                data={lineChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  // MINIMAL OPTIONS - no plugins that could interfere
                  onHover: (event: any, elements: any) => {
                    console.log('Chart hover event:', event, elements);
                  },
                  onClick: (event: any, elements: any) => {
                    console.log('Chart click event:', event, elements);
                    console.log('Chart instance:', chartRef.current);
                    if (chartRef.current) {
                      console.log('Chart data:', chartRef.current.data);
                      console.log('Chart datasets:', chartRef.current.data?.datasets);
                    }
                  },
                }}
                id={chartId}
                key={`chart-minimal-test-${chartKey}`} // Force re-render with new key
              />
            </div>
            <div className="flex justify-center gap-8 mt-4">
              <div className="flex items-center">
                <div
                  className="w-4 h-4 rounded mr-2 border border-white shadow-sm"
                  style={{ backgroundColor: 'red' }}
                ></div>
                <span className="text-sm text-gray-700 font-medium">Red Level (Non-Compliant)</span>
              </div>
              <div className="flex items-center">
                <div
                  className="w-4 h-4 rounded mr-2 border border-white shadow-sm"
                  style={{ backgroundColor: 'orange' }}
                ></div>
                <span className="text-sm text-gray-700 font-medium">Amber Level (Review Required)</span>
              </div>
              <div className="flex items-center">
                <div
                  className="w-4 h-4 rounded mr-2 border border-white shadow-sm"
                  style={{ backgroundColor: 'green' }}
                ></div>
                <span className="text-sm text-gray-700 font-medium">Green Level (Compliant)</span>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Section */}
        <div className="w-1/3 flex flex-col items-center">
          <div className="w-full mb-6">
            <div style={{ height: '200px' }}>
              <Doughnut 
                data={donutData} 
                options={donutOptions} 
                id={`${chartId}-donut`}
              />
            </div>
          </div>

          <StatCircle 
            percentage={31} 
            color="border-4 border-green-500" 
            label="Green Level" 
          />
          <StatCircle 
            percentage={58} 
            color="border-4 border-amber-500" 
            label="Amber Level" 
          />
          <StatCircle 
            percentage={11} 
            color="border-4 border-red-500" 
            label="Red Level" 
          />

          <div className="flex items-center mb-4">
            <div className="w-24 h-24 rounded-full bg-purple-200 hover:bg-purple-300 transition-colors flex items-center justify-center cursor-pointer">
              <div className="text-center">
                <p className="text-sm">Data Privacy</p>
                <p className="text-sm">Acknowledgement</p>
                <p className="text-2xl font-bold text-purple-700">100%</p>
              </div>
            </div>
          </div>

          <button 
            onClick={handleDownload}
            className="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
          >
            Download Report
          </button>
        </div>
      </div>
    </div>
  );
};

const StatCircle = ({ percentage, color, label }: { percentage: number; color: string; label: string }) => {
  return (
    <div className="flex items-center mb-4">
      <div className={`w-24 h-24 rounded-full ${color} bg-white flex items-center justify-center`}>
        <span className="text-3xl font-bold">{percentage}%</span>
      </div>
      <div className="ml-4">
        <p className="font-medium">{label}</p>
        <p className="text-sm text-gray-600">Data Consent</p>
      </div>
    </div>
  );
};

export default Dashboard;