import { useState, useEffect, useCallback } from 'react';

interface AnalyticsData {
  timePeriod: string;
  metrics: {
    compliant: number;
    nonCompliant: number;
    pending: number;
  };
}

interface AnalyticsOptions {
  initialPeriod?: 'week' | 'month' | 'quarter' | 'year';
  fetchOnMount?: boolean;
}

const defaultOptions: AnalyticsOptions = {
  initialPeriod: 'month',
  fetchOnMount: true,
};

export const useAnalytics = (options: AnalyticsOptions = defaultOptions) => {
  const { initialPeriod, fetchOnMount } = { ...defaultOptions, ...options };
  
  const [period, setPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>(initialPeriod || 'month');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<AnalyticsData[]>([]);

  // Mock data function that would be replaced with actual API calls
  const fetchAnalyticsData = useCallback(async (selectedPeriod: 'week' | 'month' | 'quarter' | 'year') => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Generate mock data based on selected period
      let mockData: AnalyticsData[] = [];
      
      switch (selectedPeriod) {
        case 'week':
          mockData = [
            { timePeriod: 'Mon', metrics: { compliant: 75, nonCompliant: 15, pending: 10 } },
            { timePeriod: 'Tue', metrics: { compliant: 72, nonCompliant: 18, pending: 10 } },
            { timePeriod: 'Wed', metrics: { compliant: 70, nonCompliant: 20, pending: 10 } },
            { timePeriod: 'Thu', metrics: { compliant: 68, nonCompliant: 22, pending: 10 } },
            { timePeriod: 'Fri', metrics: { compliant: 73, nonCompliant: 17, pending: 10 } },
            { timePeriod: 'Sat', metrics: { compliant: 78, nonCompliant: 12, pending: 10 } },
            { timePeriod: 'Sun', metrics: { compliant: 80, nonCompliant: 10, pending: 10 } },
          ];
          break;
        case 'month':
          mockData = [
            { timePeriod: 'Week 1', metrics: { compliant: 70, nonCompliant: 20, pending: 10 } },
            { timePeriod: 'Week 2', metrics: { compliant: 72, nonCompliant: 18, pending: 10 } },
            { timePeriod: 'Week 3', metrics: { compliant: 75, nonCompliant: 15, pending: 10 } },
            { timePeriod: 'Week 4', metrics: { compliant: 78, nonCompliant: 12, pending: 10 } },
          ];
          break;
        case 'quarter':
          mockData = [
            { timePeriod: 'Jan', metrics: { compliant: 65, nonCompliant: 25, pending: 10 } },
            { timePeriod: 'Feb', metrics: { compliant: 70, nonCompliant: 20, pending: 10 } },
            { timePeriod: 'Mar', metrics: { compliant: 75, nonCompliant: 15, pending: 10 } },
          ];
          break;
        case 'year':
          mockData = [
            { timePeriod: 'Q1', metrics: { compliant: 65, nonCompliant: 25, pending: 10 } },
            { timePeriod: 'Q2', metrics: { compliant: 70, nonCompliant: 20, pending: 10 } },
            { timePeriod: 'Q3', metrics: { compliant: 75, nonCompliant: 15, pending: 10 } },
            { timePeriod: 'Q4', metrics: { compliant: 80, nonCompliant: 10, pending: 10 } },
          ];
          break;
        default:
          mockData = [];
      }
      
      setData(mockData);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Change the time period
  const changePeriod = useCallback((newPeriod: 'week' | 'month' | 'quarter' | 'year') => {
    setPeriod(newPeriod);
    fetchAnalyticsData(newPeriod);
  }, [fetchAnalyticsData]);

  // Initial data fetch
  useEffect(() => {
    if (fetchOnMount) {
      fetchAnalyticsData(period);
    }
  }, [fetchOnMount, fetchAnalyticsData, period]);

  return {
    data,
    isLoading,
    error,
    period,
    changePeriod,
    refresh: () => fetchAnalyticsData(period),
  };
}; 