# Compliance Dashboard

A modern compliance monitoring and management dashboard built with React and TypeScript.

## Features

- Real-time compliance monitoring
- Policy management and tracking
- Compliance trends visualization
- Violation reporting and analytics
- Interactive data visualization

## Tech Stack

- React 18
- TypeScript
- TailwindCSS
- Chart.js
- React Router
- Context API

## Project Structure

```plaintext
src/
├── assets/        # Static files like images and global styles
├── components/    # Reusable UI components
├── config/        # Application configuration
├── context/       # React Context providers
├── hooks/         # Custom React hooks
├── services/      # API and external service integrations
├── types/         # TypeScript type definitions
├── utils/         # Helper functions and utilities
└── pages/         # Application pages/routes