import React, { useState, useRef, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import {
  Upload,
  FileText,
  File,
  Image,
  CheckCircle,
  XCircle,
  Alert<PERSON>riangle,
  Loader,
  Scan,
  Shield,
  Eye,
  Trash2,
  Download,
  Info
} from 'lucide-react';

// Simple toast notification component
interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  onClose: () => void;
}

const Toast: React.FC<ToastProps> = ({ message, type, onClose }) => {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertTriangle,
    info: Info
  };

  const colors = {
    success: 'bg-success/10 border-success/20 text-success',
    error: 'bg-error/10 border-error/20 text-error',
    warning: 'bg-warning/10 border-warning/20 text-warning',
    info: 'bg-info/10 border-info/20 text-info'
  };

  const Icon = icons[type];

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${colors[type]} shadow-lg max-w-sm animate-in slide-in-from-right duration-300`}>
      <div className="flex items-center space-x-3">
        <Icon className="w-5 h-5 flex-shrink-0" />
        <p className="text-sm font-medium">{message}</p>
        <button
          onClick={onClose}
          className="ml-auto flex-shrink-0 p-1 hover:bg-black/10 rounded"
        >
          <XCircle className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

// TypeScript interfaces for document upload and scanning
interface UploadedFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  uploadProgress: number;
  status: 'uploading' | 'uploaded' | 'scanning' | 'scanned' | 'error';
  preview?: string;
  scanResult?: DocumentScanResult;
}

interface DocumentScanResult {
  documentType: 'pdf' | 'word' | 'text' | 'image' | 'unknown';
  isCompliant: boolean;
  confidence: number;
  extractedText: string;
  complianceScore: number;
  keywordMatches: ComplianceKeyword[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
  errors?: string[];
}

interface ComplianceKeyword {
  keyword: string;
  category: 'gdpr' | 'ccpa' | 'hipaa' | 'sox' | 'pci' | 'iso27001' | 'general';
  matches: number;
  context: string[];
  severity: 'info' | 'warning' | 'critical';
}

interface ScanProgress {
  stage: 'upload' | 'extraction' | 'analysis' | 'compliance' | 'complete';
  progress: number;
  message: string;
}

const ComplianceDocumentUpload: React.FC = () => {
  const { mode } = useTheme();
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState<ScanProgress>({
    stage: 'upload',
    progress: 0,
    message: 'Ready to upload documents'
  });
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'warning' | 'info' } | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Show toast notification
  const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 5000);
  };

  // Add error to error list
  const addError = (error: string) => {
    setErrors(prev => [...prev, error]);
    showToast(error, 'error');
  };

  // Clear errors
  const clearErrors = () => {
    setErrors([]);
  };

  // Supported file types and validation
  const SUPPORTED_TYPES = {
    'application/pdf': { icon: FileText, label: 'PDF Document' },
    'application/msword': { icon: FileText, label: 'Word Document' },
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: FileText, label: 'Word Document' },
    'text/plain': { icon: File, label: 'Text File' },
    'image/jpeg': { icon: Image, label: 'JPEG Image' },
    'image/png': { icon: Image, label: 'PNG Image' },
    'image/gif': { icon: Image, label: 'GIF Image' }
  };

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const MAX_FILES = 5;

  // File validation function
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {
      return { isValid: false, error: 'Unsupported file type. Please upload PDF, Word, Text, or Image files.' };
    }
    if (file.size > MAX_FILE_SIZE) {
      return { isValid: false, error: 'File size exceeds 10MB limit.' };
    }
    if (uploadedFiles.length >= MAX_FILES) {
      return { isValid: false, error: `Maximum ${MAX_FILES} files allowed.` };
    }
    return { isValid: true };
  };

  // Generate file preview for images
  const generatePreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  // Handle file upload
  const handleFileUpload = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    clearErrors(); // Clear previous errors

    for (const file of fileArray) {
      const validation = validateFile(file);
      if (!validation.isValid) {
        addError(validation.error || 'File validation failed');
        continue;
      }

      const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const preview = await generatePreview(file);

      const uploadedFile: UploadedFile = {
        id: fileId,
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadProgress: 0,
        status: 'uploading',
        preview
      };

      setUploadedFiles(prev => [...prev, uploadedFile]);

      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setUploadedFiles(prev => prev.map(f => {
          if (f.id === fileId) {
            const newProgress = Math.min(f.uploadProgress + 10, 100);
            return {
              ...f,
              uploadProgress: newProgress,
              status: newProgress === 100 ? 'uploaded' : 'uploading'
            };
          }
          return f;
        }));
      }, 200);

      setTimeout(() => {
        clearInterval(uploadInterval);
        setUploadedFiles(prev => prev.map(f =>
          f.id === fileId ? { ...f, uploadProgress: 100, status: 'uploaded' } : f
        ));
        showToast(`${file.name} uploaded successfully!`, 'success');
      }, 2000);
    }

    if (fileArray.length > 1) {
      showToast(`Uploading ${fileArray.length} files...`, 'info');
    }
  }, [uploadedFiles.length]);

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  // File input change handler
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
    // Reset input value to allow re-uploading the same file
    e.target.value = '';
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Get file icon
  const getFileIcon = (fileType: string) => {
    const typeInfo = SUPPORTED_TYPES[fileType as keyof typeof SUPPORTED_TYPES];
    return typeInfo ? typeInfo.icon : File;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Compliance keywords for scanning
  const COMPLIANCE_KEYWORDS = {
    gdpr: {
      keywords: ['personal data', 'data subject', 'consent', 'privacy policy', 'data protection', 'gdpr', 'right to be forgotten', 'data controller', 'data processor'],
      severity: 'critical' as const
    },
    ccpa: {
      keywords: ['california consumer privacy act', 'ccpa', 'personal information', 'consumer rights', 'opt-out', 'sale of personal information'],
      severity: 'critical' as const
    },
    hipaa: {
      keywords: ['protected health information', 'phi', 'hipaa', 'medical records', 'health information', 'patient data'],
      severity: 'critical' as const
    },
    sox: {
      keywords: ['sarbanes-oxley', 'sox', 'financial reporting', 'internal controls', 'audit trail', 'financial disclosure'],
      severity: 'warning' as const
    },
    pci: {
      keywords: ['payment card industry', 'pci dss', 'cardholder data', 'credit card', 'payment processing', 'card data'],
      severity: 'critical' as const
    },
    iso27001: {
      keywords: ['iso 27001', 'information security', 'security management', 'risk assessment', 'security controls'],
      severity: 'warning' as const
    }
  };

  // Simulate document scanning and analysis
  const simulateDocumentScan = async (file: UploadedFile): Promise<DocumentScanResult> => {
    // Simulate text extraction based on file type
    let extractedText = '';

    if (file.type === 'application/pdf') {
      extractedText = 'Sample PDF content with personal data processing information and GDPR compliance requirements...';
    } else if (file.type.includes('word')) {
      extractedText = 'Sample Word document containing privacy policy, data subject rights, and consent management procedures...';
    } else if (file.type === 'text/plain') {
      extractedText = 'Sample text file with compliance documentation, security controls, and audit procedures...';
    } else if (file.type.startsWith('image/')) {
      extractedText = 'OCR extracted text from image: compliance checklist, security requirements, data protection measures...';
    }

    // Analyze compliance keywords
    const keywordMatches: ComplianceKeyword[] = [];
    let totalMatches = 0;

    Object.entries(COMPLIANCE_KEYWORDS).forEach(([category, config]) => {
      const matches = config.keywords.filter(keyword =>
        extractedText.toLowerCase().includes(keyword.toLowerCase())
      ).length;

      if (matches > 0) {
        keywordMatches.push({
          keyword: config.keywords.join(', '),
          category: category as ComplianceKeyword['category'],
          matches,
          context: [`Found ${matches} compliance-related terms in document`],
          severity: config.severity
        });
        totalMatches += matches;
      }
    });

    // Calculate compliance score
    const baseScore = Math.min(90, 60 + (totalMatches * 5));
    const complianceScore = Math.max(30, baseScore + (Math.random() * 20 - 10));

    // Determine risk level
    let riskLevel: DocumentScanResult['riskLevel'] = 'low';
    if (complianceScore < 50) riskLevel = 'critical';
    else if (complianceScore < 70) riskLevel = 'high';
    else if (complianceScore < 85) riskLevel = 'medium';

    // Generate recommendations
    const recommendations: string[] = [];
    if (complianceScore < 70) {
      recommendations.push('Review document for missing compliance requirements');
      recommendations.push('Add explicit data protection clauses');
    }
    if (keywordMatches.some(k => k.category === 'gdpr')) {
      recommendations.push('Ensure GDPR compliance measures are properly documented');
    }
    if (totalMatches === 0) {
      recommendations.push('Consider adding compliance-specific terminology');
    }

    return {
      documentType: file.type.includes('pdf') ? 'pdf' :
                   file.type.includes('word') ? 'word' :
                   file.type === 'text/plain' ? 'text' :
                   file.type.startsWith('image/') ? 'image' : 'unknown',
      isCompliant: complianceScore >= 70,
      confidence: Math.min(95, 80 + Math.random() * 15),
      extractedText,
      complianceScore: Math.round(complianceScore),
      keywordMatches,
      riskLevel,
      recommendations,
      errors: complianceScore < 50 ? ['Document may not meet minimum compliance standards'] : undefined
    };
  };

  // Start scanning all uploaded files
  const startDocumentScan = async () => {
    const uploadedFilesToScan = uploadedFiles.filter(f => f.status === 'uploaded');
    if (uploadedFilesToScan.length === 0) {
      showToast('No documents available for scanning', 'warning');
      return;
    }

    clearErrors();
    setIsScanning(true);
    setScanProgress({ stage: 'extraction', progress: 0, message: 'Starting document analysis...' });
    showToast(`Starting scan of ${uploadedFilesToScan.length} document${uploadedFilesToScan.length > 1 ? 's' : ''}...`, 'info');

    for (let i = 0; i < uploadedFilesToScan.length; i++) {
      const file = uploadedFilesToScan[i];

      // Update file status to scanning
      setUploadedFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, status: 'scanning' } : f
      ));

      // Simulate scanning stages
      const stages = [
        { stage: 'extraction' as const, progress: 25, message: `Extracting text from ${file.name}...` },
        { stage: 'analysis' as const, progress: 50, message: `Analyzing content for compliance...` },
        { stage: 'compliance' as const, progress: 75, message: `Checking compliance requirements...` },
        { stage: 'complete' as const, progress: 100, message: `Scan complete for ${file.name}` }
      ];

      for (const stage of stages) {
        setScanProgress(stage);
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Generate scan result
      const scanResult = await simulateDocumentScan(file);

      // Update file with scan result
      setUploadedFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, status: 'scanned', scanResult } : f
      ));
    }

    setIsScanning(false);
    setScanProgress({ stage: 'complete', progress: 100, message: 'All documents scanned successfully' });

    const compliantCount = uploadedFilesToScan.filter(f => f.scanResult?.isCompliant).length;
    const nonCompliantCount = uploadedFilesToScan.length - compliantCount;

    if (nonCompliantCount === 0) {
      showToast(`All ${uploadedFilesToScan.length} documents are compliant!`, 'success');
    } else {
      showToast(`Scan complete: ${compliantCount} compliant, ${nonCompliantCount} non-compliant`, 'warning');
    }
  };

  return (
    <div className="space-y-6">
      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}

      {/* Error Display */}
      {errors.length > 0 && (
        <div className="bg-error/10 border border-error/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-error" />
            <h4 className="font-medium text-error">Upload Errors</h4>
          </div>
          <ul className="space-y-1">
            {errors.map((error, index) => (
              <li key={index} className="text-sm text-error">• {error}</li>
            ))}
          </ul>
          <button
            onClick={clearErrors}
            className="mt-2 text-xs text-error hover:text-error/80 underline"
          >
            Clear errors
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-card rounded-lg p-6 border border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full flex items-center justify-center">
              <Shield className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-text">Compliance Document Scanner</h2>
              <p className="text-text-secondary">Upload and analyze documents for compliance requirements</p>
            </div>
          </div>
          <div className="text-right">
            <div className="bg-surface rounded-lg p-3 border border-border">
              <p className="text-sm font-medium text-text-secondary">Files Uploaded</p>
              <p className="text-2xl font-bold text-primary">{uploadedFiles.length}/{MAX_FILES}</p>
            </div>
          </div>
        </div>

        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center transition-all duration-200 transform ${
            isDragOver
              ? 'border-primary bg-primary/5 scale-[1.02]'
              : 'border-border hover:border-primary/50 hover:bg-primary/5 hover:scale-[1.01]'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          role="button"
          tabIndex={0}
          aria-label="Upload area for compliance documents"
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              fileInputRef.current?.click();
            }
          }}
        >
          <div className={`w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-4 transition-transform duration-200 ${
            isDragOver ? 'scale-110' : ''
          }`}>
            <Upload className={`w-6 h-6 md:w-8 md:h-8 text-primary transition-transform duration-200 ${
              isDragOver ? 'animate-bounce' : ''
            }`} />
          </div>
          <h3 className="text-base md:text-lg font-medium text-text mb-2">
            {isDragOver ? 'Drop files here' : 'Upload Compliance Documents'}
          </h3>
          <p className="text-sm text-text-secondary mb-4">
            Drag and drop files here, or click to browse
          </p>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="bg-primary hover:bg-primary-hover text-white px-4 md:px-6 py-2 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            Choose Files
          </button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
            onChange={handleFileInputChange}
            className="hidden"
          />
          <p className="text-xs text-text-secondary mt-4">
            Supported: PDF, Word, Text, Images • Max {formatFileSize(MAX_FILE_SIZE)} per file • Up to {MAX_FILES} files
          </p>
        </div>
      </div>

      {/* Scanning Progress */}
      {uploadedFiles.length > 0 && (
        <div className="bg-card rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-text flex items-center">
              <Scan className="w-5 h-5 mr-2" />
              Document Analysis
            </h3>
            {uploadedFiles.some(f => f.status === 'uploaded') && !isScanning && (
              <button
                onClick={startDocumentScan}
                className="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                aria-label={`Scan ${uploadedFiles.filter(f => f.status === 'uploaded').length} uploaded documents for compliance`}
              >
                <Scan className="w-4 h-4" />
                <span>Scan Documents</span>
              </button>
            )}
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-text">{scanProgress.message}</span>
              <span className="text-sm text-text-secondary">{scanProgress.progress}%</span>
            </div>
            <div className="w-full bg-surface rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${
                  isScanning ? 'bg-gradient-to-r from-primary to-primary-light' : 'bg-success'
                }`}
                style={{ width: `${scanProgress.progress}%` }}
              />
            </div>
          </div>

          {/* Scanning Status */}
          {isScanning && (
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3">
                <Loader className="w-5 h-5 text-primary animate-spin" />
                <div>
                  <p className="font-medium text-primary">Scanning in Progress</p>
                  <p className="text-sm text-text-secondary">Analyzing documents for compliance requirements...</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Uploaded Documents ({uploadedFiles.length})
          </h3>
          <div className="space-y-3">
            {uploadedFiles.map((file) => {
              const FileIcon = getFileIcon(file.type);
              return (
                <div
                  key={file.id}
                  className="flex items-center space-x-4 p-4 bg-surface rounded-lg border border-border"
                >
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <FileIcon className="w-5 h-5 text-primary" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-text truncate">{file.name}</p>
                    <p className="text-xs text-text-secondary">{formatFileSize(file.size)}</p>
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="w-full bg-border rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${file.uploadProgress}%` }}
                          />
                        </div>
                        <p className="text-xs text-text-secondary mt-1">Uploading... {file.uploadProgress}%</p>
                      </div>
                    )}
                    {file.status === 'scanning' && (
                      <div className="mt-2">
                        <div className="flex items-center space-x-2">
                          <Loader className="w-3 h-3 text-primary animate-spin" />
                          <p className="text-xs text-primary">Scanning...</p>
                        </div>
                      </div>
                    )}
                    {file.scanResult && (
                      <div className="mt-2 flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <span className="text-xs text-text-secondary">Compliance:</span>
                          <span className={`text-xs font-medium ${
                            file.scanResult.isCompliant ? 'text-success' : 'text-error'
                          }`}>
                            {file.scanResult.complianceScore}%
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className="text-xs text-text-secondary">Risk:</span>
                          <span className={`text-xs font-medium ${
                            file.scanResult.riskLevel === 'low' ? 'text-success' :
                            file.scanResult.riskLevel === 'medium' ? 'text-warning' :
                            'text-error'
                          }`}>
                            {file.scanResult.riskLevel.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {file.status === 'uploading' && <Loader className="w-4 h-4 text-primary animate-spin" />}
                    {file.status === 'uploaded' && <CheckCircle className="w-4 h-4 text-success" />}
                    {file.status === 'scanning' && <Loader className="w-4 h-4 text-primary animate-spin" />}
                    {file.status === 'scanned' && (
                      <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
                        file.scanResult?.isCompliant ? 'bg-success' : 'bg-error'
                      }`}>
                        {file.scanResult?.isCompliant ? (
                          <CheckCircle className="w-3 h-3 text-white" />
                        ) : (
                          <XCircle className="w-3 h-3 text-white" />
                        )}
                      </div>
                    )}
                    {file.status === 'error' && <XCircle className="w-4 h-4 text-error" />}
                    {file.preview && (
                      <button className="p-1 hover:bg-border rounded">
                        <Eye className="w-4 h-4 text-text-secondary" />
                      </button>
                    )}
                    <button
                      onClick={() => removeFile(file.id)}
                      className="p-1 hover:bg-error/10 rounded text-error"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Detailed Scan Results */}
      {uploadedFiles.some(f => f.scanResult) && (
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold text-text mb-6 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Compliance Analysis Results
          </h3>

          {uploadedFiles
            .filter(f => f.scanResult)
            .map((file) => (
              <div key={file.id} className="mb-8 last:mb-0">
                <div className="border border-border rounded-lg overflow-hidden">
                  {/* File Header */}
                  <div className={`p-4 ${
                    file.scanResult!.isCompliant
                      ? 'bg-success/10 border-b border-success/20'
                      : 'bg-error/10 border-b border-error/20'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          file.scanResult!.isCompliant ? 'bg-success' : 'bg-error'
                        }`}>
                          {file.scanResult!.isCompliant ? (
                            <CheckCircle className="w-5 h-5 text-white" />
                          ) : (
                            <XCircle className="w-5 h-5 text-white" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-semibold text-text">{file.name}</h4>
                          <p className="text-sm text-text-secondary">
                            {file.scanResult!.isCompliant ? 'Compliant' : 'Non-Compliant'} •
                            Confidence: {file.scanResult!.confidence.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-2xl font-bold ${
                          file.scanResult!.isCompliant ? 'text-success' : 'text-error'
                        }`}>
                          {file.scanResult!.complianceScore}%
                        </div>
                        <p className="text-xs text-text-secondary">Compliance Score</p>
                      </div>
                    </div>
                  </div>

                  {/* Scan Details */}
                  <div className="p-4 space-y-4">
                    {/* Risk Level */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-text">Risk Level:</span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        file.scanResult!.riskLevel === 'low' ? 'bg-success/20 text-success' :
                        file.scanResult!.riskLevel === 'medium' ? 'bg-warning/20 text-warning' :
                        'bg-error/20 text-error'
                      }`}>
                        {file.scanResult!.riskLevel.toUpperCase()}
                      </span>
                    </div>

                    {/* Compliance Keywords */}
                    {file.scanResult!.keywordMatches.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-text mb-2">Compliance Keywords Found:</h5>
                        <div className="space-y-2">
                          {file.scanResult!.keywordMatches.map((match, index) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-surface rounded border border-border">
                              <div>
                                <span className="text-sm font-medium text-text">{match.category.toUpperCase()}</span>
                                <p className="text-xs text-text-secondary">{match.matches} matches found</p>
                              </div>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                match.severity === 'critical' ? 'bg-error/20 text-error' :
                                match.severity === 'warning' ? 'bg-warning/20 text-warning' :
                                'bg-info/20 text-info'
                              }`}>
                                {match.severity}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Recommendations */}
                    {file.scanResult!.recommendations.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-text mb-2">Recommendations:</h5>
                        <ul className="space-y-1">
                          {file.scanResult!.recommendations.map((rec, index) => (
                            <li key={index} className="text-sm text-text-secondary flex items-start space-x-2">
                              <span className="text-primary mt-1">•</span>
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Errors */}
                    {file.scanResult!.errors && file.scanResult!.errors.length > 0 && (
                      <div className="bg-error/10 border border-error/20 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <AlertTriangle className="w-4 h-4 text-error" />
                          <h5 className="text-sm font-medium text-error">Issues Found:</h5>
                        </div>
                        <ul className="space-y-1">
                          {file.scanResult!.errors.map((error, index) => (
                            <li key={index} className="text-sm text-error">• {error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Document Type Info */}
                    <div className="pt-2 border-t border-border">
                      <div className="flex items-center justify-between text-xs text-text-secondary">
                        <span>Document Type: {file.scanResult!.documentType.toUpperCase()}</span>
                        <span>Scanned: {new Date().toLocaleTimeString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

          {/* Overall Summary */}
          {uploadedFiles.filter(f => f.scanResult).length > 1 && (
            <div className="mt-6 p-4 bg-surface rounded-lg border border-border">
              <h4 className="font-semibold text-text mb-3">Overall Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-success">
                    {uploadedFiles.filter(f => f.scanResult?.isCompliant).length}
                  </div>
                  <p className="text-sm text-text-secondary">Compliant Documents</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-error">
                    {uploadedFiles.filter(f => f.scanResult && !f.scanResult.isCompliant).length}
                  </div>
                  <p className="text-sm text-text-secondary">Non-Compliant Documents</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {Math.round(
                      uploadedFiles
                        .filter(f => f.scanResult)
                        .reduce((acc, f) => acc + f.scanResult!.complianceScore, 0) /
                      uploadedFiles.filter(f => f.scanResult).length
                    )}%
                  </div>
                  <p className="text-sm text-text-secondary">Average Compliance</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ComplianceDocumentUpload;
