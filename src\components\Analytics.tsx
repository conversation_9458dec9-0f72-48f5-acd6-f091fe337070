import React, { useEffect, useRef, useMemo } from 'react';
import { Chart } from 'chart.js';
import { optimizedChartDefaults } from '../utils/chartOptimizations';
import { useTheme } from '../context/ThemeContext';

const Analytics: React.FC = () => {
  const { mode } = useTheme();
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  const chartConfig = useMemo(() => ({
    type: 'line' as const,
    data: {
      labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'],
      datasets: [
        {
          label: 'Europe',
          data: [34, 43, 66, 69, 58, 40, 78, 13],
          borderColor: '#10b981', // Professional green for compliant
          backgroundColor: 'transparent',
          borderWidth: 2,
        },
        {
          label: 'Uk',
          data: [21, 26, 29, 32, 47, 46, 50, 90],
          borderColor: '#ef4444', // Professional red for non-compliant
          backgroundColor: 'transparent',
          borderWidth: 2,
        },
        {
          label: 'America',
          data: [19, 24, 28, 26, 38, 31, 19, 52],
          borderColor: '#f59e0b', // Professional amber for pending
          backgroundColor: 'transparent',
          borderWidth: 2,
        }
      ]
    },
    options: {
      ...optimizedChartDefaults,
      plugins: {
        legend: { display: false },
        tooltip: {
          enabled: true,
          mode: 'index' as const,
          intersect: false,
          backgroundColor: mode === 'dark' ? '#1f2937' : '#ffffff',
          titleColor: mode === 'dark' ? '#f9fafb' : '#111827',
          bodyColor: mode === 'dark' ? '#e5e7eb' : '#374151',
          borderColor: mode === 'dark' ? '#374151' : '#e5e7eb',
          borderWidth: 1,
          padding: 12,
          usePointStyle: true,
          callbacks: {
            label: (context: any) => `${context.dataset.label}: ${context.parsed.y} Compliant`
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Compliance Rate',
            color: mode === 'dark' ? '#e2e8f0' : '#4b5563',
            font: {
              size: 12,
              weight: '500' as const
            }
          },
          grid: {
            color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#f1f5f9',
            borderDash: [2, 2]
          },
          ticks: {
            color: mode === 'dark' ? '#9ca3af' : '#6b7280',
            font: {
              size: 11
            }
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            color: mode === 'dark' ? '#9ca3af' : '#6b7280',
            font: {
              size: 11
            }
          }
        }
      }
    }
  }), [mode]);

  useEffect(() => {
    if (!chartRef.current) return;

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    if (ctx) {
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
    }

    chartInstance.current = new Chart(chartRef.current, {
      ...chartConfig,
      options: {
        ...chartConfig.options,
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart' as const
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
        chartInstance.current = null;
      }
    };
  }, [chartConfig]);

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-text">Compliance Analytics</h3>
        <p className="text-sm text-text-secondary mt-1">Monthly compliance status distribution</p>
      </div>
      <div
        className="h-[400px]"
        style={{
          contain: 'content',
          willChange: 'transform',
          isolation: 'isolate',
          backfaceVisibility: 'hidden'
        }}
      >
        <canvas ref={chartRef} />
      </div>
      <div className="flex justify-center gap-8 mt-6">
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-green-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">Europe</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-red-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">UK</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-amber-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">America</span>
        </div>
      </div>
    </div>
  );
};

export default React.memo(Analytics);