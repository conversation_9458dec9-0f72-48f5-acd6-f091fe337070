import React, { useEffect, useState } from 'react';
import { useConsent } from '../context/ConsentContext';

interface StatCircleProps {
  percentage: number;
  color: string;
  label: string;
  sublabel: string;
  isLoading?: boolean;
}

const StatCircle = ({ percentage, color, label, sublabel, isLoading = false }: StatCircleProps) => {
  const circumference = 2 * Math.PI * 45; // radius = 45
  const strokeDasharray = `${(percentage * circumference) / 100} ${circumference}`;
  
  const circleClasses = `transition-all duration-700 ease-in-out ${isLoading ? 'opacity-50' : 'opacity-100'}`;
  const textClasses = `transition-all duration-500 ${isLoading ? 'animate-pulse' : ''}`;

  
  return (
    <div className="relative inline-flex flex-col items-center">
      <svg className="w-32 h-32 transform -rotate-90">
        <circle
          cx="64"
          cy="64"
          r="45"
          stroke="#e5e7eb"
          strokeWidth="8"
          fill="none"
        />
        <circle
          cx="64"
          cy="64"
          r="45"
          stroke={color}
          strokeWidth="8"
          strokeDasharray={strokeDasharray}
          fill="none"
          className={circleClasses}
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={`text-2xl font-bold ${textClasses}`}>{percentage}%</span>
      </div>
      <div className="mt-2 text-center">
        <div className="font-semibold">{label}</div>
        <div className="text-sm text-gray-600">{sublabel}</div>
      </div>
    </div>
  );
};

const ConsentStats = () => {
  const { consentData, isLoading, error, updateConsentData } = useConsent();
  const [updateMessage, setUpdateMessage] = useState('');

  useEffect(() => {
    if (consentData.lastUpdated) {
      setUpdateMessage(`Last updated: ${consentData.lastUpdated.toLocaleTimeString()}`);
      const timer = setTimeout(() => setUpdateMessage(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [consentData.lastUpdated]);

  useEffect(() => {
    if (error) {
      setUpdateMessage(error);
      const timer = setTimeout(() => setUpdateMessage(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-4 gap-8">
      <StatCircle
        percentage={consentData.greenLevel}
        color="#86efac"
        label="Green Level"
        sublabel="Data Consent"
        isLoading={isLoading}
      />
      <StatCircle
        percentage={consentData.amberLevel}
        color="#fbbf24"
        label="Amber Level"
        sublabel="Data Consent"
        isLoading={isLoading}
      />
      <StatCircle
        percentage={consentData.redLevel}
        color="#ef4444"
        label="Red Level"
        sublabel="Data Consent"
        isLoading={isLoading}
      />
      <div className="flex flex-col items-center">
        <StatCircle
          percentage={consentData.privacyAcknowledgement}
          color="#7e22ce"
          label="Data Privacy"
          sublabel="Acknowledgement"
          isLoading={isLoading}
        />
        <button 
          className="mt-4 px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          Download Report
        </button>
      </div>
      {updateMessage && (
        <div className={`text-center py-2 px-4 rounded-md ${error ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'} transition-all duration-300`}>
          {updateMessage}
        </div>
      )}
    </div>
  </div>
  );
};

export default ConsentStats;