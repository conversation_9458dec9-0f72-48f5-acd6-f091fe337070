import axios from 'axios';

// In development mode with MSW, we can use relative URLs
// In production, we would use the actual API URL
const isDev = import.meta.env.MODE === 'development';

const api = axios.create({
  baseURL: isDev ? '/api' : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'),
  timeout: 30000, // 30 seconds for document processing
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Types
export interface DocumentScanRequest {
  file: File;
  documentType: 'passport' | 'visa';
  passengerName?: string;
}

export interface DocumentScanResult {
  id: string;
  documentType: 'passport' | 'visa' | 'unknown';
  isValid: boolean;
  confidence: number;
  extractedData: {
    fullName?: string;
    nationality?: string;
    documentNumber?: string;
    expiryDate?: string;
    issueDate?: string;
    placeOfBirth?: string;
    gender?: string;
    dateOfBirth?: string;
  };
  verificationStatus: 'approved' | 'denied' | 'pending' | 'error';
  riskLevel: 'low' | 'medium' | 'high';
  processingTime: number;
  errors?: string[];
  vendorInfo?: {
    vendor: string;
    apiVersion: string;
    processingNode: string;
  };
}

export interface TravelRestriction {
  id: string;
  country: string;
  nationality: string;
  restrictionType: 'banned' | 'visa_required' | 'quarantine' | 'health_check';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  effectiveDate: string;
  expiryDate?: string;
  lastUpdated: string;
  updatedBy: string;
  isActive: boolean;
}

export interface VerificationRecord {
  id: string;
  passengerName: string;
  documentType: 'passport' | 'visa';
  documentNumber: string;
  nationality: string;
  verificationStatus: 'approved' | 'denied' | 'pending';
  confidence: number;
  riskLevel: 'low' | 'medium' | 'high';
  timestamp: string;
  processingTime: number;
  verifiedBy: string;
  errors?: string[];
  auditTrailId: string;
}



// Document Verification Services
export const documentVerificationService = {
  async scanDocument(request: DocumentScanRequest): Promise<DocumentScanResult> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('documentType', request.documentType);
    if (request.passengerName) {
      formData.append('passengerName', request.passengerName);
    }

    const response = await api.post('/flyer-verification/scan', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

  async getVerificationHistory(filters?: {
    startDate?: string;
    endDate?: string;
    status?: string;
    riskLevel?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ records: VerificationRecord[]; total: number }> {
    const response = await api.get('/flyer-verification/history', { params: filters });
    return response.data;
  },

  async getVerificationById(id: string): Promise<VerificationRecord> {
    const response = await api.get(`/flyer-verification/history/${id}`);
    return response.data;
  },

  async retryVerification(id: string): Promise<DocumentScanResult> {
    const response = await api.post(`/flyer-verification/retry/${id}`);
    return response.data;
  }
};

// Travel Restrictions Services
export const travelRestrictionsService = {
  async getRestrictions(filters?: {
    country?: string;
    nationality?: string;
    restrictionType?: string;
    severity?: string;
    isActive?: boolean;
  }): Promise<TravelRestriction[]> {
    const response = await api.get('/flyer-verification/restrictions', { params: filters });
    return response.data;
  },

  async createRestriction(restriction: Omit<TravelRestriction, 'id' | 'lastUpdated' | 'updatedBy'>): Promise<TravelRestriction> {
    const response = await api.post('/flyer-verification/restrictions', restriction);
    return response.data;
  },

  async updateRestriction(id: string, updates: Partial<TravelRestriction>): Promise<TravelRestriction> {
    const response = await api.put(`/flyer-verification/restrictions/${id}`, updates);
    return response.data;
  },

  async deleteRestriction(id: string): Promise<void> {
    await api.delete(`/flyer-verification/restrictions/${id}`);
  },

  async checkRestrictions(nationality: string, destinationCountry: string): Promise<{
    isRestricted: boolean;
    restrictions: TravelRestriction[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  }> {
    const response = await api.post('/flyer-verification/restrictions/check', {
      nationality,
      destinationCountry
    });
    return response.data;
  },

  async refreshRestrictionsData(): Promise<{ updated: number; timestamp: string }> {
    const response = await api.post('/flyer-verification/restrictions/refresh');
    return response.data;
  }
};



// Statistics and Analytics Services
export const analyticsService = {
  async getVerificationStats(period?: '24h' | '7d' | '30d' | '90d'): Promise<{
    totalVerifications: number;
    successfulVerifications: number;
    failedVerifications: number;
    pendingVerifications: number;
    accuracyRate: number;
    averageProcessingTime: number;
    riskDistribution: Record<string, number>;
    trendData: Array<{ date: string; count: number; successRate: number }>;
  }> {
    const response = await api.get('/flyer-verification/analytics/stats', { params: { period } });
    return response.data;
  },

  async getComplianceMetrics(): Promise<{
    auditCoverage: number;
    complianceScore: number;
    riskMitigationRate: number;
    vendorPerformance: Array<{
      vendor: string;
      accuracy: number;
      avgProcessingTime: number;
      errorRate: number;
    }>;
  }> {
    const response = await api.get('/flyer-verification/analytics/compliance');
    return response.data;
  },

  async generateComplianceReport(options: {
    startDate: string;
    endDate: string;
    includeAuditTrail: boolean;
    includeVendorLiability: boolean;
    format: 'pdf' | 'excel';
  }): Promise<{ reportId: string; downloadUrl: string }> {
    const response = await api.post('/flyer-verification/analytics/report', options);
    return response.data;
  }
};

// System Health and Configuration Services
export const systemService = {
  async getSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'down';
    services: Array<{
      name: string;
      status: 'up' | 'down';
      responseTime: number;
      lastCheck: string;
    }>;
    uptime: number;
    version: string;
  }> {
    const response = await api.get('/flyer-verification/system/health');
    return response.data;
  },

  async getConfiguration(): Promise<{
    ocrProvider: string;
    maxFileSize: number;
    supportedFormats: string[];
    processingTimeout: number;
    confidenceThreshold: number;
    riskThresholds: Record<string, number>;
  }> {
    const response = await api.get('/flyer-verification/system/config');
    return response.data;
  },

  async updateConfiguration(config: Record<string, any>): Promise<void> {
    await api.put('/flyer-verification/system/config', config);
  }
};

// Error handling utilities
export class VerificationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'VerificationError';
  }
}

export const handleApiError = (error: any): VerificationError => {
  if (error.response?.data?.error) {
    return new VerificationError(
      error.response.data.error.message || 'Verification failed',
      error.response.data.error.code || 'UNKNOWN_ERROR',
      error.response.data.error.details
    );
  }
  
  return new VerificationError(
    error.message || 'An unexpected error occurred',
    'NETWORK_ERROR'
  );
};

export default {
  documentVerificationService,
  travelRestrictionsService,
  analyticsService,
  systemService,
  VerificationError,
  handleApiError
};
