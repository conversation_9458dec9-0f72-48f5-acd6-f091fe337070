import React, { useState } from 'react';
import { <PERSON>ert<PERSON><PERSON><PERSON>, Clock, CheckCircle, ArrowLeft } from 'lucide-react';
import { AlertTriangle } from 'lucide-react';
import { CheckSquare, Square, Trash2 } from 'lucide-react';

interface SubPolicyPageProps {
  subPolicy: {
    name: string;
    status: string;
    completionDate?: string;
    toBeUpdated?: boolean;
  };
  parentPolicy: string;
  onBack: () => void;
  onStatusChange: (status: string, toBeUpdated?: boolean) => void;
}

export const SubPolicyPage: React.FC<SubPolicyPageProps> = ({
  subPolicy,
  parentPolicy,
  onBack,
  onStatusChange,
}) => {
  const [updateReason, setUpdateReason] = useState('');
  const [todos, setTodos] = useState<{ id: string; text: string; completed: boolean }[]>([]);

  const addTodo = () => {
    if (updateReason.trim()) {
      setTodos([...todos, { id: Date.now().toString(), text: updateReason, completed: false }]);
      setUpdateReason('');
    }
  };

  return (
    // Update the main container and header
    <div className="fixed inset-0 bg-gray-50 z-50 overflow-y-auto">
      <div className="max-w-5xl mx-auto px-4 py-8">
        <button
          onClick={onBack}
          className="mb-6 flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to Policies
        </button>

        <div className="space-y-6 bg-white rounded-xl shadow-sm p-6">
          <div className="border-b pb-6">
            <p className="text-sm text-gray-500 font-medium">{parentPolicy}</p>
            <h1 className="text-2xl font-semibold text-gray-900 mt-2">
              {subPolicy.name}
            </h1>
          </div>

          {/* Update card styling */}
          <div className="grid grid-cols-2 gap-6">
            <div className="bg-white border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                <span className="w-1 h-5 bg-blue-500 rounded-full mr-2"></span>
                Document Details
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Reference: DOC-{Math.random().toString(36).substr(2, 9).toUpperCase()}
                </p>
                <p className="text-sm text-gray-600">Version: 1.0.0</p>
                <p className="text-sm text-gray-600">Owner: John Smith</p>
                <p className="text-sm text-gray-600">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-3">Status</h3>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <button
                    onClick={() => onStatusChange('pending')}
                    className={`flex items-center px-4 py-2 rounded-lg border shadow-sm transition-all ${
                      subPolicy.status === 'pending'
                        ? 'bg-orange-50 border-orange-200 text-orange-700 shadow-orange-100'
                        : 'hover:bg-gray-50 hover:shadow'
                    }`}
                  >
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Pending
                  </button>
                  <button
                    onClick={() => onStatusChange('in_progress')}
                    className={`flex items-center px-4 py-2 rounded-lg border shadow-sm transition-all ${
                      subPolicy.status === 'in_progress'
                        ? 'bg-blue-50 border-blue-200 text-blue-700 shadow-blue-100'
                        : 'hover:bg-gray-50 hover:shadow'
                    }`}
                  >
                    <Clock className="w-4 h-4 mr-2" />
                    In Progress
                  </button>
                  <button
                    onClick={() => onStatusChange('completed')}
                    className={`flex items-center px-4 py-2 rounded-lg border shadow-sm transition-all ${
                      subPolicy.status === 'completed'
                        ? 'bg-green-50 border-green-200 text-green-700 shadow-green-100'
                        : 'hover:bg-gray-50 hover:shadow'
                    }`}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Completed
                  </button>
                </div>

                <div className="pt-3 border-t">
                  <button
                    onClick={() => onStatusChange('to_be_updated')}
                    className={`flex items-center px-4 py-2 rounded-lg border shadow-sm transition-all w-full justify-center ${
                      subPolicy.status === 'to_be_updated'
                        ? 'bg-purple-50 border-purple-200 text-purple-700 shadow-purple-100'
                        : 'hover:bg-gray-50 hover:shadow'
                    }`}
                  >
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    To Be Updated
                  </button>
                </div>

                {subPolicy.status === 'to_be_updated' && (
                  <div className="mt-2 space-y-3">
                    <div className="flex gap-2">
                      <textarea
                        value={updateReason}
                        onChange={(e) => setUpdateReason(e.target.value)}
                        placeholder="Enter update task..."
                        className="flex-1 px-3 py-2 text-sm border rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        rows={1}
                      />
                      <button
                        onClick={addTodo}
                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        Add
                      </button>
                    </div>
                    
                    <div className="space-y-2">
                      {todos.map((todo) => (
                        <div
                          key={todo.id}
                          className="flex items-center gap-2 p-2 border rounded-lg bg-white group"
                        >
                          <button
                            onClick={() => setTodos(todos.map(t => 
                              t.id === todo.id ? { ...t, completed: !t.completed } : t
                            ))}
                            className="text-gray-500 hover:text-blue-500 transition-colors"
                          >
                            {todo.completed ? (
                              <CheckSquare className="w-5 h-5" />
                            ) : (
                              <Square className="w-5 h-5" />
                            )}
                          </button>
                          <span className={`flex-1 text-sm ${todo.completed ? 'line-through text-gray-400' : 'text-gray-700'}`}>
                            {todo.text}
                          </span>
                          <button
                            onClick={() => setTodos(todos.filter(t => t.id !== todo.id))}
                            className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {subPolicy.status === 'completed' && (
                  <div className="text-sm text-gray-600 mt-2">
                    Completed on: {subPolicy.completionDate || new Date().toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Update Timeline section */}
          <div className="bg-white border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center">
              <span className="w-1 h-5 bg-blue-500 rounded-full mr-2"></span>
              Timeline & Updates
            </h3>
            <div className="space-y-4">
              <div className="border-l-2 border-gray-200 pl-4 space-y-6">
                <div className="relative">
                  <div className="absolute -left-[21px] top-1 w-3 h-3 bg-green-500 rounded-full"></div>
                  <p className="text-sm font-medium text-gray-900">Last Completed</p>
                  <p className="text-sm text-gray-600">December 15, 2023</p>
                  <p className="text-sm text-gray-500 mt-1">Policy successfully implemented and verified</p>
                </div>

                <div className="relative">
                  <div className="absolute -left-[21px] top-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                  <p className="text-sm font-medium text-gray-900">Status Updated</p>
                  <p className="text-sm text-gray-600">January 5, 2024</p>
                  <p className="text-sm text-gray-500 mt-1">Review process initiated for Q1 2024</p>
                </div>

                <div className="relative">
                  <div className="absolute -left-[21px] top-1 w-3 h-3 bg-orange-500 rounded-full"></div>
                  <p className="text-sm font-medium text-gray-900">Requirements Updated</p>
                  <p className="text-sm text-gray-600">January 10, 2024</p>
                  <p className="text-sm text-gray-500 mt-1">New compliance requirements added</p>
                </div>
              </div>
            </div>
          </div>

          {/* Update other sections */}
          <div className="grid grid-cols-2 gap-6">
            <div className="bg-white border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                <span className="w-1 h-5 bg-blue-500 rounded-full mr-2"></span>
                Description
              </h3>
              <p className="text-gray-600">
                Detailed information about {subPolicy.name} implementation and requirements.
              </p>
            </div>

            <div className="bg-white border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                <span className="w-1 h-5 bg-blue-500 rounded-full mr-2"></span>
                Requirements
              </h3>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Requirement 1</li>
                <li>Requirement 2</li>
                <li>Requirement 3</li>
              </ul>
            </div>
          </div>

          <div className="bg-white border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center">
              <span className="w-1 h-5 bg-blue-500 rounded-full mr-2"></span>
              Stakeholders
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 rounded-lg border bg-gray-50">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                  JS
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">John Smith</p>
                  <p className="text-xs text-gray-500">Policy Owner</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};