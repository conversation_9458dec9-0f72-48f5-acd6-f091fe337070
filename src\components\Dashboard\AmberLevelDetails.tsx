import React, { useState } from 'react';
import { ArrowLeft, AlertTriangle, Users, TrendingDown, Calendar, Clock, RefreshCw, Download, FileText, PlayCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { useAmberLevelData } from '../../hooks/usePrivacyDashboard';
import { FullPageSkeleton, ErrorState } from './LoadingSkeleton';
import { toast } from 'react-toastify';

const AmberLevelDetails: React.FC = () => {
  const navigate = useNavigate();
  const { mode } = useTheme();
  const { data, isLoading, error, refresh, exportData, generateReport, startReviewProcess } = useAmberLevelData();
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleBackClick = () => {
    navigate('/');
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    setActionLoading('export');
    try {
      await exportData(format);
    } finally {
      setActionLoading(null);
    }
  };

  const handleGenerateReport = async () => {
    setActionLoading('report');
    try {
      await generateReport();
    } finally {
      setActionLoading(null);
    }
  };

  const handleStartReview = async () => {
    setActionLoading('review');
    try {
      await startReviewProcess!();
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefresh = async () => {
    setActionLoading('refresh');
    try {
      await refresh();
      toast.success('Data refreshed successfully!');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return <FullPageSkeleton />;
  }

  if (error || !data) {
    return (
      <ErrorState
        error={error || 'Failed to load amber level data'}
        onRetry={refresh}
        onBack={handleBackClick}
      />
    );
  }

  return (
    <div className="flex-1 bg-background">
      <div className="p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackClick}
              className="flex items-center text-text-secondary hover:text-text transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Privacy Dashboard
            </button>
            <button
              onClick={handleRefresh}
              disabled={actionLoading === 'refresh'}
              className="flex items-center px-3 py-2 text-sm border border-border rounded-md hover:bg-surface transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${actionLoading === 'refresh' ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          <h1 className="text-sm text-text-secondary">Customer privacy management portal</h1>
          <h2 className="text-2xl font-bold text-text">Amber Level Data Consent</h2>
          <p className="text-text-secondary mt-1">Last updated: {data.lastUpdated.toLocaleString()}</p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Overview Card */}
          <div className="lg:col-span-2 bg-card rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full border-4 flex items-center justify-center bg-surface mr-4"
                   style={{ borderColor: 'var(--dashboard-amber)' }}>
                <AlertTriangle className="w-8 h-8" style={{ color: 'var(--dashboard-amber)' }} />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text">Pending Review Data Consent</h3>
                <p className="text-text-secondary">{data.percentage.toFixed(1)}% of total data subjects</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-text mb-3">Overview</h4>
                <p className="text-text-secondary leading-relaxed">
                  Amber Level represents data subjects whose consent status requires review or action. This includes
                  individuals with partial consent, expired consent periods, or those who have requested modifications
                  to their consent preferences. These cases need attention to ensure continued compliance.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Review queue details coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Users className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-amber)' }} />
                    <span className="font-medium text-text">Total Subjects</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.totalSubjects.toLocaleString()}</p>
                  <p className="text-sm text-text-secondary">Requiring review</p>
                </div>

                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Review time analytics coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Clock className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-amber)' }} />
                    <span className="font-medium text-text">Avg. Review Time</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.performance.avgReviewTime.toFixed(1)} days</p>
                  <p className="text-sm text-text-secondary">Current average</p>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h5 className="font-medium text-text mb-2">Action Required</h5>
                <ul className="text-sm text-text-secondary space-y-1">
                  <li>• {Math.floor(data.totalSubjects * data.reviewCategories.expiringSoon / 100)} consents expiring within 30 days</li>
                  <li>• {Math.floor(data.totalSubjects * data.reviewCategories.partialConsent / 100)} partial consent forms pending completion</li>
                  <li>• {Math.floor(data.totalSubjects * data.reviewCategories.modifications / 100)} consent modification requests</li>
                  <li>• {Math.floor(data.totalSubjects * data.reviewCategories.withdrawals / 100)} consent withdrawals to process</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div className="space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Review Categories</h4>
              <div className="space-y-4">
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Expiring consents details coming soon!')}>
                  <span className="text-text-secondary">Expiring Soon</span>
                  <span className="font-medium text-text">{data.reviewCategories.expiringSoon}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Partial consent details coming soon!')}>
                  <span className="text-text-secondary">Partial Consent</span>
                  <span className="font-medium text-text">{data.reviewCategories.partialConsent}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Modification requests details coming soon!')}>
                  <span className="text-text-secondary">Modifications</span>
                  <span className="font-medium text-text">{data.reviewCategories.modifications}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Withdrawal requests details coming soon!')}>
                  <span className="text-text-secondary">Withdrawals</span>
                  <span className="font-medium text-text">{data.reviewCategories.withdrawals}%</span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Priority Actions</h4>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 text-text-secondary" />
                  <span className="text-text-secondary">Next review: Tomorrow</span>
                </div>
                <div className="text-sm text-text-secondary">
                  <p>• {data.priorityActions.highPriority} high-priority reviews</p>
                  <p>• {data.priorityActions.renewalsDue} consent renewals due</p>
                  <p>• {data.priorityActions.urgentModifications} urgent modifications</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Performance</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Resolution Rate</span>
                  <span className="font-medium text-text">{data.performance.resolutionRate.toFixed(0)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">On-time Reviews</span>
                  <span className="font-medium text-text">{data.performance.onTimeReviews.toFixed(0)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            onClick={handleStartReview}
            disabled={actionLoading === 'review'}
            className="flex items-center px-6 py-2 rounded transition-colors disabled:opacity-50"
            style={{
              backgroundColor: 'var(--dashboard-amber)',
              color: 'white'
            }}
          >
            {actionLoading === 'review' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <PlayCircle className="w-4 h-4 mr-2" />
            )}
            Start Review Process
          </button>
          <button
            onClick={() => handleExport('csv')}
            disabled={actionLoading === 'export'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'export' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Export Pending Items
          </button>
          <button
            onClick={handleGenerateReport}
            disabled={actionLoading === 'report'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'report' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FileText className="w-4 h-4 mr-2" />
            )}
            View Detailed Report
          </button>
        </div>
      </div>
    </div>
  );
};

export default AmberLevelDetails;
