import React, { createContext, useContext, useState, useEffect } from 'react';

interface ConsentData {
  greenLevel: number;
  amberLevel: number;
  redLevel: number;
  privacyAcknowledgement: number;
  lastUpdated?: Date;
}

interface ConsentUpdateOptions {
  silent?: boolean;
  immediate?: boolean;
}

interface ConsentContextType {
  consentData: ConsentData;
  updateConsentData: (newData: Partial<ConsentData>, options?: ConsentUpdateOptions) => void;
  isLoading: boolean;
  error: string | null;
}

const defaultConsentData: ConsentData = {
  greenLevel: 31,
  amberLevel: 58,
  redLevel: 11,
  privacyAcknowledgement: 100,
  lastUpdated: new Date(),
};

const ConsentContext = createContext<ConsentContextType | undefined>(undefined);

export const ConsentProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [consentData, setConsentData] = useState<ConsentData>(defaultConsentData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateConsentData = (data: Partial<ConsentData>): boolean => {
    const numericFields = ['greenLevel', 'amberLevel', 'redLevel', 'privacyAcknowledgement'];
    for (const field of numericFields) {
      if (field in data && ((data[field as keyof ConsentData] as number) < 0 || (data[field as keyof ConsentData] as number) > 100)) {
        setError(`${field} must be between 0 and 100`);
        return false;
      }
    }
    return true;
  };

  const updateConsentData = async (newData: Partial<ConsentData>, options: ConsentUpdateOptions = {}) => {
    const { silent = false, immediate = false } = options;
    
    if (!validateConsentData(newData)) {
      return;
    }

    if (!silent) {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, immediate ? 0 : 500));
      
      setConsentData(prev => ({
        ...prev,
        ...newData,
        lastUpdated: new Date()
      }));
    } catch (err) {
      setError('Failed to update consent data');
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    // Simulate initial data fetch
    setIsLoading(true);
    setTimeout(() => {
      setConsentData(defaultConsentData);
      setIsLoading(false);
    }, 1000);
  }, []);

  return (
    <ConsentContext.Provider value={{ consentData, updateConsentData, isLoading, error }}>
      {children}
    </ConsentContext.Provider>
  );
};

export const useConsent = () => {
  const context = useContext(ConsentContext);
  if (context === undefined) {
    throw new Error('useConsent must be used within a ConsentProvider');
  }
  return context;
};