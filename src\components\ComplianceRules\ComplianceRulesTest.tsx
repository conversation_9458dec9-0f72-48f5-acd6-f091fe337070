import React, { useState } from 'react';
import { useComplianceRules } from '../../context/ComplianceRulesContext';
import { ComplianceEvent } from '../../services/complianceRulesService';

/**
 * Test component for compliance rules implementation
 * This component is for development and testing purposes only
 */
const ComplianceRulesTest: React.FC = () => {
  const {
    dpdpRules,
    gdprRules,
    dpdpEnabled,
    gdprEnabled,
    toggleDPDP,
    toggleGDPR,
    evaluateEvent,
    complianceHistory
  } = useComplianceRules();

  const [testResults, setTestResults] = useState<string[]>([]);

  // Test DPDP consent rule
  const testDPDPConsent = async () => {
    // Test with missing consent
    const noConsentEvent: ComplianceEvent = {
      type: 'data_processing_initiated',
      data: {
        consent_status: 'not_granted',
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const noConsentResults = await evaluateEvent(noConsentEvent);

    // Test with valid consent
    const validConsentEvent: ComplianceEvent = {
      type: 'data_processing_initiated',
      data: {
        consent_status: 'granted',
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const validConsentResults = await evaluateEvent(validConsentEvent);

    setTestResults([
      'DPDP Consent Test:',
      `No Consent: ${noConsentResults.length} rules evaluated, ${noConsentResults.filter(r => !r.compliant).length} violations`,
      `Valid Consent: ${validConsentResults.length} rules evaluated, ${validConsentResults.filter(r => !r.compliant).length} violations`,
      '---'
    ]);
  };

  // Test DPDP child data protection
  const testDPDPChildData = async () => {
    // Test with child data without parental consent
    const noParentalConsentEvent: ComplianceEvent = {
      type: 'child_data_processing_requested',
      data: {
        age: 15,
        parental_consent: 'not_verified',
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const noParentalConsentResults = await evaluateEvent(noParentalConsentEvent);

    // Test with valid parental consent
    const validParentalConsentEvent: ComplianceEvent = {
      type: 'child_data_processing_requested',
      data: {
        age: 15,
        parental_consent: 'verified',
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const validParentalConsentResults = await evaluateEvent(validParentalConsentEvent);

    setTestResults(prev => [
      ...prev,
      'DPDP Child Data Test:',
      `No Parental Consent: ${noParentalConsentResults.length} rules evaluated, ${noParentalConsentResults.filter(r => !r.compliant).length} violations`,
      `Valid Parental Consent: ${validParentalConsentResults.length} rules evaluated, ${validParentalConsentResults.filter(r => !r.compliant).length} violations`,
      '---'
    ]);
  };

  // Test GDPR consent
  const testGDPRConsent = async () => {
    // Test with missing consent
    const noConsentEvent: ComplianceEvent = {
      type: 'gdpr_consent_check',
      data: {
        explicit_consent: false,
        informed_consent: false,
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const noConsentResults = await evaluateEvent(noConsentEvent);

    // Test with valid consent
    const validConsentEvent: ComplianceEvent = {
      type: 'gdpr_consent_check',
      data: {
        explicit_consent: true,
        informed_consent: true,
        user_id: '12345',
        data_type: 'personal'
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const validConsentResults = await evaluateEvent(validConsentEvent);

    setTestResults(prev => [
      ...prev,
      'GDPR Consent Test:',
      `No Consent: ${noConsentResults.length} rules evaluated, ${noConsentResults.filter(r => !r.compliant).length} violations`,
      `Valid Consent: ${validConsentResults.length} rules evaluated, ${validConsentResults.filter(r => !r.compliant).length} violations`,
      '---'
    ]);
  };

  // Test GDPR data breach notification
  const testGDPRDataBreach = async () => {
    // Test with late notification
    const lateNotificationEvent: ComplianceEvent = {
      type: 'gdpr_data_breach',
      data: {
        notification_hours: 96, // More than 72 hours
        users_informed: false,
        breach_severity: 'high',
        affected_users: 1000
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const lateNotificationResults = await evaluateEvent(lateNotificationEvent);

    // Test with timely notification
    const timelyNotificationEvent: ComplianceEvent = {
      type: 'gdpr_data_breach',
      data: {
        notification_hours: 48, // Less than 72 hours
        users_informed: true,
        breach_severity: 'high',
        affected_users: 1000
      },
      timestamp: new Date().toISOString(),
      source: 'test'
    };

    const timelyNotificationResults = await evaluateEvent(timelyNotificationEvent);

    setTestResults(prev => [
      ...prev,
      'GDPR Data Breach Test:',
      `Late Notification: ${lateNotificationResults.length} rules evaluated, ${lateNotificationResults.filter(r => !r.compliant).length} violations`,
      `Timely Notification: ${timelyNotificationResults.length} rules evaluated, ${timelyNotificationResults.filter(r => !r.compliant).length} violations`,
      '---'
    ]);
  };

  // Run all tests
  const runAllTests = async () => {
    setTestResults(['Starting tests...']);
    await testDPDPConsent();
    await testDPDPChildData();
    await testGDPRConsent();
    await testGDPRDataBreach();
    setTestResults(prev => [...prev, 'All tests completed']);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-bold mb-4">Compliance Rules Test</h2>

      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Compliance Status</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">DPDP</span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${dpdpEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {dpdpEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Rules: {dpdpRules.length}</p>
            <button
              onClick={() => toggleDPDP(!dpdpEnabled)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${dpdpEnabled ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`}
            >
              {dpdpEnabled ? 'Disable' : 'Enable'}
            </button>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">GDPR</span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${gdprEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {gdprEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Rules: {gdprRules.length}</p>
            <button
              onClick={() => toggleGDPR(!gdprEnabled)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${gdprEnabled ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`}
            >
              {gdprEnabled ? 'Disable' : 'Enable'}
            </button>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Test Compliance Rules</h3>
        <div className="mb-4">
          <button
            onClick={runAllTests}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 mr-4"
          >
            Run All Tests
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-3">DPDP Tests</h4>
            <div className="flex flex-col space-y-2">
              <button
                onClick={testDPDPConsent}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                disabled={!dpdpEnabled}
              >
                Test DPDP Consent
              </button>
              <button
                onClick={testDPDPChildData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                disabled={!dpdpEnabled}
              >
                Test DPDP Child Data
              </button>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-800 mb-3">GDPR Tests</h4>
            <div className="flex flex-col space-y-2">
              <button
                onClick={testGDPRConsent}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                disabled={!gdprEnabled}
              >
                Test GDPR Consent
              </button>
              <button
                onClick={testGDPRDataBreach}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                disabled={!gdprEnabled}
              >
                Test GDPR Data Breach
              </button>
            </div>
          </div>
        </div>
      </div>

      {testResults.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Test Results</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <pre className="whitespace-pre-wrap text-sm">
              {testResults.join('\n')}
            </pre>
          </div>
        </div>
      )}

      <div>
        <h3 className="text-lg font-medium mb-2">Compliance History</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
          {complianceHistory.length === 0 ? (
            <p className="text-gray-500">No compliance events have been evaluated yet.</p>
          ) : (
            <div className="space-y-2">
              {complianceHistory.slice(0, 5).map((result, index) => (
                <div key={index} className="p-2 bg-white rounded border border-gray-200">
                  <div className="flex justify-between">
                    <span className="font-medium">{result.rule_id}</span>
                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${result.compliant ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {result.compliant ? 'Compliant' : 'Non-Compliant'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{new Date(result.timestamp).toLocaleString()}</p>
                </div>
              ))}
              {complianceHistory.length > 5 && (
                <p className="text-sm text-gray-500 text-center">
                  + {complianceHistory.length - 5} more events
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComplianceRulesTest;
