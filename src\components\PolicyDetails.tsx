import React, { useState } from 'react';
import { Policy } from '../types/compliance';
import { formatDate, formatStatus, getStatusColorClass, getStatusBgColorClass } from '../utils/dataFormatters';

interface PolicyDetailsProps {
  policy: Policy;
  onClose: () => void;
}

const PolicyDetails: React.FC<PolicyDetailsProps> = ({ policy, onClose }) => {
  const [activeTab, setActiveTab] = useState<'details' | 'history' | 'actions'>('details');
  
  // Mock data for history and actions
  const historyItems = [
    { date: '2024-01-15', user: '<PERSON>', action: 'Updated compliance status', status: 'compliant' },
    { date: '2024-01-10', user: '<PERSON>', action: 'Review completed', status: 'pending' },
    { date: '2024-01-05', user: '<PERSON>', action: 'Initial assessment', status: 'non_compliant' }
  ];
  
  const actionItems = [
    { id: 1, name: 'Schedule Review', description: 'Schedule a policy review meeting' },
    { id: 2, name: 'Update Documentation', description: 'Update compliance documentation' },
    { id: 3, name: 'Implement Controls', description: 'Implement necessary controls' }
  ];
  
  const statusColorClass = getStatusColorClass(policy.status);
  const statusBgColorClass = getStatusBgColorClass(policy.status);
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
        {/* Header with close button */}
        <div className="flex justify-between items-center border-b p-4">
          <h2 className="text-xl font-semibold">Policy Details</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Policy title and status */}
        <div className="p-4 bg-gray-50">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-bold">{policy.name}</h3>
              <p className="text-gray-600 text-sm">{policy.description}</p>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusBgColorClass} ${statusColorClass}`}>
              {formatStatus(policy.status)}
            </span>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Last updated: {formatDate(policy.lastUpdated)}
          </p>
        </div>
        
        {/* Tabs */}
        <div className="flex border-b">
          <button
            className={`px-4 py-3 font-medium text-sm ${activeTab === 'details' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('details')}
          >
            Details
          </button>
          <button
            className={`px-4 py-3 font-medium text-sm ${activeTab === 'history' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('history')}
          >
            History
          </button>
          <button
            className={`px-4 py-3 font-medium text-sm ${activeTab === 'actions' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('actions')}
          >
            Actions
          </button>
        </div>
        
        {/* Tab Content */}
        <div className="p-4 flex-1 overflow-auto">
          {activeTab === 'details' && (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">POLICY ID</h4>
                <p>{policy.id}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">DESCRIPTION</h4>
                <p>{policy.description}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">STATUS</h4>
                <p className={statusColorClass}>{formatStatus(policy.status)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">LAST UPDATED</h4>
                <p>{formatDate(policy.lastUpdated, true)}</p>
              </div>
              <div className="border-t pt-4 mt-6">
                <h4 className="text-sm font-medium text-gray-500 mb-2">COMPLIANCE CRITERIA</h4>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Regular data protection assessments</li>
                  <li>Staff training completed</li>
                  <li>Documentation updated within 90 days</li>
                  <li>Access controls implemented</li>
                </ul>
              </div>
            </div>
          )}
          
          {activeTab === 'history' && (
            <div className="space-y-4">
              {historyItems.map((item, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0 last:pb-0">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">{formatDate(item.date, true)}</span>
                    <span className={getStatusColorClass(item.status)}>{formatStatus(item.status)}</span>
                  </div>
                  <p className="font-medium">{item.action}</p>
                  <p className="text-sm text-gray-600">By: {item.user}</p>
                </div>
              ))}
            </div>
          )}
          
          {activeTab === 'actions' && (
            <div className="space-y-4">
              {actionItems.map(item => (
                <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <h4 className="font-medium">{item.name}</h4>
                  <p className="text-sm text-gray-600">{item.description}</p>
                  <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                    Start action →
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Footer with actions */}
        <div className="border-t p-4 flex justify-end space-x-3">
          <button 
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Close
          </button>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Edit Policy
          </button>
        </div>
      </div>
    </div>
  );
};

export default PolicyDetails; 