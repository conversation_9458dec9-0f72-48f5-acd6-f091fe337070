import { http, HttpResponse } from 'msw';

// Define default settings for the mock API
const defaultSettings = {
  apiConfiguration: {
    endpoint: 'https://api.compliance.example.com',
    apiKey: 'mock-api-key',
    secretKey: 'mock-secret-key',
    timeout: 30,
    environment: 'development',
    apiVersion: 'v1',
    rateLimits: {
      enabled: true,
      requestsPerMinute: 100
    }
  },
  security: {
    mfaEnabled: true,
    passwordPolicy: {
      minLength: 12,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      maxAge: 90,
      preventReuse: 5,
      preventCommonWords: true,
    },
    sessionTimeout: 30,
    ipWhitelist: [],
    failedLoginAttempts: 5,
    lockoutDuration: 15,
    sslEnabled: true,
    encryptionLevel: 'high',
    deviceTracking: false,
    geofencing: false,
    bruteForceProtection: true,
    anomalyDetection: false,
    ddosProtection: true,
  },
  notifications: {
    email: true,
    slack: false,
    webhook: '',
    frequency: 'daily',
  },
  dataRetention: {
    auditLogs: 365,
    reports: 90,
    backupEnabled: true,
    backupFrequency: 'daily',
  },
  compliance: {
    gdprEnabled: true,
    dpdpEnabled: false,
    hipaaEnabled: false,
    sox: false,
    pci: true,
    iso27001: false,
    ccpa: true,
    nist: false,
    fedramp: false,
  },
  externalServices: {
    openai: {
      apiKey: 'mock-openai-api-key',
      model: 'gpt-4',
      maxTokens: 2000
    },
    aws: {
      accessKeyId: 'mock-aws-access-key',
      secretAccessKey: 'mock-aws-secret-key',
      region: 'us-east-1'
    },
    azure: {
      tenantId: 'mock-azure-tenant-id',
      clientId: 'mock-azure-client-id',
      clientSecret: 'mock-azure-client-secret'
    }
  },
  auditSettings: {
    enableAuditLogging: true,
    logRetentionDays: 90,
    detailedLogging: true,
    sensitiveDataMasking: true
  },
  riskManagement: {
    enableRiskAssessment: true,
    riskThreshold: 75,
    automaticMitigation: false,
    notifyOnHighRisk: true
  },
  integrations: {
    jira: {
      enabled: false,
      apiKey: '',
      projectKey: ''
    }
  }
};

// Store settings in memory for the mock API
let settings = { ...defaultSettings };

export const handlers = [
  // GET /api/settings
  http.get('/api/settings', () => {
    console.log('[MSW] Returning mock settings');
    return HttpResponse.json(settings);
  }),

  // Flyer Verification API endpoints
  http.post('/api/flyer-verification/scan', async () => {
    console.log('[MSW] Document scan request received');

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock successful scan result
    const mockResult = {
      id: `SCAN-${Date.now()}`,
      documentType: 'passport',
      isValid: Math.random() > 0.1, // 90% success rate
      confidence: Math.random() * 15 + 85, // 85-100%
      extractedData: {
        fullName: 'JOHN MICHAEL SMITH',
        nationality: 'BRITISH',
        documentNumber: 'P123456789',
        expiryDate: '2029-12-15',
        issueDate: '2019-12-15',
        placeOfBirth: 'LONDON, UK',
        gender: 'M',
        dateOfBirth: '1985-03-15'
      },
      verificationStatus: Math.random() > 0.1 ? 'approved' : 'denied',
      riskLevel: Math.random() > 0.8 ? 'medium' : 'low',
      processingTime: 2.3,
      vendorInfo: {
        vendor: 'DocumentVerify Pro',
        apiVersion: 'v2.1',
        processingNode: 'EU-WEST-1'
      }
    };

    return HttpResponse.json(mockResult);
  }),

  http.get('/api/flyer-verification/history', () => {
    console.log('[MSW] Verification history request');
    return HttpResponse.json({
      records: [],
      total: 0
    });
  }),

  http.get('/api/flyer-verification/restrictions', () => {
    console.log('[MSW] Travel restrictions request');

    // Return mock travel restrictions data
    const mockRestrictions = [
      {
        id: 'TR-0001',
        country: 'United States',
        nationality: 'Iranian',
        restrictionType: 'banned',
        severity: 'critical',
        description: 'Entry completely prohibited due to Executive Order - Iranian nationals banned from US travel',
        effectiveDate: '2020-01-01',
        lastUpdated: new Date().toISOString(),
        updatedBy: 'System Administrator',
        isActive: true
      },
      {
        id: 'TR-0002',
        country: 'United States',
        nationality: 'Syrian',
        restrictionType: 'banned',
        severity: 'critical',
        description: 'Entry completely prohibited due to security concerns - Syrian nationals banned from US travel',
        effectiveDate: '2020-01-01',
        lastUpdated: new Date().toISOString(),
        updatedBy: 'System Administrator',
        isActive: true
      },
      {
        id: 'TR-0003',
        country: 'United States',
        nationality: 'North Korean',
        restrictionType: 'banned',
        severity: 'critical',
        description: 'Entry completely prohibited due to diplomatic restrictions - North Korean nationals banned from US travel',
        effectiveDate: '2020-01-01',
        lastUpdated: new Date().toISOString(),
        updatedBy: 'System Administrator',
        isActive: true
      },
      {
        id: 'TR-0004',
        country: 'United States',
        nationality: 'Indian',
        restrictionType: 'visa_required',
        severity: 'medium',
        description: 'Valid visa required for entry - Indian nationals must have appropriate visa for US travel',
        effectiveDate: '2020-01-01',
        lastUpdated: new Date().toISOString(),
        updatedBy: 'System Administrator',
        isActive: true
      }
    ];

    return HttpResponse.json(mockRestrictions);
  }),

  http.post('/api/flyer-verification/restrictions/check', async ({ request }) => {
    console.log('[MSW] Travel restrictions check request');

    const body = await request.json() as { nationality: string; destinationCountry: string };
    const { nationality, destinationCountry } = body;

    // Check for restrictions based on nationality and destination
    const bannedNationalities = ['iranian', 'syrian', 'north korean', 'afghan'];
    const isRestricted = bannedNationalities.includes(nationality.toLowerCase()) &&
                        destinationCountry.toLowerCase() === 'united states';

    return HttpResponse.json({
      isRestricted,
      restrictions: isRestricted ? [{
        country: destinationCountry,
        nationality: nationality,
        restrictionType: 'banned',
        severity: 'critical',
        description: `${nationality} nationals are prohibited from entering ${destinationCountry}`
      }] : [],
      riskLevel: isRestricted ? 'critical' : 'low'
    });
  }),

  http.get('/api/flyer-verification/audit', () => {
    console.log('[MSW] Audit trail request');
    return HttpResponse.json({
      entries: [],
      total: 0
    });
  }),

  http.get('/api/flyer-verification/analytics/stats', () => {
    console.log('[MSW] Analytics stats request');
    return HttpResponse.json({
      totalVerifications: 1247,
      successfulVerifications: 1198,
      failedVerifications: 23,
      pendingVerifications: 26,
      accuracyRate: 96.1,
      averageProcessingTime: 2.4,
      riskDistribution: {
        low: 85,
        medium: 12,
        high: 3
      }
    });
  }),

  // PUT /api/settings
  http.put('/api/settings', async ({ request }) => {
    try {
      const updatedSettings = await request.json() as Record<string, unknown>;
      console.log('[MSW] Updating settings with:', updatedSettings);

      // Handle nested updates
      if (updatedSettings && typeof updatedSettings === 'object' && updatedSettings.compliance) {
        settings.compliance = {
          ...settings.compliance,
          ...updatedSettings.compliance
        };
      } else if (updatedSettings && typeof updatedSettings === 'object') {
        settings = { ...settings, ...updatedSettings };
      }

      return HttpResponse.json(settings);
    } catch (error) {
      console.error('[MSW] Error processing settings update:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid request data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  // POST /compliance/logs - For logging compliance results
  http.post('/compliance/logs', async ({ request }) => {
    try {
      const logData = await request.json();
      console.log('[MSW] Logging compliance result:', logData);
      return HttpResponse.json({ success: true, id: `log-${Date.now()}` });
    } catch (error) {
      console.error('[MSW] Error logging compliance result:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid log data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  // Report API endpoints
  http.post('/api/reports', async ({ request }) => {
    try {
      const reportData = await request.json() as Record<string, unknown>;
      console.log('[MSW] Creating report:', reportData);
      return HttpResponse.json({
        id: `report-${Date.now()}`,
        ...(typeof reportData === 'object' && reportData !== null ? reportData : {}),
        createdAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('[MSW] Error creating report:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid report data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  http.delete('/api/reports/:reportId', ({ params }) => {
    const { reportId } = params;
    console.log(`[MSW] Deleting report: ${reportId}`);
    return HttpResponse.json({ success: true });
  }),

  http.get('/api/reports/:reportId/download', ({ params }) => {
    const { reportId } = params;
    console.log(`[MSW] Downloading report: ${reportId}`);

    // Return a mock PDF file (just some text in this case)
    return new HttpResponse('Mock PDF content for report', {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="report-${reportId}.pdf"`
      }
    });
  })
];
