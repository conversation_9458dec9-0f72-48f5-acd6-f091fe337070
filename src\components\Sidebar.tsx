import React, { useEffect, useState } from 'react';
import { Home, LayoutDashboard, Settings, Users, Shield, PieChart, FileText, BarChart3 } from 'lucide-react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { HiChevronLeft, HiChevronRight } from 'react-icons/hi';
import { Link as CustomLink } from './Link';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '../context/ThemeContext';

interface SidebarProps {
  onPageChange: (page: string) => void;
  currentPage: string;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPage, onPageChange }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const { mode } = useTheme();

  useEffect(() => {
    if (!currentPage) {
      onPageChange('home');
    }
  }, [currentPage, onPageChange]);

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  const handleLogoClick = () => {
    onPageChange('home');
    navigate('/');
  };

  return (
    <div
      className={`bg-surface min-h-screen flex flex-col shadow-md relative z-10
        transition-all duration-300 ease-in-out ${isExpanded ? 'w-72' : 'w-20'} overflow-hidden
        text-text border-r border-border dark:shadow-none`}
      style={{ willChange: 'width, transform', transformOrigin: 'left' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Logo section */}
      <div className="flex flex-col border-b border-border">
        <div className="py-4 px-2 flex justify-center items-center">
          <div
            onClick={handleLogoClick}
            className={`cursor-pointer transition-all duration-300 ease-in-out flex justify-center items-center
              ${isExpanded ? 'w-full' : 'w-10 h-10'}`}
            aria-label="Go to home page"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleLogoClick();
              }
            }}
          >
            {isExpanded ? (
              <h1 className="text-3xl font-bold transition-all duration-300 transform hover:translate-x-1">
                <span style={{ color: '#A6D933', fontFamily: 'Inter, system-ui, sans-serif' }}>PRAE</span>
                <span className="text-text" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>FERRE</span>
              </h1>
            ) : (
              <div className="w-8 h-8 relative transform transition-transform duration-300 hover:scale-105 mx-auto">
                {/* Using the existing thumbnail.png image */}
                <img
                  src="/thumbnail.png"
                  alt="Praeferre Logo"
                  className="w-full h-full object-contain drop-shadow-sm"
                  style={{
                    maxWidth: '32px',
                    maxHeight: '32px',
                    filter: mode === 'dark' ? 'brightness(1.1)' : 'none'
                  }}
                  draggable="false"
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-between px-4 pb-4">
          {isExpanded && <ThemeToggle />}
          <button
            onClick={toggleSidebar}
            className={`p-2 rounded-full transition-all duration-250 shadow-sm
              ${isExpanded
                ? 'ml-auto'
                : 'mx-auto'}
              ${isHovered
                ? 'bg-primary/10 hover:bg-primary/20 text-primary'
                : 'hover:bg-primary/10 text-text-secondary hover:text-primary'}`}
            aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
          >
            {isExpanded ? <HiChevronLeft size={20} /> : <HiChevronRight size={20} />}
          </button>
        </div>
      </div>

      {/* Navigation section */}
      <nav className={`space-y-1 flex-1 pt-4 ${isExpanded ? 'px-4' : 'px-2'}`}>
        <div className={`mb-2 ${isExpanded ? 'px-4' : 'text-center'}`}>
          <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">
            {isExpanded ? 'Main Navigation' : ''}
          </p>
        </div>
        {[
          { name: 'Home', icon: <Home size={20} />, path: 'home' },
          { name: 'Enterprise Dashboard', icon: <Shield size={20} />, path: 'enterprise' },
          { name: 'Privacy Dashboard', icon: <PieChart size={20} />, path: 'privacy' },
          // { name: 'Flyer Verification', icon: <UserCheck size={20} />, path: 'flyer-verification', isRoute: true }, // Temporarily hidden
          { name: 'Compliance Rules', icon: <Shield size={20} />, path: 'complianceRules' },
          { name: 'Compliance Reports', icon: <FileText size={20} />, path: 'reports' },
          { name: 'Configurations', icon: <Settings size={20} />, path: 'configurations' },
          { name: 'Consumer Interface', icon: <Users size={20} />, path: 'consumer' }
        ].map((item) => {
          // Determine if this item is active based on route or currentPage
          const isActive = item.isRoute
            ? location.pathname === `/${item.path}`
            : currentPage === item.path;

          return (
            <CustomLink
              key={item.path}
              href="#"
              icon={React.cloneElement(item.icon, {
                className: `transition-colors duration-250 ${isActive ? 'text-primary' : 'text-text-secondary'}`
              })}
              active={isActive}
              onClick={() => {
                if (item.isRoute) {
                  navigate(`/${item.path}`);
                } else {
                  // If we're currently on a route (not home), navigate to home first
                  if (location.pathname !== '/') {
                    navigate('/');
                  }
                  onPageChange(item.path);
                }
              }}
              className={`group py-3 px-4 rounded-lg transition-all duration-250 flex items-center
                ${isActive
                  ? 'bg-primary/10 text-primary font-medium'
                  : 'hover:bg-primary/5 text-text hover:text-primary'}
                ${!isExpanded ? 'justify-center w-full mx-auto' : ''}`}
            >
              {isExpanded && (
                <span className="transition-all duration-250 group-hover:translate-x-1 ml-3 truncate-1">
                  {item.name}
                </span>
              )}
            </CustomLink>
          );
        })}
      </nav>

      {/* Profile section */}
      <div className={`mt-auto pt-6 border-t border-border ${isExpanded ? 'px-4' : 'px-2'}`}>
        <div className={`flex items-center p-3 rounded-lg hover:bg-primary/5 transition-all duration-250 cursor-pointer
          ${!isExpanded ? 'justify-center' : ''}`}
          role="button"
          tabIndex={0}
          aria-label="User profile"
        >
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-light to-primary text-white flex items-center justify-center shadow-md flex-shrink-0">
            <span className="font-medium text-sm">JD</span>
          </div>
          {isExpanded && (
            <div className="ml-3 overflow-hidden">
              <p className="text-sm font-medium text-text truncate-1">John Doe</p>
              <p className="text-xs text-text-secondary truncate-1">Administrator</p>
            </div>
          )}
        </div>
        {!isExpanded && (
          <div className="flex justify-center mt-4 mb-4">
            <ThemeToggle />
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;