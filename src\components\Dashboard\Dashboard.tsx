import React, { useEffect, useRef, useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import styles from './Dashboard.module.css';
import GovernmentDepartments from './GovernmentDepartments';
import { useTheme } from '../../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const Dashboard = React.memo(() => {
  const chartRef = useRef(null);
  const { mode } = useTheme();
  const navigate = useNavigate();

  useEffect(() => {
    // Log to verify component mounting
    console.log('Dashboard mounted');
  }, []);

  // Navigation handlers for each section
  const handleGreenLevelClick = () => {
    navigate('/green-level');
  };

  const handleAmberLevelClick = () => {
    navigate('/amber-level');
  };

  const handleRedLevelClick = () => {
    navigate('/red-level');
  };

  const handleDataPrivacyClick = () => {
    navigate('/data-privacy-acknowledgement');
  };

  const lineChartData = useMemo(() => ({
    labels: ['Nov-23', 'Dec-23', 'Jan-24', 'Feb-24', 'Mar-24', 'Apr-24', 'May-24', 'Jun-24'],
    datasets: [
      {
        label: 'Red Data',
        data: [0.2, 1.8, 1.5, 1.0, 0.2, 1.8, 1.7, 2.8],
        borderColor: '#ff3333',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Amber Data',
        data: [1.8, 1.2, 0.9, 1.5, 2.0, 1.5, 0.5, 1.6],
        borderColor: '#ffcc00',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Green Data',
        data: [0.2, 0.5, 0.5, 0.5, 2.4, 1.6, 1.1, 0.9],
        borderColor: '#33cc99',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
    ],
  }), []);

  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Consent management trends',
        align: 'center' as const,
        font: {
          size: 16,
          family: 'Arial, sans-serif',
          weight: 'normal' as const,
        },
        padding: {
          bottom: 30,
        },
        color: mode === 'dark' ? '#f9fafb' : '#111827',
      },
      tooltip: {
        backgroundColor: mode === 'dark' ? '#1f2937' : '#ffffff',
        titleColor: mode === 'dark' ? '#f9fafb' : '#111827',
        bodyColor: mode === 'dark' ? '#e5e7eb' : '#374151',
        borderColor: mode === 'dark' ? '#374151' : '#e5e7eb',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        min: 0,
        max: 4,
        ticks: {
          stepSize: 1,
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          padding: 8,
        },
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#e5e7eb',
          drawBorder: false,
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        border: {
          display: false,
        },
        title: {
          display: true,
          text: 'Consent Rate',
          font: {
            size: 12,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#e2e8f0' : '#4b5563',
          padding: { top: 0, bottom: 10 },
        },
      },
      x: {
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#e5e7eb',
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        ticks: {
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          padding: 5,
        },
        border: {
          display: false,
        },
      },
    },
  }), [mode]);

  return (
    <div className="flex-1 bg-background dashboard-content">
      <div className="p-8">
        <div className="mb-8">
          <h1 className="text-sm text-text-secondary">Customer privacy management portal</h1>
          <h2 className="text-2xl font-bold text-text">Privacy Dashboard</h2>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          <div className="w-full lg:w-2/3 bg-card rounded-lg p-6 shadow-sm dashboard-card">
            {/* Compliance Trends Chart - matching Enterprise Dashboard exactly */}
            <h3 className="text-lg font-semibold mb-4 text-text">Compliance Trends</h3>
            <div style={{ height: '400px' }}>
              <Line data={lineChartData} options={chartOptions} />
            </div>
          </div>

          <div className="w-full lg:w-1/3 flex flex-col items-center bg-card rounded-lg p-6 shadow-sm dashboard-card">
            <div className="flex space-x-8 mb-8">
              <div className="flex flex-col items-center dashboard-element cursor-pointer hover:opacity-80 transition-opacity"
                   onClick={handleGreenLevelClick}>
                <div className="w-24 h-24 rounded-full border-8 flex items-center justify-center bg-surface"
                     style={{ borderColor: 'var(--dashboard-green)' }}>
                  <span className="text-2xl font-bold text-text">31%</span>
                </div>
                <p className="mt-2 text-center text-text">Green Level<br/><span className="text-text-secondary">Data Consent</span></p>
              </div>

              <div className="flex flex-col items-center dashboard-element cursor-pointer hover:opacity-80 transition-opacity"
                   onClick={handleAmberLevelClick}>
                <div className="w-24 h-24 rounded-full border-8 flex items-center justify-center bg-surface"
                     style={{ borderColor: 'var(--dashboard-amber)' }}>
                  <span className="text-2xl font-bold text-text">58%</span>
                </div>
                <p className="mt-2 text-center text-text">Amber Level<br/><span className="text-text-secondary">Data Consent</span></p>
              </div>

              <div className="flex flex-col items-center dashboard-element cursor-pointer hover:opacity-80 transition-opacity"
                   onClick={handleRedLevelClick}>
                <div className="w-24 h-24 rounded-full border-8 flex items-center justify-center bg-surface"
                     style={{ borderColor: 'var(--dashboard-red)' }}>
                  <span className="text-2xl font-bold text-text">11%</span>
                </div>
                <p className="mt-2 text-center text-text">Red Level<br/><span className="text-text-secondary">Data Consent</span></p>
              </div>
            </div>

            <div className="mb-8 dashboard-element cursor-pointer hover:opacity-80 transition-opacity"
                 onClick={handleDataPrivacyClick}>
              <div className="w-40 h-40 rounded-full border-8 flex items-center justify-center bg-surface"
                   style={{ borderColor: 'var(--dashboard-purple)' }}>
                <div className="text-center">
                  <p className="font-bold text-text">Data Privacy<br/>Acknowledgement</p>
                  <p className="text-success text-xl">100%</p>
                </div>
              </div>
            </div>

            <button className="px-4 py-2 rounded transition-colors dashboard-element"
                    style={{
                      backgroundColor: 'var(--dashboard-button-bg)',
                      color: 'var(--dashboard-button-text)'
                    }}>
              Download Report
            </button>
          </div>
        </div>
      </div>

      <div className="mt-8 dashboard-element">
        <GovernmentDepartments />
      </div>
    </div>
  );
});

export default Dashboard;