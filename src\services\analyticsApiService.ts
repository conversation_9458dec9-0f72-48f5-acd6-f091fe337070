import { BaseApiService } from './api';

// API Types
export interface ExecutiveDashboard {
  overview: {
    totalDataSubjects: number;
    complianceScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    activeViolations: number;
    lastUpdated: string;
  };
  kpis: Record<string, {
    current: number;
    target: number;
    trend: 'improving' | 'declining' | 'stable';
    change: string;
    unit?: string;
  }>;
  trends: Record<string, Array<{
    date: string;
    value: number;
  }>>;
  alerts: Array<{
    id: string;
    severity: string;
    category: string;
    message: string;
    timestamp: string;
  }>;
}

export interface OperationalDashboard {
  systemHealth: {
    overall: 'healthy' | 'warning' | 'critical';
    services: Array<{
      name: string;
      status: 'healthy' | 'warning' | 'critical';
      uptime: number;
      responseTime: number;
      lastCheck: string;
    }>;
  };
  performance: {
    apiRequests: {
      total: number;
      successful: number;
      failed: number;
      averageResponseTime: number;
    };
    dataProcessing: {
      recordsProcessed: number;
      processingRate: number;
      errorRate: number;
    };
  };
  realTimeMetrics: {
    activeUsers: number;
    currentLoad: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}

export interface PrivacyMetrics {
  summary: {
    totalDataSubjects: number;
    greenLevel: number;
    amberLevel: number;
    redLevel: number;
    complianceScore: number;
  };
  trends: {
    consentLevels: Array<{
      date: string;
      green: number;
      amber: number;
      red: number;
    }>;
    newConsents: Array<{
      date: string;
      count: number;
      source: string;
    }>;
    withdrawals: Array<{
      date: string;
      count: number;
      reason: string;
    }>;
  };
  breakdown: {
    byConsentType: Record<string, number>;
    byRegion: Record<string, number>;
    bySource: Record<string, number>;
  };
}

export interface DataSubjectRequestAnalytics {
  summary: {
    totalRequests: number;
    completedRequests: number;
    pendingRequests: number;
    rejectedRequests: number;
    averageResolutionTime: number;
    complianceRate: number;
  };
  trends: Array<{
    date: string;
    access: number;
    rectification: number;
    erasure: number;
    portability: number;
  }>;
  performance: {
    resolutionTimes: Record<string, number>;
    complianceRates: Record<string, number>;
  };
}

export interface CustomReport {
  name: string;
  description: string;
  type: 'custom';
  dataSource: string;
  period: {
    type: 'fixed' | 'rolling';
    duration?: string;
    start?: string;
    end?: string;
  };
  metrics: string[];
  filters: Record<string, any>;
  format: 'pdf' | 'excel' | 'csv';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    dayOfWeek?: string;
    dayOfMonth?: number;
    time?: string;
  };
  recipients?: string[];
  visualizations?: Array<{
    type: 'line_chart' | 'bar_chart' | 'pie_chart' | 'table';
    metric: string;
    title: string;
    options?: Record<string, any>;
  }>;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  metrics: string[];
  defaultPeriod: string;
  format: string;
  estimatedGenerationTime: number;
}

export interface DataExportRequest {
  dataType: string;
  period: {
    start: string;
    end: string;
  };
  format: 'csv' | 'json' | 'xlsx';
  compression?: 'gzip' | 'zip';
  includeMetadata: boolean;
  filters?: Record<string, any>;
}

export interface DataExport {
  exportId: string;
  status: 'processing' | 'completed' | 'failed';
  estimatedCompletion?: string;
  downloadUrl?: string;
  fileSize?: number;
  recordCount?: number;
  createdAt: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class AnalyticsApiService extends BaseApiService {
  constructor() {
    super('/api/analytics');
  }

  // Dashboard Analytics
  async getExecutiveDashboard(): Promise<ExecutiveDashboard> {
    return this.get<ExecutiveDashboard>('/dashboard/executive');
  }

  async getOperationalDashboard(params: {
    timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
    department?: string;
    system?: string;
  } = {}): Promise<OperationalDashboard> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get<OperationalDashboard>(`/dashboard/operational?${queryParams.toString()}`);
  }

  // Privacy Analytics
  async getPrivacyMetrics(params: {
    period?: '7d' | '30d' | '90d' | '1y';
    granularity?: 'hourly' | 'daily' | 'weekly' | 'monthly';
    consentType?: string;
    region?: string;
  } = {}): Promise<PrivacyMetrics> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get<PrivacyMetrics>(`/privacy/metrics?${queryParams.toString()}`);
  }

  async getDataSubjectRequestAnalytics(params: {
    period?: '7d' | '30d' | '90d' | '1y';
    type?: 'access' | 'rectification' | 'erasure' | 'portability';
    status?: 'pending' | 'completed' | 'rejected';
  } = {}): Promise<DataSubjectRequestAnalytics> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get<DataSubjectRequestAnalytics>(`/privacy/dsr?${queryParams.toString()}`);
  }

  // NOC Analytics
  async getNetworkPerformanceAnalytics(params: {
    period?: '1h' | '6h' | '24h' | '7d' | '30d';
    sourceType?: string;
    region?: string;
  } = {}): Promise<{
    summary: {
      averageThroughput: number;
      averageLatency: number;
      packetLoss: number;
      uptime: number;
    };
    trends: Record<string, Array<{
      timestamp: string;
      value: number;
      unit: string;
    }>>;
    breakdown: Record<string, Record<string, any>>;
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get(`/noc/performance?${queryParams.toString()}`);
  }

  async getAlertAnalytics(params: {
    period?: '1h' | '6h' | '24h' | '7d' | '30d';
    severity?: 'low' | 'medium' | 'high' | 'critical';
  } = {}): Promise<{
    summary: {
      totalAlerts: number;
      resolvedAlerts: number;
      activeAlerts: number;
      suppressedAlerts: number;
      averageResolutionTime: number;
      falsePositiveRate: number;
    };
    trends: Array<{
      date: string;
      critical: number;
      high: number;
      medium: number;
      low: number;
    }>;
    breakdown: Record<string, Record<string, number>>;
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get(`/noc/alerts?${queryParams.toString()}`);
  }

  // Flight Operations Analytics removed

  // Compliance Analytics
  async getComplianceTrends(params: {
    period?: '7d' | '30d' | '90d' | '1y';
    framework?: 'gdpr' | 'dpdp' | 'all';
  } = {}): Promise<{
    summary: {
      overallScore: number;
      violationCount: number;
      resolutionRate: number;
      averageResolutionTime: number;
    };
    trends: Record<string, Array<{
      date: string;
      gdpr?: number;
      dpdp?: number;
      overall?: number;
      count?: number;
      severity?: string;
    }>>;
    breakdown: Record<string, Record<string, any>>;
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get(`/compliance/trends?${queryParams.toString()}`);
  }

  // Custom Reports
  async createCustomReport(data: CustomReport): Promise<{
    reportId: string;
    status: string;
    estimatedCompletion: string;
  }> {
    return this.post('/reports/custom', data);
  }

  async getReportTemplates(params: {
    category?: string;
  } = {}): Promise<{
    templates: ReportTemplate[];
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get(`/reports/templates?${queryParams.toString()}`);
  }

  async generateReportFromTemplate(templateId: string, data: {
    period: {
      start: string;
      end: string;
    };
    filters?: Record<string, any>;
    format?: 'pdf' | 'excel' | 'csv';
    includeRawData?: boolean;
  }): Promise<{
    reportId: string;
    status: string;
    estimatedCompletion: string;
  }> {
    return this.post(`/reports/generate/${templateId}`, data);
  }

  async getReport(reportId: string): Promise<{
    reportId: string;
    status: string;
    progress?: number;
    downloadUrl?: string;
    error?: string;
  }> {
    return this.get(`/reports/${reportId}`);
  }

  async downloadReport(reportId: string): Promise<Blob> {
    const response = await this.api.get(`/reports/${reportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Data Export
  async exportData(data: DataExportRequest): Promise<DataExport> {
    return this.post('/export', data);
  }

  async getExportStatus(exportId: string): Promise<DataExport> {
    return this.get(`/exports/${exportId}`);
  }

  async downloadExport(exportId: string): Promise<Blob> {
    const response = await this.api.get(`/exports/${exportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getExports(params: {
    page?: number;
    limit?: number;
    status?: 'processing' | 'completed' | 'failed';
  } = {}): Promise<PaginatedResponse<DataExport>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get(`/exports?${queryParams.toString()}`);
  }
}

export const analyticsApiService = new AnalyticsApiService();
