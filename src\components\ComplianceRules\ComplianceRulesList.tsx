import React, { useState } from 'react';
import { useComplianceRules } from '../../context/ComplianceRulesContext';
import { DPDPRule, GDPRRule } from '../../services/complianceRulesService';

const ComplianceRulesList: React.FC = () => {
  const { dpdpRules, gdprRules, dpdpEnabled, gdprEnabled } = useComplianceRules();
  const [activeTab, setActiveTab] = useState<'dpdp' | 'gdpr'>('gdpr');

  // Function to render DPDP rules
  const renderDPDPRules = () => {
    if (!dpdpEnabled) {
      return (
        <div className="p-6 text-center text-text-secondary">
          DPDP compliance is currently disabled. Enable it in the configuration settings.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {dpdpRules.map((rule) => (
          <div key={rule.rule_id} className="bg-surface p-4 rounded-lg shadow-sm border border-border">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-medium text-text">{rule.rule_id}</h3>
                <p className="text-sm text-text-secondary mt-1">{rule.description}</p>
              </div>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(rule.severity)}`}>
                {rule.severity}
              </span>
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-text">Trigger</h4>
                <p className="text-sm text-text-secondary mt-1">{rule.trigger}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-text">Action</h4>
                <p className="text-sm text-text-secondary mt-1">{rule.action}</p>
              </div>
            </div>

            {rule.conditions.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-text">Conditions</h4>
                <ul className="mt-1 space-y-1">
                  {rule.conditions.map((condition, index) => (
                    <li key={index} className="text-sm text-text-secondary">
                      {condition.field} {condition.operator} {condition.value}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Function to render GDPR rules
  const renderGDPRRules = () => {
    if (!gdprEnabled) {
      return (
        <div className="p-6 text-center text-text-secondary">
          GDPR compliance is currently disabled. Enable it in the configuration settings.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {gdprRules.map((rule) => (
          <div key={rule.id} className="bg-surface p-4 rounded-lg shadow-sm border border-border">
            <div>
              <h3 className="text-lg font-medium text-text">{rule.id}: {rule.name}</h3>
              <p className="text-sm text-text-secondary mt-1">{rule.description}</p>
            </div>

            <div className="mt-4">
              <h4 className="text-sm font-medium text-text">Conditions</h4>
              <ul className="mt-1 space-y-1">
                {rule.conditions.map((condition, index) => (
                  <li key={index} className="text-sm text-text-secondary">
                    • {condition}
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-4">
              <h4 className="text-sm font-medium text-text">Actions</h4>
              <ul className="mt-1 space-y-1">
                {rule.actions.map((action, index) => (
                  <li key={index} className="text-sm text-text-secondary">
                    • {action}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Helper function to get color based on severity
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <div className="bg-background p-6 rounded-lg border border-border">
      <h2 className="text-xl font-bold text-text mb-6">Compliance Rules</h2>

      {/* Tabs */}
      <div className="border-b border-border mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('gdpr')}
            className={`${
              activeTab === 'gdpr'
                ? 'border-primary text-primary'
                : 'border-transparent text-text-secondary hover:text-text hover:border-border'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            GDPR Rules
          </button>
          <button
            onClick={() => setActiveTab('dpdp')}
            className={`${
              activeTab === 'dpdp'
                ? 'border-primary text-primary'
                : 'border-transparent text-text-secondary hover:text-text hover:border-border'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            DPDP Rules
          </button>
        </nav>
      </div>

      {/* Content */}
      <div>
        {activeTab === 'dpdp' ? renderDPDPRules() : renderGDPRRules()}
      </div>
    </div>
  );
};

export default ComplianceRulesList;
