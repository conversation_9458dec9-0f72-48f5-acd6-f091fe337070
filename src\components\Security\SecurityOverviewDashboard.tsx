import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Shield,
  AlertTriangle,
  Eye,
  Search,
  Filter,
  RefreshCw,
  Download,
  Settings,
  Clock,
  Users,
  Building,
  Target,
  Activity,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Lock,
  Unlock,
  Key,
  FileText,
  Bell,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Layers,
  History,
  User,
  MapPin,
  Calendar,
  Play,
  Pause,
  RotateCcw,
  ExternalLink,
  Bug,
  Wifi,
  Server,
  Database,
  Globe
} from 'lucide-react';

// Security Types
interface SecurityThreat {
  id: string;
  name: string;
  type: 'malware' | 'phishing' | 'ddos' | 'intrusion' | 'data_breach' | 'insider_threat';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'investigating' | 'contained' | 'resolved' | 'false_positive';
  detectedAt: Date;
  source: string;
  targetAsset: string;
  affectedSystems: string[];
  riskScore: number; // 1-100
  investigator?: string;
  resolutionTime?: number; // minutes
  mitigationActions: Array<{
    id: string;
    action: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignee: string;
    completedAt?: Date;
  }>;
}

interface SecurityMetric {
  id: string;
  name: string;
  category: 'access_control' | 'network_security' | 'data_protection' | 'endpoint_security' | 'compliance';
  currentValue: number;
  targetValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'compliant' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    incidents: number;
  }>;
}

interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  category: 'security_breach' | 'policy_violation' | 'system_compromise' | 'data_leak' | 'unauthorized_access';
  reportedAt: Date;
  reportedBy: string;
  assignedTo?: string;
  resolvedAt?: Date;
  affectedAssets: string[];
  responseActions: Array<{
    id: string;
    action: string;
    timestamp: Date;
    performer: string;
    result: string;
  }>;
}

interface SecurityOverviewData {
  overview: {
    totalThreats: number;
    activeThreats: number;
    resolvedThreats: number;
    criticalThreats: number;
    averageResolutionTime: number; // minutes
    securityScore: number; // 1-100
    lastScan: Date;
    nextScheduledScan: Date;
    systemsMonitored: number;
    alertsLast24h: number;
  };
  threats: SecurityThreat[];
  metrics: SecurityMetric[];
  incidents: SecurityIncident[];
  complianceStatus: {
    iso27001: { score: number; status: string; lastAudit: Date };
    nist: { score: number; status: string; lastAudit: Date };
    gdpr: { score: number; status: string; lastAudit: Date };
    sox: { score: number; status: string; lastAudit: Date };
  };
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    activeScans: number;
    pendingInvestigations: number;
  };
}

interface SecurityOverviewDashboardProps {
  className?: string;
}

// Mock data generator
const generateSecurityOverviewData = (): SecurityOverviewData => {
  const threats: SecurityThreat[] = [
    {
      id: 'threat-001',
      name: 'Suspicious Login Attempts',
      type: 'intrusion',
      severity: 'high',
      status: 'investigating',
      detectedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      source: '*************',
      targetAsset: 'Admin Portal',
      affectedSystems: ['Web Server', 'Database'],
      riskScore: 85,
      investigator: 'Security Team',
      mitigationActions: [
        {
          id: 'mit-001',
          action: 'Block suspicious IP addresses',
          status: 'completed',
          assignee: 'Network Admin',
          completedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
        },
        {
          id: 'mit-002',
          action: 'Review access logs',
          status: 'in_progress',
          assignee: 'Security Analyst'
        }
      ]
    },
    {
      id: 'threat-002',
      name: 'Malware Detection on Endpoint',
      type: 'malware',
      severity: 'critical',
      status: 'contained',
      detectedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      source: 'Endpoint-PC-047',
      targetAsset: 'Employee Workstation',
      affectedSystems: ['Workstation', 'File Server'],
      riskScore: 95,
      investigator: 'Incident Response Team',
      resolutionTime: 180,
      mitigationActions: [
        {
          id: 'mit-003',
          action: 'Isolate infected machine',
          status: 'completed',
          assignee: 'IT Support',
          completedAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
        },
        {
          id: 'mit-004',
          action: 'Run full system scan',
          status: 'completed',
          assignee: 'Security Team',
          completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
      ]
    },
    {
      id: 'threat-003',
      name: 'Phishing Email Campaign',
      type: 'phishing',
      severity: 'medium',
      status: 'resolved',
      detectedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
      source: 'External Email',
      targetAsset: 'Email System',
      affectedSystems: ['Email Server'],
      riskScore: 65,
      investigator: 'Email Security Team',
      resolutionTime: 120,
      mitigationActions: [
        {
          id: 'mit-005',
          action: 'Block sender domain',
          status: 'completed',
          assignee: 'Email Admin',
          completedAt: new Date(Date.now() - 7 * 60 * 60 * 1000)
        }
      ]
    },
    {
      id: 'threat-004',
      name: 'Unauthorized Data Access',
      type: 'data_breach',
      severity: 'high',
      status: 'detected',
      detectedAt: new Date(Date.now() - 30 * 60 * 1000),
      source: 'Internal Network',
      targetAsset: 'Customer Database',
      affectedSystems: ['Database Server', 'API Gateway'],
      riskScore: 88,
      mitigationActions: [
        {
          id: 'mit-006',
          action: 'Review database access logs',
          status: 'pending',
          assignee: 'DBA Team'
        }
      ]
    }
  ];

  const metrics: SecurityMetric[] = [
    {
      id: 'metric-001',
      name: 'Password Compliance Rate',
      category: 'access_control',
      currentValue: 94.2,
      targetValue: 98.0,
      unit: '%',
      trend: 'up',
      status: 'warning',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 90 + Math.random() * 8,
        incidents: Math.floor(Math.random() * 5)
      }))
    },
    {
      id: 'metric-002',
      name: 'Firewall Block Rate',
      category: 'network_security',
      currentValue: 99.7,
      targetValue: 99.5,
      unit: '%',
      trend: 'stable',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 5 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 98 + Math.random() * 2,
        incidents: Math.floor(Math.random() * 3)
      }))
    },
    {
      id: 'metric-003',
      name: 'Data Encryption Coverage',
      category: 'data_protection',
      currentValue: 87.5,
      targetValue: 95.0,
      unit: '%',
      trend: 'down',
      status: 'critical',
      lastUpdated: new Date(Date.now() - 10 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 85 + Math.random() * 10,
        incidents: Math.floor(Math.random() * 8)
      }))
    }
  ];

  const incidents: SecurityIncident[] = [
    {
      id: 'incident-001',
      title: 'Unauthorized Access Attempt',
      description: 'Multiple failed login attempts detected from external IP address',
      severity: 'high',
      status: 'investigating',
      category: 'unauthorized_access',
      reportedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      reportedBy: 'Automated System',
      assignedTo: 'Security Team Lead',
      affectedAssets: ['Web Application', 'User Database'],
      responseActions: [
        {
          id: 'action-001',
          action: 'IP address blocked',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          performer: 'Security System',
          result: 'Successfully blocked suspicious IP'
        }
      ]
    }
  ];

  const activeThreats = threats.filter(t => ['detected', 'investigating', 'contained'].includes(t.status)).length;
  const resolvedThreats = threats.filter(t => ['resolved', 'false_positive'].includes(t.status)).length;
  const criticalThreats = threats.filter(t => t.severity === 'critical').length;
  const averageResolutionTime = threats
    .filter(t => t.resolutionTime)
    .reduce((sum, t) => sum + (t.resolutionTime || 0), 0) / threats.filter(t => t.resolutionTime).length || 0;

  return {
    overview: {
      totalThreats: threats.length,
      activeThreats,
      resolvedThreats,
      criticalThreats,
      averageResolutionTime,
      securityScore: 87.3,
      lastScan: new Date(Date.now() - 30 * 60 * 1000),
      nextScheduledScan: new Date(Date.now() + 90 * 60 * 1000),
      systemsMonitored: 247,
      alertsLast24h: 23
    },
    threats,
    metrics,
    incidents,
    complianceStatus: {
      iso27001: { score: 92.1, status: 'compliant', lastAudit: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      nist: { score: 88.7, status: 'warning', lastAudit: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000) },
      gdpr: { score: 95.2, status: 'compliant', lastAudit: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) },
      sox: { score: 91.8, status: 'compliant', lastAudit: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      activeScans: Math.floor(Math.random() * 5),
      pendingInvestigations: Math.floor(Math.random() * 8)
    }
  };
};

export const SecurityOverviewDashboard: React.FC<SecurityOverviewDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<SecurityOverviewData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedThreatType, setSelectedThreatType] = useState<'all' | 'malware' | 'phishing' | 'ddos' | 'intrusion' | 'data_breach' | 'insider_threat'>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'detected' | 'investigating' | 'contained' | 'resolved' | 'false_positive'>('all');
  const [expandedThreat, setExpandedThreat] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'threats' | 'incidents' | 'compliance'>('overview');
  const [searchTerm, setSearchTerm] = useState('');

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1200));
        const data = generateSecurityOverviewData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load security overview data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          overview: {
            ...prevData.overview,
            alertsLast24h: Math.max(0, prevData.overview.alertsLast24h + Math.floor(Math.random() * 3) - 1)
          },
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            activeScans: Math.floor(Math.random() * 5),
            pendingInvestigations: Math.floor(Math.random() * 8)
          }
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter threats based on criteria
  const filteredThreats = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.threats.filter(threat => {
      const matchesType = selectedThreatType === 'all' || threat.type === selectedThreatType;
      const matchesSeverity = selectedSeverity === 'all' || threat.severity === selectedSeverity;
      const matchesStatus = selectedStatus === 'all' || threat.status === selectedStatus;
      const matchesSearch = searchTerm === '' ||
        threat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        threat.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
        threat.targetAsset.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesType && matchesSeverity && matchesStatus && matchesSearch;
    });
  }, [dashboardData, selectedThreatType, selectedSeverity, selectedStatus, searchTerm]);

  // Generate threat distribution chart
  const threatDistributionData = useMemo(() => {
    if (!dashboardData) return null;

    const statusCounts = dashboardData.threats.reduce((acc, threat) => {
      acc[threat.status] = (acc[threat.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      labels: Object.keys(statusCounts).map(status =>
        status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)
      ),
      datasets: [{
        data: Object.values(statusCounts),
        backgroundColor: [
          'rgba(248, 113, 113, 0.8)', // detected - red
          'rgba(251, 191, 36, 0.8)',  // investigating - amber
          'rgba(59, 130, 246, 0.8)',  // contained - blue
          'rgba(52, 211, 153, 0.8)',  // resolved - green
          'rgba(156, 163, 175, 0.8)'  // false_positive - gray
        ],
        borderColor: [
          'rgb(248, 113, 113)',
          'rgb(251, 191, 36)',
          'rgb(59, 130, 246)',
          'rgb(52, 211, 153)',
          'rgb(156, 163, 175)'
        ],
        borderWidth: 2,
        hoverOffset: 8
      }]
    };
  }, [dashboardData]);

  // Generate security metrics chart
  const securityMetricsData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.metrics.map(m => m.name),
      datasets: [{
        label: 'Current Value',
        data: dashboardData.metrics.map(m => m.currentValue),
        backgroundColor: dashboardData.metrics.map(m =>
          m.status === 'compliant' ? 'rgba(52, 211, 153, 0.8)' :
          m.status === 'warning' ? 'rgba(251, 191, 36, 0.8)' :
          'rgba(248, 113, 113, 0.8)'
        ),
        borderColor: dashboardData.metrics.map(m =>
          m.status === 'compliant' ? 'rgb(52, 211, 153)' :
          m.status === 'warning' ? 'rgb(251, 191, 36)' :
          'rgb(248, 113, 113)'
        ),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }, {
        label: 'Target Value',
        data: dashboardData.metrics.map(m => m.targetValue),
        backgroundColor: 'rgba(156, 163, 175, 0.3)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'medium':
        return <AlertCircle className="w-5 h-5 text-blue-500" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Shield className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSeverityColorClass = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'contained':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'investigating':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'detected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'false_positive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getThreatTypeIcon = (type: string) => {
    switch (type) {
      case 'malware':
        return <Bug className="w-4 h-4" />;
      case 'phishing':
        return <Globe className="w-4 h-4" />;
      case 'ddos':
        return <Wifi className="w-4 h-4" />;
      case 'intrusion':
        return <Unlock className="w-4 h-4" />;
      case 'data_breach':
        return <Database className="w-4 h-4" />;
      case 'insider_threat':
        return <User className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateSecurityOverviewData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleThreatClick = useCallback((threatId: string) => {
    setExpandedThreat(expandedThreat === threatId ? null : threatId);
  }, [expandedThreat]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-80" />
            <LoadingSkeleton className="h-80" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Security Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Security Overview</h1>
              <p className="text-text-secondary">
                Comprehensive security monitoring with threat detection and incident response
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • {dashboardData.realTimeUpdates.activeScans} scans active
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh security data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Security Report
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Security Score</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.securityScore.toFixed(1)}</p>
                </div>
                <Shield className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Active Threats</p>
                  <p className="text-2xl font-bold text-red-500">{dashboardData.overview.activeThreats}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Systems Monitored</p>
                  <p className="text-2xl font-bold text-blue-500">{dashboardData.overview.systemsMonitored}</p>
                </div>
                <Server className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Alerts (24h)</p>
                  <p className="text-2xl font-bold text-amber-500">{dashboardData.overview.alertsLast24h}</p>
                </div>
                <Bell className="w-8 h-8 text-amber-500" />
              </div>
            </div>
          </div>
        )}

        {/* View Tabs */}
        <div className="flex items-center gap-2 mb-6">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'overview'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('threats')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'threats'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <AlertTriangle className="w-4 h-4 inline mr-2" />
            Threats
          </button>
          <button
            onClick={() => setActiveView('incidents')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'incidents'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Activity className="w-4 h-4 inline mr-2" />
            Incidents
          </button>
          <button
            onClick={() => setActiveView('compliance')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'compliance'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <CheckCircle className="w-4 h-4 inline mr-2" />
            Compliance
          </button>
        </div>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && (
        <>
          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Threat Distribution Chart */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Threat Status Distribution
              </h3>
              {threatDistributionData && (
                <div className="h-64 flex items-center justify-center">
                  <Doughnut
                    data={threatDistributionData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>

            {/* Security Metrics Chart */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Security Metrics Performance
              </h3>
              {securityMetricsData && (
                <div className="h-64">
                  <Bar
                    data={securityMetricsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      },
                      scales: {
                        x: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                          }
                        },
                        y: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                            callback: (value) => `${value}%`
                          },
                          min: 0,
                          max: 100
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-surface rounded-lg p-6">
            <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Security Activity
            </h3>

            {dashboardData && (
              <div className="space-y-3">
                {dashboardData.threats.slice(0, 5).map((threat) => (
                  <div key={threat.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                    <div className="flex items-center gap-3">
                      {getThreatTypeIcon(threat.type)}
                      <div>
                        <p className="font-medium text-text">{threat.name}</p>
                        <p className="text-sm text-text-secondary">
                          {threat.detectedAt.toLocaleString()} • {threat.source}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(threat.severity)}`}>
                        {threat.severity}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(threat.status)}`}>
                        {threat.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}

      {/* Threats View */}
      {activeView === 'threats' && (
        <div className="bg-surface rounded-lg p-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search threats, sources, or assets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              />
            </div>

            <select
              value={selectedThreatType}
              onChange={(e) => setSelectedThreatType(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Types</option>
              <option value="malware">Malware</option>
              <option value="phishing">Phishing</option>
              <option value="ddos">DDoS</option>
              <option value="intrusion">Intrusion</option>
              <option value="data_breach">Data Breach</option>
              <option value="insider_threat">Insider Threat</option>
            </select>

            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Status</option>
              <option value="detected">Detected</option>
              <option value="investigating">Investigating</option>
              <option value="contained">Contained</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
            </select>
          </div>

          {/* Threat Cards */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text">Security Threats ({filteredThreats.length})</h3>

            {filteredThreats.length === 0 ? (
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">No threats found matching your criteria.</p>
              </div>
            ) : (
              filteredThreats.map((threat) => (
                <div key={threat.id} className="bg-card rounded-lg border border-border overflow-hidden">
                  {/* Threat Header */}
                  <div
                    className="p-4 cursor-pointer hover:bg-border/30 transition-colors"
                    onClick={() => handleThreatClick(threat.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getSeverityIcon(threat.severity)}
                        <div>
                          <h4 className="text-lg font-semibold text-text">{threat.name}</h4>
                          <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                            <span className="flex items-center gap-1">
                              {getThreatTypeIcon(threat.type)}
                              {threat.type.replace('_', ' ')}
                            </span>
                            <span>Source: {threat.source}</span>
                            <span>Target: {threat.targetAsset}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-text">{threat.riskScore}</p>
                          <p className="text-xs text-text-secondary">Risk Score</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(threat.severity)}`}>
                            {threat.severity}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(threat.status)}`}>
                            {threat.status.replace('_', ' ')}
                          </span>

                          {expandedThreat === threat.id ? (
                            <ChevronDown className="w-5 h-5 text-text-secondary" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-text-secondary" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Threat Timeline */}
                    <div className="mt-4 flex items-center gap-4 text-sm text-text-secondary">
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        Detected: {threat.detectedAt.toLocaleString()}
                      </span>
                      {threat.resolutionTime && (
                        <span className="flex items-center gap-1">
                          <Activity className="w-3 h-3" />
                          Resolved in: {Math.floor(threat.resolutionTime / 60)}h {threat.resolutionTime % 60}m
                        </span>
                      )}
                      {threat.investigator && (
                        <span className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          Investigator: {threat.investigator}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Expanded Threat Details */}
                  {expandedThreat === threat.id && (
                    <div className="border-t border-border p-4 bg-background/50">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Affected Systems */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Affected Systems</h5>
                          <div className="space-y-2">
                            {threat.affectedSystems.map((system, index) => (
                              <div key={index} className="flex items-center gap-2 p-2 bg-card rounded border border-border">
                                <Server className="w-4 h-4 text-text-secondary" />
                                <span className="text-text">{system}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Mitigation Actions */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Mitigation Actions</h5>
                          {threat.mitigationActions.length === 0 ? (
                            <p className="text-text-secondary">No mitigation actions defined.</p>
                          ) : (
                            <div className="space-y-3">
                              {threat.mitigationActions.map((action) => (
                                <div key={action.id} className="bg-card rounded-lg p-3 border border-border">
                                  <div className="flex items-start justify-between mb-2">
                                    <h6 className="font-medium text-text">{action.action}</h6>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                      action.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                      action.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                                      'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                                    }`}>
                                      {action.status.replace('_', ' ')}
                                    </span>
                                  </div>
                                  <div className="flex items-center justify-between text-sm text-text-secondary">
                                    <span>Assignee: {action.assignee}</span>
                                    {action.completedAt && (
                                      <span>Completed: {action.completedAt.toLocaleString()}</span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Incidents View */}
      {activeView === 'incidents' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Security Incidents
          </h3>

          {dashboardData && dashboardData.incidents.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <p className="text-text-secondary">No security incidents reported.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {dashboardData?.incidents.map((incident) => (
                <div key={incident.id} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-text">{incident.title}</h4>
                      <p className="text-text-secondary mb-2">{incident.description}</p>
                      <div className="flex items-center gap-4 text-sm text-text-secondary">
                        <span>Reported: {incident.reportedAt.toLocaleString()}</span>
                        <span>By: {incident.reportedBy}</span>
                        {incident.assignedTo && <span>Assigned: {incident.assignedTo}</span>}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(incident.severity)}`}>
                        {incident.severity}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        incident.status === 'resolved' || incident.status === 'closed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                        incident.status === 'investigating' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                        'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
                      }`}>
                        {incident.status}
                      </span>
                    </div>
                  </div>

                  {/* Response Actions */}
                  {incident.responseActions.length > 0 && (
                    <div className="mt-4 p-3 bg-background/50 rounded border border-border">
                      <h6 className="font-medium text-text mb-2">Response Actions</h6>
                      <div className="space-y-2">
                        {incident.responseActions.map((action) => (
                          <div key={action.id} className="text-sm">
                            <div className="flex items-center justify-between">
                              <span className="text-text">{action.action}</span>
                              <span className="text-text-secondary">{action.timestamp.toLocaleString()}</span>
                            </div>
                            <div className="text-text-secondary">
                              By: {action.performer} • Result: {action.result}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Compliance View */}
      {activeView === 'compliance' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Security Compliance Status
          </h3>

          {dashboardData && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(dashboardData.complianceStatus).map(([framework, status]) => (
                <div key={framework} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-lg font-semibold text-text uppercase">{framework}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      status.status === 'compliant' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
                    }`}>
                      {status.status}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-text-secondary">Compliance Score</span>
                        <span className="text-text font-medium">{status.score.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-border rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            status.score >= 90 ? 'bg-green-500' :
                            status.score >= 70 ? 'bg-amber-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${status.score}%` }}
                        />
                      </div>
                    </div>

                    <div className="text-sm text-text-secondary">
                      Last Audit: {status.lastAudit.toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
