import React, { useState } from 'react';
import { useTheme } from '../../context/ThemeContext';

interface Department {
  logo: string;
  name: string;
  totalUsers: number;
  activeUsers: number;
  onboardingDate: string;
}

// Update the remaining departments data to match the new interface
const departments: Department[] = [
  {
    logo: '/logos/agriculture.png',
    name: 'Ministry of Agriculture and Farmers\' Welfare',
    totalUsers: 1234567,
    activeUsers: 980000,
    onboardingDate: '2024-01-15'
  },
  {
    logo: '/logos/consumer.png',
    name: 'Ministry of Consumer Affairs, Food and Public Distribution',
    totalUsers: 987654,
    activeUsers: 850000,
    onboardingDate: '2024-01-20'
  },
  {
    logo: '/logos/culture.png',
    name: 'Ministry of Culture',
    totalUsers: 456789,
    activeUsers: 380000,
    onboardingDate: '2024-01-25'
  },
  {
    logo: '/logos/corporate.png',
    name: 'Ministry of Corporate Affairs',
    totalUsers: 789123,
    activeUsers: 600000,
    onboardingDate: '2024-01-28'
  },
  {
    logo: '/logos/earth.png',
    name: 'Ministry of Earth Sciences',
    totalUsers: 345678,
    activeUsers: 290000,
    onboardingDate: '2024-02-01'
  },
  {
    logo: '/logos/external.png',
    name: 'Ministry of External Affairs',
    totalUsers: 891234,
    activeUsers: 750000,
    onboardingDate: '2024-02-05'
  },
  {
    logo: '/logos/food.png',
    name: 'Ministry of Food Processing Industries',
    totalUsers: 567891,
    activeUsers: 480000,
    onboardingDate: '2024-02-10'
  },
  {
    logo: '/logos/electronics.png',
    name: 'Ministry of Electronics and Information Technology',
    totalUsers: 1456789,
    activeUsers: 1200000,
    onboardingDate: '2024-02-15'
  },
  {
    logo: '/logos/finance.png',
    name: 'Ministry of Finance',
    totalUsers: 2345678,
    activeUsers: 2100000,
    onboardingDate: '2024-02-20'
  },
  {
    logo: '/logos/health.png',
    name: 'Ministry of Health and Family Welfare',
    totalUsers: 1789123,
    activeUsers: 1500000,
    onboardingDate: '2024-02-25'
  },
  {
    logo: '/logos/home.png',
    name: 'Ministry of Home Affairs',
    totalUsers: 2891234,
    activeUsers: 2500000,
    onboardingDate: '2024-03-01'
  },
  {
    logo: '/logos/defence.png',
    name: 'Ministry of Defence',
    totalUsers: 1567891,
    activeUsers: 1400000,
    onboardingDate: '2024-03-05'
  },
  {
    logo: '/logos/railways.png',
    name: 'Ministry of Railways',
    totalUsers: 3456789,
    activeUsers: 3000000,
    onboardingDate: '2024-03-10'
  },
  {
    logo: '/logos/education.png',
    name: 'Ministry of Education',
    totalUsers: 2789123,
    activeUsers: 2400000,
    onboardingDate: '2024-03-15'
  },
  {
    logo: '/logos/environment.png',
    name: 'Ministry of Environment, Forest and Climate Change',
    totalUsers: 891234,
    activeUsers: 780000,
    onboardingDate: '2024-03-20'
  }
];

// Add helper function for department initials
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .filter(word => !['of', 'and', '&'].includes(word.toLowerCase()))
    .slice(0, 2)
    .map(word => word[0])
    .join('')
    .toUpperCase();
};

const GovernmentDepartments: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { mode } = useTheme();
  const [newDepartment, setNewDepartment] = useState<Partial<Department>>({
    name: '',
    totalUsers: 0,
    activeUsers: 0,
    onboardingDate: new Date().toISOString().split('T')[0]
  });

  // Update the newDepartment state reset in handleAddDepartment
  const handleAddDepartment = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('New department:', newDepartment);
    setIsModalOpen(false);
    setNewDepartment({
      name: '',
      totalUsers: 0,
      activeUsers: 0,
      onboardingDate: new Date().toISOString().split('T')[0]
    });
  };

  return (
    <div className="bg-card rounded-xl p-6 shadow-md mx-8 dashboard-card">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h3 className="text-2xl font-semibold text-text">Government Departments</h3>
          <p className="text-sm text-text-secondary mt-1">Overview of all onboarded departments and their statistics</p>
        </div>
        <div className="w-full sm:w-auto flex gap-4 items-center">
          <div className="relative flex-1 sm:flex-none">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search departments..."
              className="w-full sm:w-64 bg-surface text-text pl-10 pr-4 py-2.5 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 border border-border hover:border-purple-300 dark:hover:border-purple-600 transition-all placeholder:text-text-secondary"
            />
            <svg
              className="absolute left-3 top-3 h-4 w-4 text-purple-500 dark:text-purple-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <button
            onClick={() => setIsModalOpen(true)}
            className="hidden sm:flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Department
          </button>

          {/* Add Department Modal */}
          {isModalOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-card rounded-xl p-6 w-full max-w-md">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-text">Add New Department</h3>
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleAddDepartment} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text mb-1">
                      Department Name
                    </label>
                    <input
                      type="text"
                      required
                      value={newDepartment.name}
                      onChange={(e) => setNewDepartment({ ...newDepartment, name: e.target.value })}
                      className="w-full px-3 py-2 border border-border bg-surface text-text rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      placeholder="Enter department name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">
                      Total Users
                    </label>
                    <input
                      type="number"
                      required
                      value={newDepartment.totalUsers}
                      onChange={(e) => setNewDepartment({ ...newDepartment, totalUsers: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-border bg-surface text-text rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      placeholder="Enter total users count"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">
                      Active Users
                    </label>
                    <input
                      type="number"
                      required
                      value={newDepartment.activeUsers}
                      onChange={(e) => setNewDepartment({ ...newDepartment, activeUsers: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-border bg-surface text-text rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      placeholder="Enter active users count"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">
                      Onboarding Date
                    </label>
                    <input
                      type="date"
                      required
                      value={newDepartment.onboardingDate}
                      onChange={(e) => setNewDepartment({ ...newDepartment, onboardingDate: e.target.value })}
                      className="w-full px-3 py-2 border border-border bg-surface text-text rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1 px-4 py-2 border border-border text-text-secondary rounded-lg hover:bg-surface transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-lg font-medium transition-all duration-200"
                    >
                      Add Department
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="overflow-x-auto rounded-xl border border-border">
        <table className="w-full">
          {/* // Update the table header and body */}
          <thead>
            <tr className="bg-surface">
              <th className="px-6 py-4 text-left text-xs font-semibold text-text uppercase tracking-wider">Department</th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-text uppercase tracking-wider">Onboarding Date</th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-text uppercase tracking-wider">Total Users</th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-text uppercase tracking-wider">Active Users</th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-text uppercase tracking-wider">Category</th>
            </tr>
          </thead>
          {/* // In the table body, remove the status column */}
          <tbody className="divide-y divide-border bg-card">
            {departments.map((dept, index) => (
              <tr key={index} className="hover:bg-surface transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center overflow-hidden text-white font-semibold text-sm ${index % 5 === 0 ? 'bg-gradient-to-br from-indigo-500 to-indigo-600 dark:from-indigo-400 dark:to-indigo-500' :
                        index % 5 === 1 ? 'bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-400 dark:to-purple-500' :
                          index % 5 === 2 ? 'bg-gradient-to-br from-violet-500 to-violet-600 dark:from-violet-400 dark:to-violet-500' :
                            index % 5 === 3 ? 'bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500' :
                              'bg-gradient-to-br from-slate-500 to-slate-600 dark:from-slate-400 dark:to-slate-500'
                        }`}
                    >
                      {getInitials(dept.name)}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-text">{dept.name}</div>
                      <div className="text-xs text-text-secondary">ID: GOV-{index + 1000}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-text">
                    {new Date(dept.onboardingDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="text-xs text-text-secondary">
                    {new Date(dept.onboardingDate).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-semibold text-text">
                    {new Intl.NumberFormat('en-IN').format(dept.totalUsers)}
                  </div>
                  <div className="text-xs text-text-secondary">Total Users</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-semibold text-text">
                    {new Intl.NumberFormat('en-IN').format(dept.activeUsers)}
                  </div>
                  <div className="text-xs text-text-secondary">Active Users</div>
                  <div className="text-xs text-success">
                    {Math.round((dept.activeUsers / dept.totalUsers) * 100)}% Active
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-50 to-indigo-50 text-purple-700 border border-purple-200 dark:from-purple-900/30 dark:to-indigo-900/30 dark:text-purple-300 dark:border-purple-700/50">
                    Government
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-6 flex justify-between items-center text-sm text-text-secondary">
        <div>
          Showing <span className="font-medium text-text">{departments.length}</span> departments
        </div>
        <div className="flex items-center gap-2">
          <button className="px-3 py-1 rounded border border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-colors">
            Previous
          </button>
          <button className="px-3 py-1 rounded border border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-colors">
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default GovernmentDepartments;