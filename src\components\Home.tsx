import React from 'react';
import FeatureHighlight from './FeatureHighlight';
import { Shield, Lock, FileText, BarChart2 } from 'lucide-react';
import { useTheme } from '../context/ThemeContext';

const StatCard = ({ title, value, icon }: { title: string; value: string; icon: React.ReactNode }) => {
  const { mode } = useTheme();

  return (
    <div className="bg-card rounded-xl shadow-md p-6 flex items-center space-x-4 hover:shadow-lg transition-all dark:shadow-none">
      <div className="p-3 rounded-full" style={{ backgroundColor: 'rgba(166, 217, 51, 0.15)', color: '#A6D933' }}>{icon}</div>
      <div>
        <p className="text-text-secondary text-sm font-medium">{title}</p>
        <p className="text-2xl font-bold text-text">{value}</p>
      </div>
    </div>
  );
};

const TestimonialCard = ({ quote, author, role, company }: { quote: string; author: string; role: string; company: string }) => {
  const { mode } = useTheme();

  return (
    <div className="bg-card rounded-xl shadow-md p-6 hover:shadow-lg transition-all dark:shadow-none">
      <div className="mb-4" style={{ color: '#A6D933' }}>
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
          <path d="M11.3 6.2H9.8c-2.7 0-5.3 1.6-5.3 5.1 0 2.8 1.3 4.9 3.5 4.9 1.8 0 3.2-1.5 3.2-3.5 0-1.7-1.1-3.1-2.8-3.1-.5 0-.9.1-1.1.2-.2.1-.3.1-.5.1-.4 0-.7-.3-.7-.7 0-.6.7-1.2 2.1-1.2h3c.6 0 1.1.5 1.1 1.1v5c0 .6.5 1.1 1.1 1.1.6 0 1.1-.5 1.1-1.1v-5c0-1.7-1.3-3-3-3zm7.8 0h-1.5c-2.7 0-5.3 1.6-5.3 5.1 0 2.8 1.3 4.9 3.5 4.9 1.8 0 3.2-1.5 3.2-3.5 0-1.7-1.1-3.1-2.8-3.1-.5 0-.9.1-1.1.2-.2.1-.3.1-.5.1-.4 0-.7-.3-.7-.7 0-.6.7-1.2 2.1-1.2h3c.6 0 1.1.5 1.1 1.1v5c0 .6.5 1.1 1.1 1.1.6 0 1.1-.5 1.1-1.1v-5c0-1.7-1.3-3-3-3z"/>
        </svg>
      </div>
      <p className="text-text-secondary mb-4">{quote}</p>
      <div>
        <p className="font-semibold text-text">{author}</p>
        <p className="text-text-secondary text-sm">{role}, {company}</p>
      </div>
    </div>
  );
};

const Home = () => {
  const { mode } = useTheme();

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Hero Section */}
      <div className={`bg-gradient-to-r ${mode === 'dark' ? 'from-primary/20 to-background' : 'from-[#A6D933]/10 to-white'} py-20 px-8`}>
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-10 md:mb-0 text-center md:text-left">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">
                <span style={{ color: '#A6D933' }}>PRAE</span>
                <span className="text-text">FERRE</span>
              </h1>
              <p className="text-xl text-text mb-6">
                The next generation platform for privacy management and compliance
              </p>
              <p className="text-lg text-text-secondary mb-8">
                Secure, compliant, and efficient privacy solutions for enterprises of all sizes
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center md:justify-start">
                <button className="text-white px-6 py-3 rounded-lg transition-all" style={{ backgroundColor: '#A6D933' }}>
                  Get Started
                </button>
                <button className="border border-border bg-surface px-6 py-3 rounded-lg hover:bg-surface/80 transition-all text-text">
                  Learn More
                </button>
              </div>
            </div>
            <div className="md:w-1/2">
              <img
                src={mode === 'dark'
                  ? "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80&blend=111827&blend-mode=multiply&sat=-100&blend-alpha=10"
                  : "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80"}
                alt="Privacy and Security"
                className="rounded-lg shadow-xl w-full max-w-md mx-auto dark:shadow-none"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 px-8 bg-surface">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-text">Trusted by Organizations Worldwide</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Compliance Rate"
              value="99.8%"
              icon={<Shield size={24} />}
            />
            <StatCard
              title="Data Protected"
              value="5.2 PB"
              icon={<Lock size={24} />}
            />
            <StatCard
              title="Reports Generated"
              value="12,500+"
              icon={<FileText size={24} />}
            />
            <StatCard
              title="Risk Reduction"
              value="85%"
              icon={<BarChart2 size={24} />}
            />
          </div>
        </div>
      </div>

      {/* Features Section */}
      <FeatureHighlight />

      {/* Testimonials Section */}
      <div className="py-16 px-8 bg-surface">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-text">What Our Clients Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <TestimonialCard
              quote="Praeferre has transformed how we manage privacy compliance. The platform is intuitive and has saved us countless hours of manual work."
              author="Sarah Johnson"
              role="Chief Privacy Officer"
              company="Global Tech Inc."
            />
            <TestimonialCard
              quote="The automated reporting features have been a game-changer for our quarterly compliance reviews. Highly recommended for any enterprise."
              author="Michael Chen"
              role="Data Protection Lead"
              company="Financial Services Group"
            />
            <TestimonialCard
              quote="We've reduced our privacy-related risks by over 70% since implementing Praeferre. The ROI has been exceptional."
              author="Jessica Williams"
              role="CISO"
              company="Healthcare Solutions"
            />
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className={`py-16 px-8 ${mode === 'dark' ? 'bg-primary/5' : 'bg-[#A6D933]/10'}`}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-text">Ready to Transform Your Privacy Management?</h2>
          <p className="text-lg text-text-secondary mb-8">Join thousands of organizations that trust Praeferre for their privacy compliance needs</p>
          <button className="text-white px-8 py-4 rounded-lg text-lg font-medium transition-all" style={{ backgroundColor: '#A6D933' }}>
            Schedule a Demo
          </button>
        </div>
      </div>
    </div>
  );
};

export default Home;