import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Eye, EyeOff } from 'lucide-react';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await login(email, password);
    } catch (error) {
      console.error('Login failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex">
      {/* Left side with security image */}
      <div className="hidden lg:block lg:w-3/5 relative">
        <div className="absolute top-6 left-6">
          <h1 className="text-2xl font-bold">
            <span className="text-[#A6D933]">PRAE</span>
            <span className="text-white">FERRE</span>
          </h1>
        </div>
        <div className="h-full">
          <img
            src="/src/assets/security-image.jpg"
            alt="Cyber Security"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="grid grid-cols-3 gap-8 text-white text-center max-w-3xl mx-auto">
              <div>Internet attack</div>
              <div>protection</div>
              <div>Mobile devices</div>
              <div>Compute</div>
              <div>Internet security</div>
              <div>Cyber security</div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side with login form */}
      <div className="w-full lg:w-2/5 flex flex-col justify-between p-8">
        <div className="flex-1 flex flex-col justify-center">
          <div className="mb-12">
            <img
              src="/src/assets/praeferre-logo.png"
              alt="Praeferre Logo"
              className="h-12 mx-auto"
            />
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                className="w-full px-4 py-3 rounded border border-gray-700 bg-transparent text-white focus:outline-none focus:border-[#A6D933]"
                required
              />
            </div>

            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                className="w-full px-4 py-3 rounded border border-gray-700 bg-transparent text-white focus:outline-none focus:border-[#A6D933]"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 bg-[#A6D933] text-white rounded font-semibold hover:bg-[#8AB929] transition-colors disabled:opacity-50"
            >
              {loading ? 'Logging in...' : 'Log In'}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-400">
              Don't have an account? {' '}
              <a href="#" className="text-[#A6D933] hover:underline">Sign up</a>
            </p>
          </div>
        </div>

        <div className="text-center space-y-6">
          <div>
            <h3 className="text-gray-400 font-semibold mb-2">FOLLOW US</h3>
            <p className="text-gray-500 text-sm">
              Please follow us on social media to help us to create a better experience for you
            </p>
          </div>
          <p className="text-gray-600 text-sm">
            COPYRIGHT @ 2024 PRAEFERRE
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;