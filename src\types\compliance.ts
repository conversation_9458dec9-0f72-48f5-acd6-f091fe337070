export interface Policy {
  id: string;
  name: string;
  description: string;
  status: 'compliant' | 'non_compliant' | 'pending';
  lastUpdated: string;
}

export interface Metrics {
  compliantPercentage: number;
  nonCompliantPercentage: number;
  pendingPercentage: number;
}

export interface ComplianceContextType {
  metrics: Metrics;
  fetchData: () => Promise<void>;
  isLoading: boolean; // Add this line
  trendData: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }[];
  };
  distribution: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
    }[];
  };
  policies: Policy[];
  updateComplianceMetrics: (type: string, category: string, item: string, status: string) => void;
}