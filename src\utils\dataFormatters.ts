/**
 * Utility functions for formatting data in the compliance application
 */

/**
 * Format a percentage value with appropriate precision
 * @param value The percentage value to format
 * @param decimals Number of decimal places
 * @returns Formatted percentage string with % sign
 */
export const formatPercentage = (value: number, decimals = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format a date to a human-readable string
 * @param dateString Date string to format
 * @param includeTime Whether to include time in the output
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, includeTime = false): string => {
  const date = new Date(dateString);
  
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...(includeTime && { hour: '2-digit', minute: '2-digit' })
  };
  
  return new Intl.DateTimeFormat('en-US', options).format(date);
};

/**
 * Convert a status string to a more user-friendly display text
 * @param status The status string from the API
 * @returns User-friendly status text
 */
export const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'compliant': 'Compliant',
    'non_compliant': 'Non-Compliant',
    'pending': 'Pending Review'
  };
  
  return statusMap[status] || status;
};

/**
 * Get appropriate color class based on compliance status
 * @param status The compliance status
 * @returns CSS class name for the status color
 */
export const getStatusColorClass = (status: string): string => {
  switch (status) {
    case 'compliant':
      return 'text-green-500';
    case 'non_compliant':
      return 'text-red-500';
    case 'pending':
      return 'text-yellow-500';
    default:
      return 'text-gray-500';
  }
};

/**
 * Get appropriate background color class based on compliance status
 * @param status The compliance status
 * @returns CSS class name for the status background color
 */
export const getStatusBgColorClass = (status: string): string => {
  switch (status) {
    case 'compliant':
      return 'bg-green-100';
    case 'non_compliant':
      return 'bg-red-100';
    case 'pending':
      return 'bg-yellow-100';
    default:
      return 'bg-gray-100';
  }
}; 