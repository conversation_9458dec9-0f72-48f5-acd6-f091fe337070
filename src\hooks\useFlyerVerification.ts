import { useContext } from 'react';
import { FlyerVerificationContext } from '../context/FlyerVerificationContext';
import type { FlyerVerificationContextType } from '../context/FlyerVerificationContext';

export const useFlyerVerification = (): FlyerVerificationContextType => {
  const context = useContext(FlyerVerificationContext);
  if (!context) {
    throw new Error('useFlyerVerification must be used within a FlyerVerificationProvider');
  }
  return context;
};
