import React, { useState } from 'react';
import { Shield, AlertTriangle, AlertCircle } from 'lucide-react';

interface ViolationCategory {
  title: string;
  items: {
    name: string;
    subItems: string[];
    status: 'pending' | 'in_progress' | 'completed';
  }[];
}

interface ViolationsModalProps {
  type: 'critical' | 'moderate' | 'marginal';
  onClose: () => void;
  onStatusUpdate: (type: string, category: string, item: string, status: string) => void;
}

const ViolationsModal: React.FC<ViolationsModalProps> = ({ type, onClose, onStatusUpdate }) => {
  const violationCategories: Record<string, ViolationCategory[]> = {
    critical: [
      {
        title: "Regulatory Compliance Policies",
        items: [
          {
            name: "Policy development and documentation",
            subItems: ["Policy creation", "Documentation review", "Update cycles"],
            status: 'pending'
          },
          // ... Add other regulatory compliance items
        ]
      },
      // ... Add Data Encryption and Secure Storage categories
    ],
    // ... Add moderate and marginal violations
  };

  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[80vh] overflow-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">{type.charAt(0).toUpperCase() + type.slice(1)} Violations</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <span className="sr-only">Close</span>
            ×
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {violationCategories[type]?.map((category, idx) => (
            <div key={idx} className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-4">{category.title}</h3>
              {category.items.map((item, itemIdx) => (
                <div key={itemIdx} className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">{item.name}</span>
                    <select
                      value={item.status}
                      onChange={(e) => onStatusUpdate(type, category.title, item.name, e.target.value)}
                      className={`px-3 py-1 rounded-full text-sm ${getStatusColor(item.status)}`}
                    >
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                  <ul className="ml-4 text-sm text-gray-600">
                    {item.subItems.map((subItem, subIdx) => (
                      <li key={subIdx} className="mb-1">• {subItem}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ViolationsModal;