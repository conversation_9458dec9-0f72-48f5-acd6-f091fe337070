import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeMode, getTheme, generateCssVariables } from '../theme/theme';

interface ThemeContextType {
  mode: ThemeMode;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Get initial theme from localStorage or default to light
  const [mode, setMode] = useState<ThemeMode>(() => {
    const savedTheme = localStorage.getItem('theme');
    return (savedTheme as ThemeMode) || 'light';
  });

  // Toggle between light and dark mode
  const toggleTheme = () => {
    setMode((prevMode) => {
      const newMode = prevMode === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', newMode);
      return newMode;
    });
  };

  // Apply theme CSS variables to document root
  useEffect(() => {
    const theme = getTheme(mode);

    // Apply CSS variables to document root
    const root = document.documentElement;
    const cssVars = generateCssVariables(theme);

    // Split the CSS variables string and apply each variable individually
    // This avoids overwriting any existing inline styles
    cssVars.split(';').forEach(variable => {
      if (variable.trim()) {
        const [name, value] = variable.split(':');
        if (name && value) {
          root.style.setProperty(name.trim(), value.trim());
        }
      }
    });

    // Set data attribute for potential CSS selectors
    root.setAttribute('data-theme', mode);

    // Apply the dark class to the html element for Tailwind dark mode
    if (mode === 'dark') {
      // Add dark class to all necessary elements
      root.classList.add('dark');
      document.body.classList.add('dark-mode'); // Keep for backward compatibility
      document.body.classList.remove('light-mode');
      document.body.classList.add('dark');

      // Apply dark mode to all containers that need specific handling
      const darkModeContainers = document.querySelectorAll(
        '.dashboard-content, .chart-container, .card, .dashboard-card'
      );
      darkModeContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.classList.add('dark-mode');
          // Ensure z-index is properly set to prevent blurry content
          if (container.classList.contains('dashboard-content') ||
              container.classList.contains('chart-container')) {
            container.style.position = 'relative';
            container.style.zIndex = '10';
          }
        }
      });

      // Fix for any iframes or embedded content
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        try {
          if (iframe.contentDocument && iframe.contentDocument.documentElement) {
            iframe.contentDocument.documentElement.classList.add('dark');
          }
        } catch (e) {
          // Ignore cross-origin errors
        }
      });
    } else {
      // Remove dark classes
      root.classList.remove('dark');
      document.body.classList.remove('dark-mode');
      document.body.classList.add('light-mode'); // Keep for backward compatibility
      document.body.classList.remove('dark');

      // Remove dark mode from all containers
      const darkModeContainers = document.querySelectorAll(
        '.dashboard-content, .chart-container, .card, .dashboard-card'
      );
      darkModeContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.classList.remove('dark-mode');
          // Reset z-index if it was set
          if (container.classList.contains('dashboard-content') ||
              container.classList.contains('chart-container')) {
            container.style.position = '';
            container.style.zIndex = '';
          }
        }
      });

      // Fix for any iframes or embedded content
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        try {
          if (iframe.contentDocument && iframe.contentDocument.documentElement) {
            iframe.contentDocument.documentElement.classList.remove('dark');
          }
        } catch (e) {
          // Ignore cross-origin errors
        }
      });
    }

    // Force a repaint to ensure all styles are applied correctly
    document.body.style.transition = 'none';
    setTimeout(() => {
      document.body.style.transition = '';

      // Add a class to indicate theme transition is complete
      document.body.classList.add('theme-transition-complete');

      // Remove the class after a short delay to allow for any animations
      setTimeout(() => {
        document.body.classList.remove('theme-transition-complete');
      }, 300);
    }, 50);
  }, [mode]);

  return (
    <ThemeContext.Provider value={{ mode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
