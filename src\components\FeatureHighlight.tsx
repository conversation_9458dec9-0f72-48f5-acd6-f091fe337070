import React from 'react';
import { useTheme } from '../context/ThemeContext';

interface FeatureProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const FeatureCard: React.FC<FeatureProps> = ({ title, description, icon }) => {
  const { mode } = useTheme();

  return (
    <div className="bg-card p-6 rounded-lg shadow-md hover:shadow-lg transition-all dark:shadow-none">
      <div className="text-primary mb-4 text-3xl">{icon}</div>
      <h3 className="text-xl font-semibold mb-2 text-text">{title}</h3>
      <p className="text-text-secondary">{description}</p>
    </div>
  );
};

const FeatureHighlight: React.FC = () => {
  const { mode } = useTheme();
  const features = [
    {
      title: 'Privacy Compliance',
      description: 'Stay compliant with global privacy regulations including GDPR, CCPA, and more.',
      icon: '🔒'
    },
    {
      title: 'Data Protection',
      description: 'Secure sensitive information with advanced encryption and access controls.',
      icon: '🛡️'
    },
    {
      title: 'Risk Assessment',
      description: 'Identify and mitigate privacy risks before they become compliance issues.',
      icon: '📊'
    },
    {
      title: 'Automated Reporting',
      description: 'Generate comprehensive compliance reports with a single click.',
      icon: '📝'
    }
  ];

  return (
    <div className="py-12 bg-background">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12 text-text">Key Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeatureHighlight;
