import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

const TestEnterpriseDashboard = () => {
  console.log('🚀 TestEnterpriseDashboard component mounting/rendering');
  
  const [activeTab, setActiveTab] = useState<'compliant' | 'non_compliant' | 'pending'>('compliant');
  
  console.log('🔍 Test Component state:', { activeTab });

  // Debug logging for tab state changes
  useEffect(() => {
    console.log('🔍 Test Active tab changed to:', activeTab);
  }, [activeTab]);

  // Simple test data
  const testPolicies = [
    { id: '1', name: 'Test Policy 1', status: 'compliant' as const },
    { id: '2', name: 'Test Policy 2', status: 'compliant' as const },
    { id: '3', name: 'Test Policy 3', status: 'non_compliant' as const },
    { id: '4', name: 'Test Policy 4', status: 'pending' as const },
  ];

  // Filter policies based on active tab
  const filteredPolicies = testPolicies.filter(policy => policy.status === activeTab);

  console.log('📋 Test Filtered policies:', filteredPolicies);

  // Handle tab clicks with debugging
  const handleTabClick = (tabName: 'compliant' | 'non_compliant' | 'pending') => {
    console.log('🖱️ Test Tab clicked:', tabName);
    console.log('🖱️ Test Previous active tab:', activeTab);
    setActiveTab(tabName);
    console.log('🖱️ Test setActiveTab called with:', tabName);
  };

  return (
    <div className="p-8 bg-white dark:bg-gray-900 min-h-screen">
      <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Test Enterprise Dashboard</h1>
      
      {/* Debug indicator */}
      <div className="mb-4 text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded">
        🔍 Test Debug: Active Tab = "{activeTab}" | Filtered Count = {filteredPolicies.length}
      </div>

      {/* Test buttons */}
      <div className="mb-4 flex gap-2">
        <button 
          onClick={() => {
            console.log('🧪 Test button: Setting tab to compliant');
            setActiveTab('compliant');
          }}
          className="px-3 py-2 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200"
        >
          Test: Compliant
        </button>
        <button 
          onClick={() => {
            console.log('🧪 Test button: Setting tab to non_compliant');
            setActiveTab('non_compliant');
          }}
          className="px-3 py-2 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200"
        >
          Test: Non-Compliant
        </button>
        <button 
          onClick={() => {
            console.log('🧪 Test button: Setting tab to pending');
            setActiveTab('pending');
          }}
          className="px-3 py-2 text-sm bg-amber-100 text-amber-800 rounded hover:bg-amber-200"
        >
          Test: Pending
        </button>
      </div>

      {/* Tab Headers */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <div className="flex">
          <button
            onClick={() => handleTabClick('compliant')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === 'compliant'
                ? 'border-green-500 text-green-600 bg-green-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-green-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              <span>Compliant</span>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                {testPolicies.filter(p => p.status === 'compliant').length}
              </span>
            </div>
          </button>

          <button
            onClick={() => handleTabClick('non_compliant')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === 'non_compliant'
                ? 'border-red-500 text-red-600 bg-red-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-red-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <XCircle className="w-4 h-4" />
              <span>Non-Compliant</span>
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                {testPolicies.filter(p => p.status === 'non_compliant').length}
              </span>
            </div>
          </button>

          <button
            onClick={() => handleTabClick('pending')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === 'pending'
                ? 'border-amber-500 text-amber-600 bg-amber-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-amber-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>Pending</span>
              <span className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs">
                {testPolicies.filter(p => p.status === 'pending').length}
              </span>
            </div>
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          {activeTab.replace('_', ' ').toUpperCase()} Policies
        </h3>
        
        {filteredPolicies.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No {activeTab.replace('_', ' ')} policies found.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredPolicies.map((policy) => (
              <div key={policy.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white">{policy.name}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">Status: {policy.status}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestEnterpriseDashboard;
