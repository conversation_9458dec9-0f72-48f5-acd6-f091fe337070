import { useState, useEffect } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js/auto';
import { useCompliance } from '../context/ComplianceContext';
import { useTheme } from '../context/ThemeContext';
// Remove this import since Analytics is already lazy loaded below
// Remove this import since PolicyDistributionChart is lazy loaded below
import { ComplianceByPolicy } from './ComplianceByPolicy';
import { ViolationsSummary } from './ViolationsSummary';
import React, { Suspense, lazy } from 'react';
import { optimizedChartDefaults } from '../utils/chartOptimizations';

// Lazy load components
const Analytics = lazy(() => import('./Analytics'));
const PolicyDistributionChart = lazy(() => import('./PolicyDistributionChart'));
// Remove the lazy loading since ComplianceByPolicy is already imported directly above
// const ComplianceByPolicy = lazy(() => import('./ComplianceByPolicy'));
// Remove duplicate lazy load since ViolationsSummary is already imported directly
// const ViolationsSummary = lazy(() => import('./ViolationsSummary'));

const LoadingFallback = () => (
  <div className="animate-pulse bg-surface rounded-xl h-[400px]"></div>
);

const ComplianceDashboard: React.FC = () => {
  const { metrics, trendData, distribution, policies, fetchData } = useCompliance();
  const { mode } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const lineChartData = {
    labels: ['Nov-24', 'Dec-24', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25', 'Jun-25'],
    datasets: [
      {
        label: 'Non-Compliant',
        data: [0.2, 1.8, 1.5, 1.0, 0.2, 1.8, 1.7, 2.8],
        borderColor: '#ef4444',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Pending Review',
        data: [1.8, 1.2, 0.9, 1.5, 2.0, 1.5, 0.5, 1.6],
        borderColor: '#f59e0b',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Compliant',
        data: [0.2, 0.5, 0.5, 0.5, 2.4, 1.6, 1.1, 0.9],
        borderColor: '#10b981',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
    ],
  };

  // Update chart options title
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Compliance Trends',
        align: 'center' as const,
        font: {
          size: 16,
          family: 'Arial, sans-serif',
          weight: 'normal' as const,
        },
        padding: {
          bottom: 30,
        },
        color: mode === 'dark' ? '#F8FAFC' : '#111827',
      },
    },
    scales: {
      y: {
        min: 0,
        max: 4,
        ticks: {
          stepSize: 1,
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 8,
        },
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          drawBorder: false,
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        border: {
          display: false,
        },
        title: {
          display: true,
          text: 'Consent Rate',
          font: {
            size: 12,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: { top: 0, bottom: 10 },
        },
      },
      x: {
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        ticks: {
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 5,
        },
        border: {
          display: false,
        },
      },
    },
  };

  useEffect(() => {
    let mounted = true;

    const loadData = async () => {
      if (!isInitialLoad) return;

      try {
        setIsLoading(true);
        await fetchData();
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
          setIsInitialLoad(false);
        }
      }
    };

    loadData();
    return () => {
      mounted = false;
    };
  }, [fetchData, isInitialLoad]);

  if (isLoading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-surface rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-surface rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-sm text-text-secondary">Compliance Overview</h1>
        <h2 className="text-2xl font-bold text-text">Compliance Dashboard</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-card rounded-lg shadow p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-text mb-1">Compliant</h3>
              <p className="text-sm text-text-secondary">Total compliant policies</p>
            </div>
            <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/30 group-hover:bg-green-100 dark:group-hover:bg-green-900/50 transition-colors">
              <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div className="flex items-end gap-2">
            <p className="text-3xl font-bold text-green-500">{metrics.compliantPercentage}%</p>
            <p className="text-sm text-text-secondary mb-1">of total policies</p>
          </div>
          <div className="mt-4 h-1 bg-surface rounded-full">
            <div
              className="h-full bg-green-500 rounded-full transition-all duration-500"
              style={{ width: `${metrics.compliantPercentage}%` }}
            />
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-text mb-1">Non-Compliant</h3>
              <p className="text-sm text-text-secondary">Policies requiring attention</p>
            </div>
            <div className="p-2 rounded-full bg-red-50 dark:bg-red-900/30 group-hover:bg-red-100 dark:group-hover:bg-red-900/50 transition-colors">
              <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end gap-2">
            <p className="text-3xl font-bold text-red-500">{metrics.nonCompliantPercentage}%</p>
            <p className="text-sm text-text-secondary mb-1">of total policies</p>
          </div>
          <div className="mt-4 h-1 bg-surface rounded-full">
            <div
              className="h-full bg-red-500 rounded-full transition-all duration-500"
              style={{ width: `${metrics.nonCompliantPercentage}%` }}
            />
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-text mb-1">Pending Review</h3>
              <p className="text-sm text-text-secondary">Awaiting assessment</p>
            </div>
            <div className="p-2 rounded-full bg-orange-50 dark:bg-orange-900/30 group-hover:bg-orange-100 dark:group-hover:bg-orange-900/50 transition-colors">
              <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end gap-2">
            <p className="text-3xl font-bold text-orange-500">{metrics.pendingPercentage}%</p>
            <p className="text-sm text-text-secondary mb-1">of total policies</p>
          </div>
          <div className="mt-4 h-1 bg-surface rounded-full">
            <div
              className="h-full bg-orange-500 rounded-full transition-all duration-500"
              style={{ width: `${metrics.pendingPercentage}%` }}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <ComplianceByPolicy />
        <ViolationsSummary />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-card rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
          <div style={{
            height: '400px',
            position: 'relative',
            marginLeft: '40px',
            marginRight: '20px'
          }}>
            <Line
              data={lineChartData}
              options={chartOptions}
            />
          </div>

          {/* Update the legend labels in the JSX */}
          <div className="flex justify-center gap-8 mt-6">
            <div className="flex items-center gap-2">
              <div className="w-6 h-[1px] bg-green-500"></div>
              <span className="text-[11px] text-text-secondary">Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-[1px] bg-red-500"></div>
              <span className="text-[11px] text-text-secondary">Non-Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-[1px] bg-yellow-500"></div>
              <span className="text-[11px] text-text-secondary">Pending Review</span>
            </div>
          </div>
        </div>
        <PolicyDistributionChart />
      </div>

      <div className="bg-card rounded-lg shadow p-6">
        <Analytics />
      </div>
    </div>
  );
};

export default React.memo(ComplianceDashboard);