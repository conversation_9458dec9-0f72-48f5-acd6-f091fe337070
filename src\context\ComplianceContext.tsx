import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ComplianceContextType, Metrics, Policy } from '../types/compliance';

const ComplianceContext = createContext<ComplianceContextType | undefined>(undefined);

const initialMetrics: Metrics = {
  compliantPercentage: 75,
  nonCompliantPercentage: 15,
  pendingPercentage: 10,
};

const initialPolicies: Policy[] = [
  {
    id: '1',
    name: 'Data Protection Policy',
    description: 'Guidelines for handling sensitive data',
    status: 'compliant',
    lastUpdated: '2024-01-15',
  },
  {
    id: '2',
    name: 'Privacy Policy',
    description: 'User data privacy guidelines',
    status: 'non_compliant',
    lastUpdated: '2024-01-14',
  },
  {
    id: '3',
    name: 'Security Policy',
    description: 'Information security guidelines',
    status: 'pending',
    lastUpdated: '2024-01-13',
  },
];

const initialTrendData = {
  labels: ['Nov', 'Dec', 'Jan'],
  datasets: [
    {
      label: 'Compliant',
      data: [65, 70, 75],
      borderColor: '#33cc99',
      backgroundColor: 'transparent',
      tension: 0.1,
      borderWidth: 1.5,
      pointRadius: 2,
    },
    {
      label: 'Non-Compliant',
      data: [20, 18, 15],
      borderColor: '#ff3333',
      backgroundColor: 'transparent',
      tension: 0.1,
      borderWidth: 1.5,
      pointRadius: 2,
    },
    {
      label: 'Pending Review',
      data: [15, 12, 10],
      borderColor: '#ffcc00',
      backgroundColor: 'transparent',
      tension: 0.1,
      borderWidth: 1.5,
      pointRadius: 2,
    },
  ],
};

const initialDistribution = {
  labels: ['Compliant', 'Non-Compliant', 'Pending'],
  datasets: [
    {
      data: [75, 15, 10],
      backgroundColor: ['#33cc99', '#ff3333', '#ffcc00'],
      borderWidth: 0,
    },
  ],
};

export const ComplianceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [metrics, setMetrics] = useState<Metrics>(initialMetrics);
  const [policies] = useState<Policy[]>(initialPolicies);
  const [trendData] = useState(initialTrendData);
  const [distribution] = useState(initialDistribution);
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      // Simulating API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // In a real application, you would fetch data from an API here
    } catch (error) {
      console.error('Error fetching compliance data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue = {
    isLoading,
    metrics,
    trendData,
    distribution,
    policies,
    fetchData,
  };

  return (
    <ComplianceContext.Provider value={contextValue}>
      {children}
    </ComplianceContext.Provider>
  );
};

export const useCompliance = () => {
  const context = useContext(ComplianceContext);
  if (context === undefined) {
    throw new Error('useCompliance must be used within a ComplianceProvider');
  }
  return context;
};