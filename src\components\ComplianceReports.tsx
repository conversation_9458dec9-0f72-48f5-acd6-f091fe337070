import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { useCompliance } from '../context/ComplianceContext';
import { formatDate } from '../utils/dataFormatters';
// import { useAuth } from '../context/AuthContext'; // Commented out as not currently used
import { deleteReport } from '../services/reportService';
// import { useTheme } from '../context/ThemeContext'; // Removed as not currently used

interface Report {
  id: string;
  name: string;
  title?: string;
  description: string;
  createdAt: string;
  date?: string;
  status: 'generated' | 'pending' | 'error' | 'compliant' | 'non-compliant';
  downloadUrl?: string;
  standard?: string;
  score?: number;
  findings?: number;
  criticalIssues?: number;
}

const ComplianceReports: React.FC = () => {
  const { policies, metrics } = useCompliance();
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'available' | 'create'>('available');
  // We'll keep these states for future implementation
  const [filterStatus, setFilterStatus] = useState<string>('all');
  // const { mode } = useTheme(); // Commented out as not currently used

  // Combine both report states into one
  const [reports, setReports] = useState<Report[]>([
    {
      id: '1',
      name: 'Monthly Compliance Summary',
      description: 'Overview of compliance status for all policies',
      createdAt: '2024-01-15T10:30:00Z',
      status: 'generated',
      downloadUrl: '#',
      standard: 'GDPR',
      score: 92,
      findings: 3,
      criticalIssues: 0
    },
    {
      id: '2',
      name: 'Quarterly Compliance Analysis',
      description: 'Detailed analysis of compliance trends',
      createdAt: '2024-01-01T14:20:00Z',
      status: 'generated',
      downloadUrl: '#'
    },
    {
      id: '3',
      name: 'Non-Compliance Report',
      description: 'Detailed report of non-compliant policies',
      createdAt: '2024-01-10T09:15:00Z',
      status: 'pending'
    }
  ]);

  // Form state for creating new reports
  const [reportForm, setReportForm] = useState({
    name: '',
    description: '',
    type: 'summary',
    format: 'pdf'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setReportForm(prev => ({ ...prev, [name]: value }));
  };

  // Add report deletion functionality
  const handleDeleteReport = async (reportId: string) => {
    try {
      await deleteReport(reportId);
      setReports(prev => prev.filter(report => report.id !== reportId));
      toast.success('Report deleted successfully');
    } catch (error) {
      toast.error('Failed to delete report');
      console.error('Error deleting report:', error);
    }
  };

  // Add report filtering functionality
  const filterOptions = [
    { value: 'all', label: 'All Reports' },
    { value: 'generated', label: 'Ready' },
    { value: 'pending', label: 'Processing' },
    { value: 'error', label: 'Failed' }
  ];

  // Auth context for user information - will be used in a real implementation
  // const { user } = useAuth(); // Commented out as not currently used
  const [searchTerm, setSearchTerm] = useState('');

  // Function to handle downloading a report
  const handleDownloadReport = async (reportId: string) => {
    console.log('Downloading report:', reportId);
    try {
      // In a real app, this would call an API to download the report
      // For now, we'll just show a success message
      toast.success('Report downloaded successfully');
    } catch (error) {
      toast.error('Failed to download report');
      console.error('Error downloading report:', error);
    }
  };

  const handleGenerateReport = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);

    try {
      // Mock report generation since we don't have a real backend
      // In a real app, this would call the API with all required parameters
      const reportId = Math.random().toString(36).substring(2, 15);
      const newReport: Report = {
        id: reportId,
        name: reportForm.name,
        description: reportForm.description,
        createdAt: new Date().toISOString(),
        status: 'generated',
        downloadUrl: '#',
        standard: 'GDPR',
        score: metrics.compliantPercentage,
        findings: policies.filter(p => p.status !== 'compliant').length,
        criticalIssues: policies.filter(p => p.status === 'non_compliant').length
      };

      setReports(prev => [newReport, ...prev]);
      toast.success('Report generated successfully');
      setActiveTab('available');
      setReportForm({
        name: '',
        description: '',
        type: 'summary',
        format: 'pdf'
      });
    } catch (error) {
      toast.error('Failed to generate report');
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Add this before the return statement
  const filteredReports = reports
    .filter(report => filterStatus === 'all' || report.status === filterStatus)
    .filter(report =>
      report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase())
    );



  return (
    <div className="bg-card rounded-lg shadow-sm p-6 dark:shadow-none">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-text">Compliance Reports</h2>
        <p className="text-text-secondary mt-1">Generate and manage compliance reports</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-border mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 -mb-px ${
            activeTab === 'available'
              ? 'border-primary text-primary'
              : 'border-transparent text-text-secondary hover:text-text'
          }`}
          onClick={() => setActiveTab('available')}
        >
          Available Reports
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 -mb-px ${
            activeTab === 'create'
              ? 'border-primary text-primary'
              : 'border-transparent text-text-secondary hover:text-text'
          }`}
          onClick={() => setActiveTab('create')}
        >
          Generate New Report
        </button>
      </div>

      {/* Available Reports Tab */}
      {activeTab === 'available' && (
        <div className="space-y-4">
          <div className="mb-4 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full sm:w-auto px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {filterOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          {reports.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-text-secondary mb-2">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-text-secondary">No reports available yet</p>
              <button
                className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-light"
                onClick={() => setActiveTab('create')}
              >
                Generate Your First Report
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-surface">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Report Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Created
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-border">
                  {filteredReports.map((report) => (
                    <tr key={report.id} className="hover:bg-surface">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-text">{report.name}</div>
                        <div className="text-sm text-text-secondary">{report.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-text-secondary">{formatDate(report.createdAt, true)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {report.status === 'generated' && (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-success bg-opacity-10 text-success">
                            Ready
                          </span>
                        )}
                        {report.status === 'pending' && (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-warning bg-opacity-10 text-warning">
                            Processing
                          </span>
                        )}
                        {report.status === 'error' && (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-error bg-opacity-10 text-error">
                            Failed
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {report.status === 'generated' && (
                          <a
                            href={report.downloadUrl}
                            download
                            className="text-primary hover:text-primary-light mr-4"
                            onClick={(e) => {
                              e.preventDefault();
                              handleDownloadReport(report.id);
                            }}
                          >
                            Download
                          </a>
                        )}
                        <button
                          onClick={() => handleDeleteReport(report.id)}
                          className="text-error hover:text-error/80"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Create Report Tab */}
      {activeTab === 'create' && (
        <form onSubmit={handleGenerateReport} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-text">
              Report Name
            </label>
            <input
              type="text"
              name="name"
              id="name"
              value={reportForm.name}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-border shadow-sm p-2 border focus:border-primary focus:ring-primary bg-surface text-text"
              placeholder="e.g., Monthly Compliance Summary"
              required
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-text">
              Description
            </label>
            <textarea
              name="description"
              id="description"
              value={reportForm.description}
              onChange={handleInputChange}
              rows={3}
              className="mt-1 block w-full rounded-md border-border shadow-sm p-2 border focus:border-primary focus:ring-primary bg-surface text-text"
              placeholder="Brief description of the report"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-text">
                Report Type
              </label>
              <select
                name="type"
                id="type"
                value={reportForm.type}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-border shadow-sm p-2 border focus:border-primary focus:ring-primary bg-surface text-text"
                required
              >
                <option value="summary">Summary Report</option>
                <option value="detailed">Detailed Report</option>
                <option value="non-compliant">Non-Compliance Report</option>
                <option value="trends">Compliance Trends</option>
              </select>
            </div>

            <div>
              <label htmlFor="format" className="block text-sm font-medium text-text">
                Format
              </label>
              <select
                name="format"
                id="format"
                value={reportForm.format}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-border shadow-sm p-2 border focus:border-primary focus:ring-primary bg-surface text-text"
                required
              >
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
                <option value="csv">CSV</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              className="px-4 py-2 border border-border rounded-md text-text bg-surface hover:bg-surface/80 mr-3"
              onClick={() => setActiveTab('available')}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-light flex items-center"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </>
              ) : (
                'Generate Report'
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};


export default ComplianceReports;