import { Chart, registerables } from 'chart.js';

// Register Chart.js components once globally
Chart.register(...registerables);

// Professional navy blue and blue-gray theme colors for charts
export const CHART_COLORS = {
  // Semantic colors optimized for navy blue and blue-gray dark mode
  compliant: {
    light: '#10B981', // Green for light mode
    dark: '#34D399',  // Strategic green highlighting
    bg: 'rgba(52, 211, 153, 0.1)', // Background with transparency
  },
  nonCompliant: {
    light: '#EF4444', // Red for light mode
    dark: '#F87171',  // Strategic red highlighting
    bg: 'rgba(248, 113, 113, 0.1)', // Background with transparency
  },
  pending: {
    light: '#F59E0B', // Amber for light mode
    dark: '#F59E0B',  // Strategic amber highlighting
    bg: 'rgba(245, 158, 11, 0.1)', // Background with transparency
  },
  // Additional accent colors - navy blue and blue-gray theme
  primary: {
    light: '#A6D933', // Brand green for light mode
    dark: '#60A5FA',  // Strategic blue highlighting
    bg: 'rgba(96, 165, 250, 0.1)',
  },
  secondary: {
    light: '#0D9488', // Teal for light mode
    dark: '#1A1F2E',  // Navy blue for secondary surfaces
    bg: 'rgba(26, 31, 46, 0.1)',
  },
  purple: {
    light: '#8B5CF6', // Purple accent for light mode (preserved for light mode)
    dark: '#1A1F2E',  // Replaced purple with navy blue in dark mode
    bg: 'rgba(26, 31, 46, 0.1)',
  }
};

// Get theme-appropriate colors
export const getChartColors = (isDark: boolean) => ({
  compliant: isDark ? CHART_COLORS.compliant.dark : CHART_COLORS.compliant.light,
  nonCompliant: isDark ? CHART_COLORS.nonCompliant.dark : CHART_COLORS.nonCompliant.light,
  pending: isDark ? CHART_COLORS.pending.dark : CHART_COLORS.pending.light,
  primary: isDark ? CHART_COLORS.primary.dark : CHART_COLORS.primary.light,
  secondary: isDark ? CHART_COLORS.secondary.dark : CHART_COLORS.secondary.light,
  purple: isDark ? CHART_COLORS.purple.dark : CHART_COLORS.purple.light,
});

// Professional navy blue and blue-gray chart theme configuration
export const getChartTheme = (isDark: boolean) => ({
  background: isDark ? '#252B42' : '#FFFFFF', // Navy blue card background
  gridColor: isDark ? '#374151' : '#E5E7EB', // Blue-gray grid lines
  textColor: isDark ? '#FFFFFF' : '#111827', // Pure white for maximum contrast
  textSecondary: isDark ? '#E2E8F0' : '#4B5563', // Light gray for secondary text
  borderColor: isDark ? '#374151' : '#E5E7EB', // Blue-gray borders
  tooltipBg: isDark ? '#1A1F2E' : '#FFFFFF', // Navy blue surface
  tooltipBorder: isDark ? '#374151' : '#E5E7EB', // Matching border color
});

export const optimizedChartDefaults = {
  responsive: true,
  maintainAspectRatio: false,
  animation: {
    duration: 300,
    easing: 'easeOutQuart'
  },
  elements: {
    line: {
      tension: 0.2,
      borderWidth: 2,
    },
    point: {
      radius: 3,
      hoverRadius: 6,
      borderWidth: 2,
    }
  },
  devicePixelRatio: window.devicePixelRatio || 1,
  redrawOnResize: false
};

// Professional chart options generator
export const createChartOptions = (isDark: boolean, customOptions: any = {}) => {
  const theme = getChartTheme(isDark);

  return {
    ...optimizedChartDefaults,
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
        align: 'start' as const,
        labels: {
          color: theme.textColor,
          font: {
            size: 12,
            weight: '500' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 10,
          boxHeight: 10
        }
      },
      tooltip: {
        backgroundColor: theme.tooltipBg,
        titleColor: theme.textColor,
        bodyColor: theme.textSecondary,
        borderColor: theme.tooltipBorder,
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        titleFont: {
          size: 12,
          weight: '600' as const,
          family: 'Inter, system-ui, sans-serif'
        },
        bodyFont: {
          size: 11,
          family: 'Inter, system-ui, sans-serif'
        },
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        usePointStyle: true
      }
    },
    scales: {
      x: {
        grid: {
          color: theme.gridColor,
          display: true,
          lineWidth: 1
        },
        ticks: {
          color: theme.textSecondary,
          font: {
            size: 11,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 8
        },
        border: {
          color: theme.borderColor,
          width: 1
        }
      },
      y: {
        grid: {
          color: theme.gridColor,
          display: true,
          lineWidth: 1
        },
        ticks: {
          color: theme.textSecondary,
          font: {
            size: 11,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 8
        },
        border: {
          color: theme.borderColor,
          width: 1
        }
      }
    },
    ...customOptions
  };
};