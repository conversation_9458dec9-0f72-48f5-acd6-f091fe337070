import { BaseApiService } from './api';
import { ConsentLevelData, AmberLevelData, RedLevelData } from './privacyDashboardService';

// API Types
export interface DataSubject {
  id: string;
  email: string;
  name: string;
  consentStatus: 'green' | 'amber' | 'red';
  consentDate: string;
  lastUpdated: string;
  consentTypes: {
    dataCollection: boolean;
    dataProcessing: boolean;
    dataSharing: boolean;
    marketing: boolean;
  };
  expiryDate: string;
  source: string;
  ipAddress: string;
  userAgent: string;
  location: {
    country: string;
    region: string;
  };
}

export interface CreateDataSubjectRequest {
  email: string;
  name: string;
  consentTypes: {
    dataCollection: boolean;
    dataProcessing: boolean;
    dataSharing: boolean;
    marketing: boolean;
  };
  source: string;
  ipAddress: string;
  userAgent: string;
  location: {
    country: string;
    region: string;
  };
  metadata?: Record<string, any>;
}

export interface UpdateDataSubjectRequest {
  consentTypes: {
    dataCollection: boolean;
    dataProcessing: boolean;
    dataSharing: boolean;
    marketing: boolean;
  };
  reason: string;
  notifyUser: boolean;
}

export interface DeleteDataSubjectRequest {
  reason: string;
  verificationToken: string;
  deleteAllData: boolean;
  notifyUser: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: {
    appliedFilters: Record<string, any>;
    availableFilters: Record<string, any[]>;
  };
}

export interface ConsentTrends {
  period: string;
  granularity: string;
  data: Array<{
    date: string;
    green: number;
    amber: number;
    red: number;
    newConsents: number;
    withdrawals: number;
  }>;
  summary: {
    totalConsents: number;
    averageGreenLevel: number;
    trendDirection: 'increasing' | 'decreasing' | 'stable';
    growthRate: number;
  };
}

export interface ComplianceMetrics {
  overall: {
    complianceScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    lastAssessment: string;
  };
  gdpr: {
    score: number;
    violations: number;
    pendingRequests: number;
  };
  dpdp: {
    score: number;
    violations: number;
    pendingRequests: number;
  };
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low';
    category: string;
    description: string;
    action: string;
  }>;
}

class PrivacyApiService extends BaseApiService {
  constructor() {
    super('/api/privacy');
  }

  // Consent Level Management
  async getGreenLevelData(): Promise<ConsentLevelData> {
    return this.get<ConsentLevelData>('/consent/green');
  }

  async getAmberLevelData(): Promise<AmberLevelData> {
    return this.get<AmberLevelData>('/consent/amber');
  }

  async getRedLevelData(): Promise<RedLevelData> {
    return this.get<RedLevelData>('/consent/red');
  }

  // Data Subject Management
  async getDataSubjects(params: {
    page?: number;
    limit?: number;
    status?: 'green' | 'amber' | 'red';
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<PaginatedResponse<DataSubject>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<DataSubject>>(`/subjects?${queryParams.toString()}`);
  }

  async getDataSubject(subjectId: string): Promise<DataSubject> {
    return this.get<DataSubject>(`/subjects/${subjectId}`);
  }

  async createDataSubject(data: CreateDataSubjectRequest): Promise<{
    id: string;
    email: string;
    consentStatus: string;
    consentDate: string;
    expiryDate: string;
    consentToken: string;
    message: string;
  }> {
    return this.post('/subjects', data);
  }

  async updateDataSubject(subjectId: string, data: UpdateDataSubjectRequest): Promise<DataSubject> {
    return this.put(`/subjects/${subjectId}`, data);
  }

  async deleteDataSubject(subjectId: string, data: DeleteDataSubjectRequest): Promise<{
    success: boolean;
    message: string;
    deletionId: string;
  }> {
    return this.delete(`/subjects/${subjectId}`, { data });
  }

  // Consent Analytics
  async getConsentTrends(params: {
    period?: '7d' | '30d' | '90d' | '1y';
    granularity?: 'hourly' | 'daily' | 'weekly' | 'monthly';
    consentType?: string;
  } = {}): Promise<ConsentTrends> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get<ConsentTrends>(`/analytics/trends?${queryParams.toString()}`);
  }

  async getComplianceMetrics(): Promise<ComplianceMetrics> {
    return this.get<ComplianceMetrics>('/analytics/compliance');
  }

  // Data Subject Requests (DSR)
  async createDataSubjectRequest(data: {
    subjectId: string;
    requestType: 'access' | 'rectification' | 'erasure' | 'portability';
    reason: string;
    verificationMethod: string;
    priority?: 'low' | 'medium' | 'high';
  }): Promise<{
    requestId: string;
    status: string;
    estimatedCompletion: string;
    verificationRequired: boolean;
  }> {
    return this.post('/dsr', data);
  }

  async getDataSubjectRequests(params: {
    page?: number;
    limit?: number;
    status?: 'pending' | 'in_progress' | 'completed' | 'rejected';
    type?: 'access' | 'rectification' | 'erasure' | 'portability';
    subjectId?: string;
  } = {}): Promise<PaginatedResponse<any>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<any>>(`/dsr?${queryParams.toString()}`);
  }

  async updateDataSubjectRequest(requestId: string, data: {
    status: string;
    notes?: string;
    completedBy?: string;
    attachments?: string[];
  }): Promise<any> {
    return this.put(`/dsr/${requestId}`, data);
  }

  // Consent Renewal
  async sendConsentRenewal(subjectId: string, data: {
    renewalType: 'email' | 'sms' | 'in_app';
    template?: string;
    scheduledFor?: string;
    reminderCount?: number;
  }): Promise<{
    renewalId: string;
    status: string;
    sentAt: string;
    expiresAt: string;
  }> {
    return this.post(`/subjects/${subjectId}/renewal`, data);
  }

  async processConsentRenewal(renewalId: string, data: {
    consentTypes: Record<string, boolean>;
    renewalToken: string;
    ipAddress: string;
    userAgent: string;
  }): Promise<{
    success: boolean;
    newConsentDate: string;
    expiryDate: string;
  }> {
    return this.post(`/renewal/${renewalId}/process`, data);
  }

  // Bulk Operations
  async bulkUpdateConsent(data: {
    subjectIds: string[];
    consentTypes: Record<string, boolean>;
    reason: string;
    notifyUsers: boolean;
  }): Promise<{
    success: boolean;
    processed: number;
    failed: number;
    errors: Array<{ subjectId: string; error: string }>;
  }> {
    return this.post('/subjects/bulk-update', data);
  }

  async bulkExport(data: {
    filters: Record<string, any>;
    format: 'csv' | 'json' | 'xlsx';
    includePersonalData: boolean;
  }): Promise<{
    exportId: string;
    status: string;
    estimatedCompletion: string;
  }> {
    return this.post('/export', data);
  }

  async getExportStatus(exportId: string): Promise<{
    status: string;
    progress: number;
    downloadUrl?: string;
    error?: string;
  }> {
    return this.get(`/export/${exportId}`);
  }
}

export const privacyApiService = new PrivacyApiService();
