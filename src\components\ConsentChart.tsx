interface ChartProps {
  data: {
    month: string;
    red: number;
    amber: number;
    green: number;
  }[];
}

const ConsentChart = ({ data }: ChartProps) => {
  const maxValue = 4;

  return (
    <div className="bg-card p-6 rounded-lg shadow-md border border-border">
      <h2 className="text-xl font-semibold mb-6 text-text">Compliance Trends</h2>

      <div className="relative h-64">
        {/* Y-axis labels */}
        <div className="absolute left-0 h-full flex flex-col justify-between">
          {Array.from({ length: maxValue + 1 }).map((_, i) => (
            <span key={i} className="text-sm text-text-secondary">
              {maxValue - i}
            </span>
          ))}
        </div>

        {/* Grid lines */}
        <div className="ml-8 h-full border-l border-gray-200 relative">
          {Array.from({ length: maxValue + 1 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-full border-t border-gray-200"
              style={{ top: `${(i * 100) / maxValue}%` }}
            />
          ))}

          {/* Chart lines */}
          <svg className="absolute inset-0" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* Red line */}
            <polyline
              points={data.map((d, i) => `${(i * 100) / (data.length - 1)},${100 - (d.red * 100) / maxValue}`).join(' ')}
              stroke="#ff3333"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* Amber line */}
            <polyline
              points={data.map((d, i) => `${(i * 100) / (data.length - 1)},${100 - (d.amber * 100) / maxValue}`).join(' ')}
              stroke="#ffcc00"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* Green line */}
            <polyline
              points={data.map((d, i) => `${(i * 100) / (data.length - 1)},${100 - (d.green * 100) / maxValue}`).join(' ')}
              stroke="#33cc99"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {/* X-axis labels */}
      <div className="ml-8 mt-2 flex justify-between">
        {data.map((d, i) => (
          <span key={i} className="text-sm text-text-secondary">{d.month}</span>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-4 flex gap-6 justify-center">
        <div className="flex items-center gap-2">
          <div className="w-4 h-1 rounded" style={{ backgroundColor: '#ff3333' }} />
          <span className="text-sm text-text font-medium">Red Data</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-1 rounded" style={{ backgroundColor: '#ffcc00' }} />
          <span className="text-sm text-text font-medium">Amber Data</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-1 rounded" style={{ backgroundColor: '#33cc99' }} />
          <span className="text-sm text-text font-medium">Green Data</span>
        </div>
      </div>
    </div>
  );
};

export default ConsentChart;