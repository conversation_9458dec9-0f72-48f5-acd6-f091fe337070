import React, { useState } from 'react';
import { useComplianceRules } from '../../context/ComplianceRulesContext';
import { ComplianceEvent, RuleEvaluationResult } from '../../services/complianceRulesService';

const ComplianceEventTester: React.FC = () => {
  const { evaluateEvent, dpdpRules, isLoading } = useComplianceRules();
  const [eventType, setEventType] = useState('');
  const [eventData, setEventData] = useState('{}');
  const [results, setResults] = useState<RuleEvaluationResult[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get unique event types from rules
  const eventTypes = Array.from(new Set(dpdpRules.map(rule => rule.trigger)));

  // Handle event type selection
  const handleEventTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setEventType(e.target.value);
    
    // Set default data template based on selected event type
    const selectedRule = dpdpRules.find(rule => rule.trigger === e.target.value);
    if (selectedRule) {
      const dataTemplate: Record<string, any> = {};
      selectedRule.conditions.forEach(condition => {
        dataTemplate[condition.field] = '';
      });
      setEventData(JSON.stringify(dataTemplate, null, 2));
    } else {
      setEventData('{}');
    }
  };

  // Handle event data change
  const handleEventDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEventData(e.target.value);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    try {
      // Parse event data
      const parsedData = JSON.parse(eventData);
      
      // Create event object
      const event: ComplianceEvent = {
        type: eventType,
        data: parsedData,
        timestamp: new Date().toISOString(),
        source: 'compliance-tester'
      };
      
      // Evaluate event
      const evaluationResults = await evaluateEvent(event);
      setResults(evaluationResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  // Get status badge color
  const getStatusColor = (compliant: boolean) => {
    return compliant 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800';
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-4 py-5 sm:px-6 bg-gray-50">
        <h3 className="text-lg leading-6 font-medium text-gray-900">Compliance Event Tester</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Test how compliance rules would evaluate specific events.
        </p>
      </div>
      
      <div className="px-4 py-5 sm:p-6">
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div>
              <label htmlFor="event-type" className="block text-sm font-medium text-gray-700">
                Event Type
              </label>
              <select
                id="event-type"
                name="event-type"
                value={eventType}
                onChange={handleEventTypeChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                required
              >
                <option value="">Select an event type</option>
                {eventTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="event-data" className="block text-sm font-medium text-gray-700">
                Event Data (JSON)
              </label>
              <div className="mt-1">
                <textarea
                  id="event-data"
                  name="event-data"
                  rows={5}
                  value={eventData}
                  onChange={handleEventDataChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md font-mono"
                  placeholder='{"field": "value"}'
                  required
                />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                Enter the event data as a JSON object.
              </p>
            </div>
            
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading ? 'Evaluating...' : 'Evaluate Event'}
              </button>
            </div>
          </div>
        </form>
        
        {results.length > 0 && (
          <div className="mt-8">
            <h4 className="text-lg font-medium text-gray-900">Evaluation Results</h4>
            <div className="mt-4 overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Rule ID</th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Message</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {results.map((result, index) => (
                    <tr key={index}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{result.rule_id}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(result.compliant)}`}>
                          {result.compliant ? 'Compliant' : 'Non-Compliant'}
                        </span>
                      </td>
                      <td className="px-3 py-4 text-sm text-gray-500">{result.message}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComplianceEventTester;
