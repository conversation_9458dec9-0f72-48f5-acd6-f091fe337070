import React, { useState } from 'react';
import { ArrowLeft, CheckCircle2, Users, TrendingUp, Calendar, Shield, Award, FileCheck, RefreshCw, Download, FileText, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { useDataPrivacyAcknowledgementData } from '../../hooks/usePrivacyDashboard';
import { FullPageSkeleton, ErrorState } from './LoadingSkeleton';
import { toast } from 'react-toastify';

const DataPrivacyAcknowledgement: React.FC = () => {
  const navigate = useNavigate();
  const { mode } = useTheme();
  const { data, isLoading, error, refresh, exportData, generateReport } = useDataPrivacyAcknowledgementData();
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleBackClick = () => {
    navigate('/');
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    setActionLoading('export');
    try {
      await exportData(format);
    } finally {
      setActionLoading(null);
    }
  };

  const handleGenerateReport = async () => {
    setActionLoading('report');
    try {
      await generateReport();
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefresh = async () => {
    setActionLoading('refresh');
    try {
      await refresh();
      toast.success('Data refreshed successfully!');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return <FullPageSkeleton />;
  }

  if (error || !data) {
    return (
      <ErrorState
        error={error || 'Failed to load data privacy acknowledgement data'}
        onRetry={refresh}
        onBack={handleBackClick}
      />
    );
  }

  return (
    <div className="flex-1 bg-background">
      <div className="p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackClick}
              className="flex items-center text-text-secondary hover:text-text transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Privacy Dashboard
            </button>
            <button
              onClick={handleRefresh}
              disabled={actionLoading === 'refresh'}
              className="flex items-center px-3 py-2 text-sm border border-border rounded-md hover:bg-surface transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${actionLoading === 'refresh' ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          <h1 className="text-sm text-text-secondary">Customer privacy management portal</h1>
          <h2 className="text-2xl font-bold text-text">Data Privacy Acknowledgement</h2>
          <p className="text-text-secondary mt-1">Last updated: {data.lastUpdated.toLocaleString()}</p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Overview Card */}
          <div className="lg:col-span-2 bg-card rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full border-4 flex items-center justify-center bg-surface mr-4"
                   style={{ borderColor: 'var(--dashboard-purple)' }}>
                <Award className="w-8 h-8" style={{ color: 'var(--dashboard-purple)' }} />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text">Privacy Policy Acknowledgement</h3>
                <p className="text-text-secondary">{data.percentage.toFixed(1)}% acknowledgement rate achieved</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-text mb-3">Overview</h4>
                <p className="text-text-secondary leading-relaxed">
                  Data Privacy Acknowledgement tracks the percentage of users who have acknowledged and accepted
                  your privacy policies. This metric ensures that all data subjects are informed about how their
                  data is collected, processed, and used, maintaining transparency and regulatory compliance.
                </p>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <CheckCircle2 className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-purple)' }} />
                  <h5 className="font-medium text-text">Perfect Compliance Achievement</h5>
                </div>
                <p className="text-sm text-text-secondary">
                  Congratulations! You have achieved 100% privacy policy acknowledgement across all data subjects.
                  This demonstrates excellent privacy governance and user engagement.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('User acknowledgement details coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Users className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-purple)' }} />
                    <span className="font-medium text-text">Total Acknowledged</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.totalAcknowledged.toLocaleString()}</p>
                  <p className="text-sm text-text-secondary">All active users</p>
                </div>

                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Completion rate analytics coming soon!')}>
                  <div className="flex items-center mb-2">
                    <TrendingUp className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-purple)' }} />
                    <span className="font-medium text-text">Completion Rate</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.completionRate.toFixed(0)}%</p>
                  <p className="text-sm text-text-secondary">Perfect score</p>
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-text mb-3">Acknowledgement Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Privacy policy details coming soon!')}>
                      <span className="text-text-secondary">Privacy Policy v2.1</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.privacyPolicy}%</span>
                    </div>
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Cookie policy details coming soon!')}>
                      <span className="text-text-secondary">Cookie Policy</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.cookiePolicy}%</span>
                    </div>
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Terms of service details coming soon!')}>
                      <span className="text-text-secondary">Terms of Service</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.termsOfService}%</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Data processing notice details coming soon!')}>
                      <span className="text-text-secondary">Data Processing Notice</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.dataProcessingNotice}%</span>
                    </div>
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Marketing preferences details coming soon!')}>
                      <span className="text-text-secondary">Marketing Preferences</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.marketingPreferences}%</span>
                    </div>
                    <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                         onClick={() => toast.info('Third-party sharing details coming soon!')}>
                      <span className="text-text-secondary">Third-party Sharing</span>
                      <span className="font-medium text-text">{data.acknowledgementDetails.thirdPartySharing}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div className="space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Recent Activity</h4>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 text-text-secondary" />
                  <span className="text-text-secondary">Last update: Today</span>
                </div>
                <div className="text-sm text-text-secondary">
                  <p>• {data.recentActivity.newAcknowledgements} new acknowledgements today</p>
                  <p>• {data.recentActivity.policyUpdatesAcknowledged} policy updates acknowledged</p>
                  <p>• {data.recentActivity.pendingAcknowledgements} pending acknowledgements</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Compliance Status</h4>
              <div className="space-y-3">
                <div className="flex items-center mb-2">
                  <Shield className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-purple)' }} />
                  <span className="font-medium text-text">Fully Compliant</span>
                </div>
                <div className="text-sm text-text-secondary">
                  <p>✓ GDPR Requirements Met</p>
                  <p>✓ CCPA Requirements Met</p>
                  <p>✓ Internal Policies Met</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Performance Metrics</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Avg. Response Time</span>
                  <span className="font-medium text-text">{data.performanceMetrics.avgResponseTime}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Completion Rate</span>
                  <span className="font-medium text-text">{data.performanceMetrics.completionRate.toFixed(0)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">User Satisfaction</span>
                  <span className="font-medium text-text">{data.performanceMetrics.userSatisfaction}/5</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            onClick={() => handleExport('pdf')}
            disabled={actionLoading === 'export'}
            className="flex items-center px-6 py-2 rounded transition-colors disabled:opacity-50"
            style={{
              backgroundColor: 'var(--dashboard-purple)',
              color: 'white'
            }}
          >
            {actionLoading === 'export' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Download Acknowledgement Report
          </button>
          <button
            onClick={handleGenerateReport}
            disabled={actionLoading === 'report'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'report' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FileText className="w-4 h-4 mr-2" />
            )}
            View Policy Analytics
          </button>
          <button
            onClick={() => toast.info('Policy management coming soon!')}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text"
          >
            <Settings className="w-4 h-4 mr-2" />
            Update Privacy Policies
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataPrivacyAcknowledgement;
