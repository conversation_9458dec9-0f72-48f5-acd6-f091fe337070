// Theme configuration
export type ThemeMode = 'light' | 'dark';

// Primary color: #4F46E5 (indigo)
// We'll use the same color for both light and dark modes for consistency

interface ThemeColors {
  // Primary color and variants
  primary: string;
  primaryLight: string;
  primaryDark: string;
  primaryHover: string;
  primaryActive: string;
  primaryFocus: string;

  // Secondary color and variants
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  secondaryHover: string;
  secondaryActive: string;

  // Accent colors
  accentBlue: string;
  accentTeal: string;
  accentPurple: string;

  // Background and surface colors
  background: string;
  surface: string;
  cardBackground: string;

  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;

  // Border colors
  border: string;
  borderLight: string;
  borderFocus: string;

  // Semantic colors
  error: string;
  errorLight: string;
  errorDark: string;
  errorBg: string;

  success: string;
  successLight: string;
  successDark: string;
  successBg: string;

  warning: string;
  warningLight: string;
  warningDark: string;
  warningBg: string;

  info: string;
  infoLight: string;
  infoDark: string;
  infoBg: string;

  // Shadow values
  shadowSm: string;
  shadow: string;
  shadowMd: string;
  shadowLg: string;
  shadowXl: string;
  shadowInner: string;
  shadowFocus: string;

  // Chart colors
  chart: {
    background: string;
    gridLines: string;
    text: string;
    textSecondary: string;
  };

  // Dashboard colors
  dashboard: {
    green: string;
    amber: string;
    red: string;
    purple: string;
    buttonBg: string;
    buttonText: string;
  };
}

export const lightTheme: ThemeColors = {
  // Primary color and variants
  primary: '#A6D933', // Green (primary color)
  primaryLight: '#B5E350', // Lighter shade of green
  primaryDark: '#8AB929', // Darker shade of green
  primaryHover: '#B5E350', // Hover state (+10% lightness)
  primaryActive: '#8AB929', // Active state (-10% lightness)
  primaryFocus: '#A6D933', // Focus state (same as primary)

  // Secondary color and variants
  secondary: '#0D9488', // Teal as complementary color
  secondaryLight: '#14B8A6', // Lighter teal
  secondaryDark: '#0F766E', // Darker teal
  secondaryHover: '#14B8A6', // Hover state
  secondaryActive: '#0F766E', // Active state

  // Accent colors
  accentBlue: '#A6D933', // Green accent (consistent with brand color)
  accentTeal: '#0D9488', // Teal accent
  accentPurple: '#8B5CF6', // Purple accent

  // Background and surface colors
  background: '#F9FAFB', // Light background (slightly warmer than before)
  surface: '#FFFFFF', // White surface
  cardBackground: '#FFFFFF', // Card background

  // Text colors
  text: '#111827', // Dark text for light mode (improved contrast)
  textSecondary: '#4B5563', // Secondary text color (improved contrast)
  textTertiary: '#6B7280', // Tertiary text color

  // Border colors
  border: '#E5E7EB', // Light border color
  borderLight: '#F3F4F6', // Lighter border color
  borderFocus: '#A6D933', // Focus border color (primary)

  // Semantic colors
  error: '#EF4444', // Error color (red)
  errorLight: '#FEE2E2', // Light error background
  errorDark: '#B91C1C', // Dark error color
  errorBg: '#FEF2F2', // Error background

  success: '#10B981', // Success color (green)
  successLight: '#D1FAE5', // Light success background
  successDark: '#047857', // Dark success color
  successBg: '#ECFDF5', // Success background

  warning: '#F59E0B', // Warning color (amber)
  warningLight: '#FEF3C7', // Light warning background
  warningDark: '#B45309', // Dark warning color
  warningBg: '#FFFBEB', // Warning background

  info: '#A6D933', // Info color (green)
  infoLight: '#EBF7C6', // Light info background
  infoDark: '#8AB929', // Dark info color
  infoBg: '#F5FBDF', // Info background

  // Shadow values with subtle green tint
  shadowSm: '0 1px 2px 0 rgba(0, 0, 0, 0.05), 0 1px 1px 0 rgba(166, 217, 51, 0.03)',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(166, 217, 51, 0.06)',
  shadowMd: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(166, 217, 51, 0.08)',
  shadowLg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(166, 217, 51, 0.05)',
  shadowXl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(166, 217, 51, 0.04)',
  shadowInner: 'inset 0 2px 4px 0 rgba(166, 217, 51, 0.08)',
  shadowFocus: '0 0 0 3px rgba(166, 217, 51, 0.45)',

  // Chart colors
  chart: {
    background: '#FFFFFF',
    gridLines: '#E5E7EB',
    text: '#111827',
    textSecondary: '#4B5563',
  },

  // Dashboard colors
  dashboard: {
    green: '#10B981',
    amber: '#F59E0B',
    red: '#EF4444',
    purple: '#8B5CF6',
    buttonBg: '#F3F4F6',
    buttonText: '#111827',
  },
};

export const darkTheme: ThemeColors = {
  // Primary color and variants - Navy blue and blue-gray professional theme
  primary: '#60A5FA', // Strategic highlighting blue
  primaryLight: '#93C5FD', // Lighter blue for highlights and hover states
  primaryDark: '#3B82F6', // Deeper blue for active states
  primaryHover: '#93C5FD', // Enhanced hover with lighter blue
  primaryActive: '#3B82F6', // Active state with deeper blue
  primaryFocus: '#60A5FA', // Focus state maintaining primary blue

  // Secondary color and variants - Navy blue for secondary surfaces
  secondary: '#1A1F2E', // Navy blue for secondary surfaces and navigation
  secondaryLight: '#252B42', // Lighter navy for elevated elements
  secondaryDark: '#0F1419', // Deeper navy for depth
  secondaryHover: '#252B42', // Enhanced hover state
  secondaryActive: '#0F1419', // Enhanced active state

  // Accent colors - Strategic highlighting system
  accentBlue: '#60A5FA', // Primary highlighting blue
  accentTeal: '#34D399', // Success state green
  accentPurple: '#1A1F2E', // Replaced purple with navy blue

  // Background and surface colors - Blue-gray and navy professional theme
  background: '#344054', // Blue-gray primary background
  surface: '#1A1F2E', // Navy blue surface for navigation and secondary elements
  cardBackground: '#252B42', // Elevated card background

  // Text colors - High contrast for navy blue and blue-gray backgrounds
  text: '#FFFFFF', // Pure white for maximum contrast
  textSecondary: '#E2E8F0', // Light gray for secondary text
  textTertiary: '#CBD5E1', // Medium gray for subtle text

  // Border colors - Navy blue and blue-gray tinted borders
  border: '#374151', // Blue-gray border matching surface elevation
  borderLight: '#4B5563', // Lighter blue-gray for subtle divisions
  borderFocus: '#60A5FA', // Primary blue for focus states

  // Semantic colors - Strategic highlighting system with high contrast
  error: '#F87171', // Softer red that works well with blue backgrounds
  errorLight: '#7F1D1D', // Dark red background maintaining depth
  errorDark: '#EF4444', // Vibrant red for emphasis
  errorBg: '#2D1B1B', // Subtle error background with blue undertone

  success: '#34D399', // Bright teal-green for success (from reference)
  successLight: '#065F46', // Dark teal background
  successDark: '#10B981', // Professional teal for emphasis
  successBg: '#1A2E2A', // Subtle success background with blue undertone

  warning: '#FBBF24', // Bright amber for warnings (high contrast)
  warningLight: '#92400E', // Dark amber background
  warningDark: '#F59E0B', // Professional amber for emphasis
  warningBg: '#2D2416', // Subtle warning background with blue undertone

  info: '#60A5FA', // Light blue for info states (from reference)
  infoLight: '#1E3A8A', // Deep blue background
  infoDark: '#3B82F6', // Primary blue for emphasis
  infoBg: '#1A2332', // Subtle info background matching theme

  // Shadow values - Navy blue and blue-gray themed shadows for sophisticated depth
  shadowSm: '0 1px 2px 0 rgba(0, 0, 0, 0.5), 0 1px 1px 0 rgba(15, 20, 25, 0.2)',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.6), 0 1px 2px 0 rgba(15, 20, 25, 0.25)',
  shadowMd: '0 4px 6px -1px rgba(0, 0, 0, 0.7), 0 2px 4px -1px rgba(15, 20, 25, 0.3)',
  shadowLg: '0 10px 15px -3px rgba(0, 0, 0, 0.8), 0 4px 6px -2px rgba(15, 20, 25, 0.25)',
  shadowXl: '0 20px 25px -5px rgba(0, 0, 0, 0.9), 0 10px 10px -5px rgba(15, 20, 25, 0.2)',
  shadowInner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.4)',
  shadowFocus: '0 0 0 3px rgba(96, 165, 250, 0.4)', // Strategic blue focus

  // Chart colors - Navy blue and blue-gray theme optimized for data visualization
  chart: {
    background: '#252B42', // Matches elevated card background
    gridLines: '#374151', // Blue-gray grid lines
    text: '#FFFFFF', // Pure white for maximum contrast
    textSecondary: '#E2E8F0', // Light gray for secondary text
  },

  // Dashboard colors - Strategic highlighting system
  dashboard: {
    green: '#34D399', // Strategic green highlighting
    amber: '#F59E0B', // Strategic amber highlighting
    red: '#F87171', // Strategic red highlighting
    purple: '#1A1F2E', // Replaced purple with navy blue
    buttonBg: '#1A1F2E', // Navy blue button background
    buttonText: '#FFFFFF', // Pure white button text
  },
};

export const getTheme = (mode: ThemeMode): ThemeColors => {
  return mode === 'light' ? lightTheme : darkTheme;
};

// CSS variables to be injected into the document root
export const generateCssVariables = (theme: ThemeColors): string => {
  return `
    /* Primary colors */
    --color-primary: ${theme.primary};
    --color-primary-light: ${theme.primaryLight};
    --color-primary-dark: ${theme.primaryDark};
    --color-primary-hover: ${theme.primaryHover};
    --color-primary-active: ${theme.primaryActive};
    --color-primary-focus: ${theme.primaryFocus};

    /* Secondary colors */
    --color-secondary: ${theme.secondary};
    --color-secondary-light: ${theme.secondaryLight};
    --color-secondary-dark: ${theme.secondaryDark};
    --color-secondary-hover: ${theme.secondaryHover};
    --color-secondary-active: ${theme.secondaryActive};

    /* Accent colors */
    --color-accent-blue: ${theme.accentBlue};
    --color-accent-teal: ${theme.accentTeal};
    --color-accent-purple: ${theme.accentPurple};

    /* Background and surface colors */
    --color-background: ${theme.background};
    --color-surface: ${theme.surface};
    --color-card-bg: ${theme.cardBackground};

    /* Text colors */
    --color-text: ${theme.text};
    --color-text-secondary: ${theme.textSecondary};
    --color-text-tertiary: ${theme.textTertiary};

    /* Border colors */
    --color-border: ${theme.border};
    --color-border-light: ${theme.borderLight};
    --color-border-focus: ${theme.borderFocus};

    /* Semantic colors */
    --color-error: ${theme.error};
    --color-error-light: ${theme.errorLight};
    --color-error-dark: ${theme.errorDark};
    --color-error-bg: ${theme.errorBg};

    --color-success: ${theme.success};
    --color-success-light: ${theme.successLight};
    --color-success-dark: ${theme.successDark};
    --color-success-bg: ${theme.successBg};

    --color-warning: ${theme.warning};
    --color-warning-light: ${theme.warningLight};
    --color-warning-dark: ${theme.warningDark};
    --color-warning-bg: ${theme.warningBg};

    --color-info: ${theme.info};
    --color-info-light: ${theme.infoLight};
    --color-info-dark: ${theme.infoDark};
    --color-info-bg: ${theme.infoBg};

    /* Shadow values */
    --shadow-sm: ${theme.shadowSm};
    --shadow: ${theme.shadow};
    --shadow-md: ${theme.shadowMd};
    --shadow-lg: ${theme.shadowLg};
    --shadow-xl: ${theme.shadowXl};
    --shadow-inner: ${theme.shadowInner};
    --shadow-focus: ${theme.shadowFocus};

    /* Chart colors */
    --chart-bg: ${theme.chart.background};
    --chart-grid: ${theme.chart.gridLines};
    --chart-text: ${theme.chart.text};
    --chart-text-secondary: ${theme.chart.textSecondary};

    /* Dashboard colors */
    --dashboard-green: ${theme.dashboard.green};
    --dashboard-amber: ${theme.dashboard.amber};
    --dashboard-red: ${theme.dashboard.red};
    --dashboard-purple: ${theme.dashboard.purple};
    --dashboard-button-bg: ${theme.dashboard.buttonBg};
    --dashboard-button-text: ${theme.dashboard.buttonText};

    /* Transition speeds */
    --transition-fast: 150ms;
    --transition-normal: 250ms;
    --transition-slow: 350ms;
  `;
};
