import React, { useState } from 'react';
import { PolicyList } from './PolicyList';
import { Sparklines, SparklinesLine } from 'react-sparklines';

interface ViolationCard {
  count: number;
  type: 'Critical' | 'Moderate' | 'Marginal';
  color: string;
  policies: string[];
}

export const ViolationsSummary: React.FC = () => {
  const [selectedViolation, setSelectedViolation] = useState<ViolationCard | null>(null);
  const [showPolicyList, setShowPolicyList] = useState(false);

  const violations: ViolationCard[] = [
    {
      count: 17,
      type: 'Critical',
      color: 'bg-red-500',
      policies: [
        'Regulatory Compliance Policies',
        'Data Encryption',
        'Secure Storage',
        'Multi-Factor Authentication (MFA)',
        'Role-Based Access Control (RBAC)',
        'Privileged Access Management (PAM)',
        'Biometric Authentication',
        'Incident Response Plans',
        'Risk Management Frameworks',
        'Employee Security Awareness Training',
        'Continuous Monitoring',
        'Auditing and Logging',
        'Data Classification Policies',
        'Data Masking and Anonymization',
        'Secure Software Development Practices',
        'Patch Management',
        'Zero Trust Architecture (ZTA)',
      ]
    },
    {
      count: 3,
      type: 'Moderate',
      color: 'bg-orange-500',
      policies: [
        'Third-Party Risk Management',
        'Backup Policies',
        'Endpoint Security',
      ]
    },
    {
      count: 5,
      type: 'Marginal',
      color: 'bg-yellow-500',
      policies: [
        'Network Security Policies',
        'Data Retention Policies',
        'Fraud Detection Systems',
        'Breach Notification Protocols',
        'Governance Frameworks',
      ]
    }
  ];

  const handleViolationClick = (violation: ViolationCard) => {
    setSelectedViolation(violation);
    setShowPolicyList(true);
  };

  return (
    <div className="bg-card p-6 rounded-xl shadow-lg border border-border">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-text">Violations Summary</h2>
          <p className="text-sm text-text-secondary mt-1">Overview of policy violations by severity</p>
        </div>
        <div className="flex gap-2">
          <button className="px-4 py-2 text-sm font-medium text-text-secondary hover:text-text transition-colors">
            Export
          </button>
          <button className="px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors">
            View All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {violations.map((violation) => (
          <button
            key={violation.type}
            onClick={() => handleViolationClick(violation)}
            className="group relative bg-card rounded-xl p-6 hover:shadow-md transition-all duration-200 border border-border hover:border-purple-300 dark:hover:border-purple-600"
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <div className="text-3xl font-bold text-text">{violation.count}</div>
                <div className="text-sm text-text-secondary">{violation.type} Violations</div>
              </div>
              <div className={`${violation.color} w-8 h-8 rounded-lg opacity-80`} />
            </div>

            <Sparklines data={[5, 10, 5, 20, 8, 15, 12, 8, 10]} height={30}>
              <SparklinesLine color={violation.type === 'Critical' ? '#ef4444' :
                violation.type === 'Moderate' ? '#f97316' : '#eab308'} />
            </Sparklines>

            <div className="mt-4 text-xs text-text-secondary">
              Last updated: {new Date().toLocaleDateString()}
            </div>
          </button>
        ))}
      </div>

      {showPolicyList && selectedViolation && (
        <PolicyList
          type={selectedViolation.type}
          policies={selectedViolation.policies}
          onClose={() => setShowPolicyList(false)}
        />
      )}
    </div>
  );
};
