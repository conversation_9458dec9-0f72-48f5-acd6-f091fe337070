import React, { useState, useMemo } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { usePerformanceMetrics } from '../../hooks/useSiteReliability';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Activity,
  Cpu,
  HardDrive,
  Network,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Calendar,
  Filter,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PerformanceMetricsCollectionProps {
  className?: string;
}

export const PerformanceMetricsCollection: React.FC<PerformanceMetricsCollectionProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const { performanceMetrics, isLoading, error, refresh } = usePerformanceMetrics(selectedTimeRange);
  
  const [selectedMetric, setSelectedMetric] = useState<'response_time' | 'throughput' | 'error_rate' | 'system'>('response_time');

  const chartTheme = getChartTheme(mode === 'dark');

  // Calculate current performance statistics
  const currentStats = useMemo(() => {
    if (!performanceMetrics || performanceMetrics.length === 0) {
      return {
        avgResponseTime: 0,
        avgThroughput: 0,
        avgErrorRate: 0,
        avgCpuUsage: 0,
        avgMemoryUsage: 0,
        avgDiskUsage: 0,
        avgNetworkLatency: 0,
        responseTimeStatus: 'unknown',
        throughputStatus: 'unknown',
        errorRateStatus: 'unknown',
        systemHealthStatus: 'unknown'
      };
    }

    const latest = performanceMetrics[performanceMetrics.length - 1];
    const avgResponseTime = performanceMetrics.reduce((sum, m) => sum + m.responseTime, 0) / performanceMetrics.length;
    const avgThroughput = performanceMetrics.reduce((sum, m) => sum + m.throughput, 0) / performanceMetrics.length;
    const avgErrorRate = performanceMetrics.reduce((sum, m) => sum + m.errorRate, 0) / performanceMetrics.length;
    const avgCpuUsage = performanceMetrics.reduce((sum, m) => sum + m.cpuUsage, 0) / performanceMetrics.length;
    const avgMemoryUsage = performanceMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / performanceMetrics.length;
    const avgDiskUsage = performanceMetrics.reduce((sum, m) => sum + m.diskUsage, 0) / performanceMetrics.length;
    const avgNetworkLatency = performanceMetrics.reduce((sum, m) => sum + m.networkLatency, 0) / performanceMetrics.length;

    // Determine status based on thresholds
    const responseTimeStatus = latest.responseTime < 200 ? 'healthy' : latest.responseTime < 500 ? 'warning' : 'critical';
    const throughputStatus = latest.throughput > 1000 ? 'healthy' : latest.throughput > 500 ? 'warning' : 'critical';
    const errorRateStatus = latest.errorRate < 0.1 ? 'healthy' : latest.errorRate < 1 ? 'warning' : 'critical';
    const systemHealthStatus = (latest.cpuUsage < 70 && latest.memoryUsage < 80) ? 'healthy' : 
                              (latest.cpuUsage < 85 && latest.memoryUsage < 90) ? 'warning' : 'critical';

    return {
      avgResponseTime,
      avgThroughput,
      avgErrorRate,
      avgCpuUsage,
      avgMemoryUsage,
      avgDiskUsage,
      avgNetworkLatency,
      responseTimeStatus,
      throughputStatus,
      errorRateStatus,
      systemHealthStatus
    };
  }, [performanceMetrics]);

  // Generate chart data based on selected metric
  const chartData = useMemo(() => {
    if (!performanceMetrics) return null;

    const labels = performanceMetrics.map(m => 
      new Date(m.timestamp).toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      })
    );

    switch (selectedMetric) {
      case 'response_time':
        return {
          labels,
          datasets: [{
            label: 'Response Time (ms)',
            data: performanceMetrics.map(m => m.responseTime),
            borderColor: 'rgb(79, 142, 247)',
            backgroundColor: 'rgba(79, 142, 247, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 2,
            pointHoverRadius: 4,
          }]
        };
      
      case 'throughput':
        return {
          labels,
          datasets: [{
            label: 'Throughput (req/s)',
            data: performanceMetrics.map(m => m.throughput),
            borderColor: 'rgb(52, 211, 153)',
            backgroundColor: 'rgba(52, 211, 153, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 2,
            pointHoverRadius: 4,
          }]
        };
      
      case 'error_rate':
        return {
          labels,
          datasets: [{
            label: 'Error Rate (%)',
            data: performanceMetrics.map(m => m.errorRate),
            borderColor: 'rgb(248, 113, 113)',
            backgroundColor: 'rgba(248, 113, 113, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 2,
            pointHoverRadius: 4,
          }]
        };
      
      case 'system':
        return {
          labels,
          datasets: [
            {
              label: 'CPU Usage (%)',
              data: performanceMetrics.map(m => m.cpuUsage),
              borderColor: 'rgb(139, 92, 246)',
              backgroundColor: 'rgba(139, 92, 246, 0.1)',
              borderWidth: 2,
              fill: false,
              tension: 0.4,
              pointRadius: 2,
              pointHoverRadius: 4,
            },
            {
              label: 'Memory Usage (%)',
              data: performanceMetrics.map(m => m.memoryUsage),
              borderColor: 'rgb(251, 191, 36)',
              backgroundColor: 'rgba(251, 191, 36, 0.1)',
              borderWidth: 2,
              fill: false,
              tension: 0.4,
              pointRadius: 2,
              pointHoverRadius: 4,
            }
          ]
        };
      
      default:
        return null;
    }
  }, [performanceMetrics, selectedMetric]);

  // System resource usage chart
  const systemResourcesData = useMemo(() => {
    if (!performanceMetrics || performanceMetrics.length === 0) return null;

    const latest = performanceMetrics[performanceMetrics.length - 1];
    
    return {
      labels: ['CPU', 'Memory', 'Disk', 'Network'],
      datasets: [{
        label: 'Usage (%)',
        data: [
          latest.cpuUsage,
          latest.memoryUsage,
          latest.diskUsage,
          (latest.networkLatency / 100) * 100 // Convert latency to percentage for visualization
        ],
        backgroundColor: [
          latest.cpuUsage > 80 ? 'rgba(248, 113, 113, 0.8)' : latest.cpuUsage > 60 ? 'rgba(251, 191, 36, 0.8)' : 'rgba(52, 211, 153, 0.8)',
          latest.memoryUsage > 85 ? 'rgba(248, 113, 113, 0.8)' : latest.memoryUsage > 70 ? 'rgba(251, 191, 36, 0.8)' : 'rgba(52, 211, 153, 0.8)',
          latest.diskUsage > 90 ? 'rgba(248, 113, 113, 0.8)' : latest.diskUsage > 75 ? 'rgba(251, 191, 36, 0.8)' : 'rgba(52, 211, 153, 0.8)',
          latest.networkLatency > 50 ? 'rgba(248, 113, 113, 0.8)' : latest.networkLatency > 30 ? 'rgba(251, 191, 36, 0.8)' : 'rgba(52, 211, 153, 0.8)'
        ],
        borderColor: [
          'rgb(248, 113, 113)',
          'rgb(251, 191, 36)',
          'rgb(52, 211, 153)',
          'rgb(139, 92, 246)'
        ],
        borderWidth: 2,
      }]
    };
  }, [performanceMetrics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  if (isLoading && !performanceMetrics) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Performance Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Activity className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-text">Performance Metrics Collection</h2>
            <p className="text-sm text-text-secondary">
              Real-time monitoring of system performance and resource utilization
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={refresh}
            disabled={isLoading}
            className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
            title="Refresh data"
          >
            <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          
          <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Performance Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-primary" />
              <p className="text-sm text-text-secondary">Response Time</p>
            </div>
            {getStatusIcon(currentStats.responseTimeStatus)}
          </div>
          <p className="text-2xl font-bold text-text">{currentStats.avgResponseTime.toFixed(0)}ms</p>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(currentStats.responseTimeStatus)}`}>
            {currentStats.responseTimeStatus.charAt(0).toUpperCase() + currentStats.responseTimeStatus.slice(1)}
          </span>
        </div>

        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-primary" />
              <p className="text-sm text-text-secondary">Throughput</p>
            </div>
            {getStatusIcon(currentStats.throughputStatus)}
          </div>
          <p className="text-2xl font-bold text-text">{currentStats.avgThroughput.toFixed(0)}</p>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(currentStats.throughputStatus)}`}>
            req/s
          </span>
        </div>

        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-primary" />
              <p className="text-sm text-text-secondary">Error Rate</p>
            </div>
            {getStatusIcon(currentStats.errorRateStatus)}
          </div>
          <p className="text-2xl font-bold text-text">{currentStats.avgErrorRate.toFixed(2)}%</p>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(currentStats.errorRateStatus)}`}>
            {currentStats.errorRateStatus.charAt(0).toUpperCase() + currentStats.errorRateStatus.slice(1)}
          </span>
        </div>

        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Cpu className="w-4 h-4 text-primary" />
              <p className="text-sm text-text-secondary">System Health</p>
            </div>
            {getStatusIcon(currentStats.systemHealthStatus)}
          </div>
          <p className="text-2xl font-bold text-text">{currentStats.avgCpuUsage.toFixed(0)}%</p>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(currentStats.systemHealthStatus)}`}>
            CPU
          </span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-text-secondary" />
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-text-secondary" />
          <select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="response_time">Response Time</option>
            <option value="throughput">Throughput</option>
            <option value="error_rate">Error Rate</option>
            <option value="system">System Resources</option>
          </select>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Main Performance Chart */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">
            {selectedMetric === 'response_time' && 'Response Time Trend'}
            {selectedMetric === 'throughput' && 'Throughput Trend'}
            {selectedMetric === 'error_rate' && 'Error Rate Trend'}
            {selectedMetric === 'system' && 'System Resource Usage'}
          </h3>
          {chartData && (
            <div className="h-64">
              <Line
                data={chartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* System Resources Chart */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">Current System Resources</h3>
          {systemResourcesData && (
            <div className="h-64">
              <Bar
                data={systemResourcesData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => {
                          if (context.label === 'Network') {
                            return `Network Latency: ${(performanceMetrics?.[performanceMetrics.length - 1]?.networkLatency || 0).toFixed(1)}ms`;
                          }
                          return `${context.label}: ${context.parsed.y.toFixed(1)}%`;
                        }
                      }
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 0,
                      max: 100
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="bg-card rounded-lg p-4 border border-border">
        <h3 className="text-lg font-semibold text-text mb-4">Detailed System Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-text-secondary flex items-center gap-2">
              <Cpu className="w-4 h-4" />
              CPU & Processing
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average CPU Usage</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgCpuUsage.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Response Time</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgResponseTime.toFixed(0)}ms</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Throughput</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgThroughput.toFixed(0)} req/s</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-text-secondary flex items-center gap-2">
              <HardDrive className="w-4 h-4" />
              Memory & Storage
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Memory Usage</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgMemoryUsage.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Disk Usage</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgDiskUsage.toFixed(1)}%</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-text-secondary flex items-center gap-2">
              <Network className="w-4 h-4" />
              Network & Errors
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Network Latency</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgNetworkLatency.toFixed(1)}ms</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Average Error Rate</span>
                <span className="text-sm font-semibold text-text">{currentStats.avgErrorRate.toFixed(2)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
