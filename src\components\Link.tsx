import React, { ReactNode } from 'react';

interface LinkProps {
  href: string;
  children: ReactNode;
  icon?: ReactNode;
  active?: boolean;
  className?: string;
  onClick?: () => void;
}

export const Link = ({ href, children, icon, active, className = '', onClick }: LinkProps) => {
  return (
    <a
      href={href}
      onClick={(e) => {
        e.preventDefault();
        onClick?.();
      }}
      className={`
        flex items-center gap-3 px-4 py-2.5
        text-text font-medium
        hover:bg-primary/5 hover:text-primary
        active:bg-primary/10
        rounded-lg transition-all duration-250
        focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-opacity-50
        ${active ? 'bg-primary/10 text-primary' : ''}
        ${className}
      `}
    >
      {icon && (
        <span className={`
          flex items-center justify-center
          ${active ? 'text-primary' : 'text-text-secondary'}
        `}>
          {icon}
        </span>
      )}
      <span className="text-sm">{children}</span>
    </a>
  );
};