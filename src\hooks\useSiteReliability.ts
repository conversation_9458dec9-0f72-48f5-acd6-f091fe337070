import { useState, useEffect, useCallback } from 'react';
import siteReliabilityService, {
  SiteReliabilityDashboardData,
  SLATarget,
  UptimeMetrics,
  PerformanceMetrics,
  AlertRule,
  ServiceIncident
} from '../services/siteReliabilityService';

interface UseSiteReliabilityState {
  dashboardData: SiteReliabilityDashboardData | null;
  slaTargets: SLATarget[];
  uptimeMetrics: UptimeMetrics[];
  performanceMetrics: PerformanceMetrics[];
  alertRules: AlertRule[];
  incidents: ServiceIncident[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseSiteReliabilityActions {
  refresh: () => Promise<void>;
  refreshSLATargets: () => Promise<void>;
  refreshUptimeMetrics: () => Promise<void>;
  refreshPerformanceMetrics: (timeRange?: '1h' | '24h' | '7d' | '30d') => Promise<void>;
  refreshAlertRules: () => Promise<void>;
  refreshIncidents: (status?: 'active' | 'resolved' | 'all') => Promise<void>;
  acknowledgeAlert: (alertId: string) => Promise<boolean>;
  resolveIncident: (incidentId: string, resolution: string) => Promise<boolean>;
  updateSLATarget: (targetId: string, updates: Partial<SLATarget>) => Promise<boolean>;
  toggleAlertRule: (ruleId: string, enabled: boolean) => Promise<boolean>;
  exportMetrics: (format?: 'csv' | 'pdf' | 'excel', timeRange?: '24h' | '7d' | '30d') => Promise<boolean>;
  generateReport: (type?: 'sla' | 'uptime' | 'performance' | 'incidents') => Promise<boolean>;
  performHealthCheck: () => Promise<{ healthy: boolean; issues: string[] }>;
}

export const useSiteReliability = (): UseSiteReliabilityState & UseSiteReliabilityActions => {
  const [state, setState] = useState<UseSiteReliabilityState>({
    dashboardData: null,
    slaTargets: [],
    uptimeMetrics: [],
    performanceMetrics: [],
    alertRules: [],
    incidents: [],
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, isLoading: false }));
  }, []);

  const refresh = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const dashboardData = await siteReliabilityService.getDashboardData();
      setState(prev => ({
        ...prev,
        dashboardData,
        slaTargets: dashboardData.slaTargets,
        uptimeMetrics: dashboardData.uptimeMetrics,
        performanceMetrics: dashboardData.performanceMetrics,
        alertRules: dashboardData.alertRules,
        incidents: dashboardData.recentIncidents,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      console.error('Error fetching site reliability data:', error);
      setError('Failed to load site reliability data. Please try again.');
    }
  }, [setLoading, setError]);

  const refreshSLATargets = useCallback(async () => {
    try {
      const slaTargets = await siteReliabilityService.getSLATargets();
      setState(prev => ({ ...prev, slaTargets, lastUpdated: new Date() }));
    } catch (error) {
      console.error('Error fetching SLA targets:', error);
      setError('Failed to load SLA targets.');
    }
  }, [setError]);

  const refreshUptimeMetrics = useCallback(async () => {
    try {
      const uptimeMetrics = await siteReliabilityService.getUptimeMetrics();
      setState(prev => ({ ...prev, uptimeMetrics, lastUpdated: new Date() }));
    } catch (error) {
      console.error('Error fetching uptime metrics:', error);
      setError('Failed to load uptime metrics.');
    }
  }, [setError]);

  const refreshPerformanceMetrics = useCallback(async (timeRange: '1h' | '24h' | '7d' | '30d' = '24h') => {
    try {
      const performanceMetrics = await siteReliabilityService.getPerformanceMetrics(timeRange);
      setState(prev => ({ ...prev, performanceMetrics, lastUpdated: new Date() }));
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      setError('Failed to load performance metrics.');
    }
  }, [setError]);

  const refreshAlertRules = useCallback(async () => {
    try {
      const alertRules = await siteReliabilityService.getAlertRules();
      setState(prev => ({ ...prev, alertRules, lastUpdated: new Date() }));
    } catch (error) {
      console.error('Error fetching alert rules:', error);
      setError('Failed to load alert rules.');
    }
  }, [setError]);

  const refreshIncidents = useCallback(async (status: 'active' | 'resolved' | 'all' = 'all') => {
    try {
      const incidents = await siteReliabilityService.getIncidents(status);
      setState(prev => ({ ...prev, incidents, lastUpdated: new Date() }));
    } catch (error) {
      console.error('Error fetching incidents:', error);
      setError('Failed to load incidents.');
    }
  }, [setError]);

  const acknowledgeAlert = useCallback(async (alertId: string) => {
    const success = await siteReliabilityService.acknowledgeAlert(alertId);
    if (success) {
      await refreshAlertRules();
    }
    return success;
  }, [refreshAlertRules]);

  const resolveIncident = useCallback(async (incidentId: string, resolution: string) => {
    const success = await siteReliabilityService.resolveIncident(incidentId, resolution);
    if (success) {
      await refreshIncidents();
    }
    return success;
  }, [refreshIncidents]);

  const updateSLATarget = useCallback(async (targetId: string, updates: Partial<SLATarget>) => {
    const success = await siteReliabilityService.updateSLATarget(targetId, updates);
    if (success) {
      await refreshSLATargets();
    }
    return success;
  }, [refreshSLATargets]);

  const toggleAlertRule = useCallback(async (ruleId: string, enabled: boolean) => {
    const success = await siteReliabilityService.toggleAlertRule(ruleId, enabled);
    if (success) {
      await refreshAlertRules();
    }
    return success;
  }, [refreshAlertRules]);

  const exportMetrics = useCallback(async (format: 'csv' | 'pdf' | 'excel' = 'csv', timeRange: '24h' | '7d' | '30d' = '24h') => {
    return await siteReliabilityService.exportMetrics(format, timeRange);
  }, []);

  const generateReport = useCallback(async (type: 'sla' | 'uptime' | 'performance' | 'incidents' = 'sla') => {
    return await siteReliabilityService.generateReport(type);
  }, []);

  const performHealthCheck = useCallback(async () => {
    return await siteReliabilityService.performHealthCheck();
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    refresh();
    
    const interval = setInterval(() => {
      if (!state.isLoading) {
        refresh();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [refresh, state.isLoading]);

  return {
    ...state,
    refresh,
    refreshSLATargets,
    refreshUptimeMetrics,
    refreshPerformanceMetrics,
    refreshAlertRules,
    refreshIncidents,
    acknowledgeAlert,
    resolveIncident,
    updateSLATarget,
    toggleAlertRule,
    exportMetrics,
    generateReport,
    performHealthCheck,
  };
};

// Specialized hooks for specific data
export const useSLATargets = () => {
  const [slaTargets, setSLATargets] = useState<SLATarget[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSLATargets = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const targets = await siteReliabilityService.getSLATargets();
      setSLATargets(targets);
    } catch (error) {
      console.error('Error fetching SLA targets:', error);
      setError('Failed to load SLA targets.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSLATargets();
  }, [fetchSLATargets]);

  return {
    slaTargets,
    isLoading,
    error,
    refresh: fetchSLATargets,
  };
};

export const useUptimeMetrics = () => {
  const [uptimeMetrics, setUptimeMetrics] = useState<UptimeMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUptimeMetrics = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const metrics = await siteReliabilityService.getUptimeMetrics();
      setUptimeMetrics(metrics);
    } catch (error) {
      console.error('Error fetching uptime metrics:', error);
      setError('Failed to load uptime metrics.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUptimeMetrics();
  }, [fetchUptimeMetrics]);

  return {
    uptimeMetrics,
    isLoading,
    error,
    refresh: fetchUptimeMetrics,
  };
};

export const usePerformanceMetrics = (timeRange: '1h' | '24h' | '7d' | '30d' = '24h') => {
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPerformanceMetrics = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const metrics = await siteReliabilityService.getPerformanceMetrics(timeRange);
      setPerformanceMetrics(metrics);
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      setError('Failed to load performance metrics.');
    } finally {
      setIsLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchPerformanceMetrics();
  }, [fetchPerformanceMetrics]);

  return {
    performanceMetrics,
    isLoading,
    error,
    refresh: fetchPerformanceMetrics,
  };
};
