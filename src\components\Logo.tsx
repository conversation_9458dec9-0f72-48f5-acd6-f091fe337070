import React from 'react';
import { useTheme } from '../context/ThemeContext';

const Logo: React.FC = () => {
  const { mode } = useTheme();
  const isDark = mode === 'dark';

  return (
    <svg viewBox="0 0 400 100" className="w-full h-auto">
      <text
        x="10"
        y="50"
        className="text-4xl font-bold"
        style={{
          fill: '#A6D933',
          fontFamily: 'Inter, system-ui, sans-serif',
          fontWeight: 700
        }}
      >
        PRAE
      </text>
      <text
        x="120"
        y="50"
        className="text-4xl font-bold"
        style={{
          fill: isDark ? '#F5F5F5' : '#111827',
          fontFamily: 'Inter, system-ui, sans-serif',
          fontWeight: 700
        }}
      >
        FERRE
      </text>
      <text
        x="50"
        y="80"
        className="text-sm tracking-wider"
        style={{
          fill: isDark ? '#D1D5DB' : '#4B5563',
          fontFamily: 'Inter, system-ui, sans-serif',
          fontWeight: 500,
          letterSpacing: '0.1em'
        }}
      >
        PRIVACY ENGINE
      </text>
    </svg>
  );
};

export default Logo;