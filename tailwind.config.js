/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    fontSize: {
      'xs': '0.75rem',    // 12px
      'sm': '0.875rem',   // 14px
      'base': '1rem',     // 16px
      'lg': '1.125rem',   // 18px
      'xl': '1.25rem',    // 20px
      '2xl': '1.5rem',    // 24px
      '3xl': '1.875rem',  // 30px
      '4xl': '2rem',      // 32px
      '5xl': '2.5rem',    // 40px
      '6xl': '3rem',      // 48px
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      bold: '700',
    },
    borderRadius: {
      'none': '0',
      'sm': '0.25rem',    // 4px
      'DEFAULT': '0.375rem', // 6px
      'md': '0.5rem',     // 8px
      'lg': '0.75rem',    // 12px
      'xl': '1rem',       // 16px
      'full': '9999px',
    },
    spacing: {
      '0': '0',
      '1': '0.25rem',     // 4px
      '2': '0.5rem',      // 8px
      '3': '0.75rem',     // 12px
      '4': '1rem',        // 16px
      '5': '1.25rem',     // 20px
      '6': '1.5rem',      // 24px
      '8': '2rem',        // 32px
      '10': '2.5rem',     // 40px
      '12': '3rem',       // 48px
      '16': '4rem',       // 64px
      '20': '5rem',       // 80px
      '24': '6rem',       // 96px
      '32': '8rem',       // 128px
      '40': '10rem',      // 160px
      '48': '12rem',      // 192px
      '56': '14rem',      // 224px
      '64': '16rem',      // 256px
    },
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--color-primary)',
          light: 'var(--color-primary-light)',
          dark: 'var(--color-primary-dark)',
          hover: 'var(--color-primary-hover)',
          active: 'var(--color-primary-active)',
          focus: 'var(--color-primary-focus)',
        },
        secondary: {
          DEFAULT: 'var(--color-secondary)',
          light: 'var(--color-secondary-light)',
          dark: 'var(--color-secondary-dark)',
          hover: 'var(--color-secondary-hover)',
          active: 'var(--color-secondary-active)',
        },
        accent: {
          blue: 'var(--color-accent-blue)',
          teal: 'var(--color-accent-teal)',
          purple: 'var(--color-accent-purple)',
        },
        background: 'var(--color-background)',
        surface: 'var(--color-surface)',
        card: 'var(--color-card-bg)',
        text: {
          DEFAULT: 'var(--color-text)',
          secondary: 'var(--color-text-secondary)',
          tertiary: 'var(--color-text-tertiary)',
        },
        border: {
          DEFAULT: 'var(--color-border)',
          light: 'var(--color-border-light)',
          focus: 'var(--color-border-focus)',
        },
        // Semantic colors
        error: {
          DEFAULT: 'var(--color-error)',
          light: 'var(--color-error-light)',
          dark: 'var(--color-error-dark)',
          bg: 'var(--color-error-bg)',
        },
        success: {
          DEFAULT: 'var(--color-success)',
          light: 'var(--color-success-light)',
          dark: 'var(--color-success-dark)',
          bg: 'var(--color-success-bg)',
        },
        warning: {
          DEFAULT: 'var(--color-warning)',
          light: 'var(--color-warning-light)',
          dark: 'var(--color-warning-dark)',
          bg: 'var(--color-warning-bg)',
        },
        info: {
          DEFAULT: 'var(--color-info)',
          light: 'var(--color-info-light)',
          dark: 'var(--color-info-dark)',
          bg: 'var(--color-info-bg)',
        },
        chart: {
          bg: 'var(--chart-bg)',
          grid: 'var(--chart-grid)',
          text: 'var(--chart-text)',
          textSecondary: 'var(--chart-text-secondary)',
        },
        dashboard: {
          green: 'var(--dashboard-green)',
          amber: 'var(--dashboard-amber)',
          red: 'var(--dashboard-red)',
          purple: 'var(--dashboard-purple)',
          buttonBg: 'var(--dashboard-button-bg)',
          buttonText: 'var(--dashboard-button-text)',
        },
      },
      boxShadow: {
        'sm': 'var(--shadow-sm)',
        'DEFAULT': 'var(--shadow)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
        'xl': 'var(--shadow-xl)',
        'inner': 'var(--shadow-inner)',
        'focus': 'var(--shadow-focus)',
        'none': 'none',
      },
      zIndex: {
        '-1': '-1',
      },
      transitionDuration: {
        '250': '250ms',
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      screens: {
        'xs': '480px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
    },
  },
  plugins: [],
  darkMode: 'class',
};
