import React from 'react';
import { AlertCircle, Clock, CheckCircle } from 'lucide-react';

interface SubPolicyInfoProps {
  subPolicy: {
    name: string;
    status: string;
    documentRef?: string;
    version?: string;
    owner?: string;
  };
  onStatusChange: (status: string) => void;
}

export const SubPolicyInfo: React.FC<SubPolicyInfoProps> = ({ subPolicy, onStatusChange }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-orange-500 bg-orange-50';
      case 'in_progress':
        return 'text-blue-500 bg-blue-50';
      case 'completed':
        return 'text-green-500 bg-green-50';
      default:
        return 'text-gray-500 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex justify-between items-start mb-6 pb-4 border-b">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">{subPolicy.name}</h2>
          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full mt-2 ${getStatusColor(subPolicy.status)}`}>
            {getStatusIcon(subPolicy.status)}
            <span className="text-sm font-medium capitalize">
              {subPolicy.status.replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">Document Details</h3>
              <p className="text-sm text-gray-600">Reference: DOC-{Math.random().toString(36).substr(2, 9).toUpperCase()}</p>
              <p className="text-sm text-gray-600">Version: 1.0.0</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Owner: John Smith</p>
              <p className="text-xs text-gray-500">Last updated: {new Date().toLocaleDateString()}</p>
            </div>
          </div>
          <p className="text-gray-600">
            Detailed information about {subPolicy.name} implementation and requirements.
          </p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Requirements</h3>
          <ul className="list-disc list-inside text-gray-600 space-y-2">
            <li>Requirement 1</li>
            <li>Requirement 2</li>
            <li>Requirement 3</li>
          </ul>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Implementation Timeline</h3>
          <p className="text-gray-600">
            Expected completion: Q4 2024
          </p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Status Update</h3>
          <div className="flex gap-3">
            <button
              onClick={() => onStatusChange('pending')}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                subPolicy.status === 'pending' 
                  ? 'bg-orange-50 border-orange-200 text-orange-700' 
                  : 'hover:bg-gray-50'
              }`}
            >
              Pending
            </button>
            <button
              onClick={() => onStatusChange('in_progress')}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                subPolicy.status === 'in_progress' 
                  ? 'bg-blue-50 border-blue-200 text-blue-700' 
                  : 'hover:bg-gray-50'
              }`}
            >
              In Progress
            </button>
            <button
              onClick={() => onStatusChange('completed')}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                subPolicy.status === 'completed' 
                  ? 'bg-green-50 border-green-200 text-green-700' 
                  : 'hover:bg-gray-50'
              }`}
            >
              Completed
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};