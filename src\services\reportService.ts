import axios from 'axios';

// In development mode with MSW, we can use relative URLs
// In production, we would use the actual API URL
const isDev = import.meta.env.MODE === 'development';

const api = axios.create({
  baseURL: isDev ? '/api' : 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

interface ReportData {
  name: string;
  description: string;
  type: string;
  format: string;
  userId?: string;
  policies?: string[];
  timestamp?: string;
}

export const generateReport = async (reportData: ReportData) => {
  const response = await api.post('/reports', reportData);
  return response.data;
};

export const deleteReport = async (reportId: string) => {
  await api.delete(`/reports/${reportId}`);
};

export const downloadReport = async (reportId: string) => {
  const response = await api.get(`/reports/${reportId}/download`, {
    responseType: 'blob'
  });
  return response.data;
};