import React, { useState } from 'react';
import { ArrowLeft, CheckCircle, Users, TrendingUp, Calendar, RefreshCw, Download, FileText, BarChart3 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { useGreenLevelData } from '../../hooks/usePrivacyDashboard';
import { FullPageSkeleton, ErrorState } from './LoadingSkeleton';
import { toast } from 'react-toastify';
import GreenLevelAnalytics from './GreenLevelAnalytics';
import GreenLevelReportModal from './GreenLevelReportModal';

const GreenLevelDetails: React.FC = () => {
  const navigate = useNavigate();
  const { mode } = useTheme();
  const { data, isLoading, error, refresh, exportData, generateReport } = useGreenLevelData();
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);

  const handleBackClick = () => {
    navigate('/');
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    setActionLoading('export');
    try {
      await exportData(format);
    } finally {
      setActionLoading(null);
    }
  };

  const handleGenerateReport = async () => {
    setShowReportModal(true);
  };

  const handleViewAnalytics = () => {
    setShowAnalytics(true);
  };

  const handleRefresh = async () => {
    setActionLoading('refresh');
    try {
      await refresh();
      toast.success('Data refreshed successfully!');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return <FullPageSkeleton />;
  }

  if (error || !data) {
    return (
      <ErrorState
        error={error || 'Failed to load green level data'}
        onRetry={refresh}
        onBack={handleBackClick}
      />
    );
  }

  return (
    <div className="flex-1 bg-background">
      <div className="p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackClick}
              className="flex items-center text-text-secondary hover:text-text transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Privacy Dashboard
            </button>
            <button
              onClick={handleRefresh}
              disabled={actionLoading === 'refresh'}
              className="flex items-center px-3 py-2 text-sm border border-border rounded-md hover:bg-surface transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${actionLoading === 'refresh' ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          <h1 className="text-sm text-text-secondary">Customer privacy management portal</h1>
          <h2 className="text-2xl font-bold text-text">Green Level Data Consent</h2>
          <p className="text-text-secondary mt-1">Last updated: {data.lastUpdated.toLocaleString()}</p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Overview Card */}
          <div className="lg:col-span-2 bg-card rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full border-4 flex items-center justify-center bg-surface mr-4"
                   style={{ borderColor: 'var(--dashboard-green)' }}>
                <CheckCircle className="w-8 h-8" style={{ color: 'var(--dashboard-green)' }} />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text">Compliant Data Consent</h3>
                <p className="text-text-secondary">{data.percentage.toFixed(1)}% of total data subjects</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-text mb-3">Overview</h4>
                <p className="text-text-secondary leading-relaxed">
                  Green Level represents data subjects who have provided full, informed consent for data processing activities.
                  These individuals have explicitly agreed to all data collection, processing, and sharing activities as outlined
                  in your privacy policies. This level indicates the highest compliance status with privacy regulations.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Detailed user breakdown coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Users className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-green)' }} />
                    <span className="font-medium text-text">Total Subjects</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.totalSubjects.toLocaleString()}</p>
                  <p className="text-sm text-text-secondary">Fully compliant users</p>
                </div>

                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Growth trend analysis coming soon!')}>
                  <div className="flex items-center mb-2">
                    <TrendingUp className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-green)' }} />
                    <span className="font-medium text-text">Growth Rate</span>
                  </div>
                  <p className="text-2xl font-bold text-text">+{data.growthRate.toFixed(1)}%</p>
                  <p className="text-sm text-text-secondary">From last month</p>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div className="space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Consent Breakdown</h4>
              <div className="space-y-4">
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Data collection details coming soon!')}>
                  <span className="text-text-secondary">Data Collection</span>
                  <span className="font-medium text-text">{data.breakdown.dataCollection}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Data processing details coming soon!')}>
                  <span className="text-text-secondary">Data Processing</span>
                  <span className="font-medium text-text">{data.breakdown.dataProcessing}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Data sharing details coming soon!')}>
                  <span className="text-text-secondary">Data Sharing</span>
                  <span className="font-medium text-text">{data.breakdown.dataSharing}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Marketing consent details coming soon!')}>
                  <span className="text-text-secondary">Marketing</span>
                  <span className="font-medium text-text">{data.breakdown.marketing}%</span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Recent Activity</h4>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 text-text-secondary" />
                  <span className="text-text-secondary">Last updated: Today</span>
                </div>
                <div className="text-sm text-text-secondary">
                  <p>• {data.recentActivity.newConsents} new consents today</p>
                  <p>• {data.recentActivity.renewals} consent renewals</p>
                  <p>• {data.recentActivity.modifications} consent modifications</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <div className="relative">
            <button
              onClick={() => handleExport('csv')}
              disabled={actionLoading === 'export'}
              className="flex items-center px-6 py-2 rounded transition-colors disabled:opacity-50"
              style={{
                backgroundColor: 'var(--dashboard-green)',
                color: 'white'
              }}
            >
              {actionLoading === 'export' ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Export Green Level Data
            </button>
          </div>
          <button
            onClick={handleGenerateReport}
            disabled={actionLoading === 'report'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'report' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FileText className="w-4 h-4 mr-2" />
            )}
            View Detailed Report
          </button>
          <button
            onClick={handleViewAnalytics}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            View Analytics
          </button>
        </div>
      </div>

      {/* Analytics Modal */}
      <GreenLevelAnalytics
        isOpen={showAnalytics}
        onClose={() => setShowAnalytics(false)}
        data={data}
      />

      {/* Report Modal */}
      <GreenLevelReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        data={data}
      />
    </div>
  );
};

export default GreenLevelDetails;
