import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { X, AlertCircle, Clock, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { SubPolicyPage } from './SubPolicyPage';
import { useNavigate } from 'react-router-dom';

interface SubPolicy {
  name: string;
  status: string;
}

interface PolicyWithSubPolicies {
  name: string;
  subPolicies?: SubPolicy[];
  status?: string;
}

interface PolicyListProps {
  type: string;
  policies: string[];
  onClose: () => void;
}

export const PolicyList: React.FC<PolicyListProps> = ({ type, policies, onClose }) => {
  const navigate = useNavigate();
  const [expandedPolicy, setExpandedPolicy] = useState<string | null>(null);
  const [policyStatuses, setPolicyStatuses] = useState<Record<string, string>>(
    Object.fromEntries(policies.map(policy => [policy, 'pending']))
  );

  const criticalPolicies: PolicyWithSubPolicies[] = [
    {
      name: 'Regulatory Compliance Policies',
      subPolicies: [
        { name: 'Policy development and documentation', status: 'pending' },
        { name: 'Roles/responsibilities assignment', status: 'in_progress' },
        { name: 'Audit preparedness frameworks', status: 'pending' },
        { name: 'Cross-departmental compliance alignment', status: 'completed' },
      ]
    },
    {
      name: 'Data Encryption',
      subPolicies: [
        { name: 'At-rest vs. in-transit encryption', status: 'pending' },
        { name: 'Key management protocols', status: 'in_progress' },
        { name: 'Cryptographic standards (e.g., AES-256)', status: 'pending' },
      ]
    },
    {
      name: 'Secure Storage',
      subPolicies: [
        { name: 'Physical security controls', status: 'pending' },
        { name: 'Access restriction mechanisms', status: 'in_progress' },
        { name: 'Redundancy and disaster recovery planning', status: 'pending' },
      ]
    },
    { name: 'Multi-Factor Authentication (MFA)', status: 'pending' },
    { name: 'Role-Based Access Control (RBAC)', status: 'in_progress' },
    { name: 'Privileged Access Management (PAM)', status: 'pending' },
    { name: 'Biometric Authentication', status: 'completed' },
    { name: 'Incident Response Plans', status: 'pending' },
    { name: 'Risk Management Frameworks', status: 'in_progress' },
    { name: 'Employee Security Awareness Training', status: 'pending' },
    { name: 'Continuous Monitoring', status: 'completed' },
    { name: 'Auditing and Logging', status: 'pending' },
    { name: 'Data Classification Policies', status: 'in_progress' },
    { name: 'Data Masking and Anonymization', status: 'pending' },
    { name: 'Secure Software Development Practices', status: 'completed' },
    { name: 'Patch Management', status: 'pending' },
    { name: 'Zero Trust Architecture (ZTA)', status: 'in_progress' },
  ];

  const [subPolicyStatuses, setSubPolicyStatuses] = useState(() => {
    const initialStatuses: Record<string, Record<string, string>> = {};
    criticalPolicies.forEach(policy => {
      if (policy.subPolicies) {
        initialStatuses[policy.name] = Object.fromEntries(
          policy.subPolicies.map(subPolicy => [subPolicy.name, subPolicy.status])
        );
      }
    });
    return initialStatuses;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-orange-500 bg-orange-50';
      case 'in_progress':
        return 'text-blue-500 bg-blue-50';
      case 'completed':
        return 'text-green-500 bg-green-50';
      default:
        return 'text-gray-500 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const handleStatusChange = (policyName: string, status: string, isSubPolicy?: boolean, parentPolicy?: string) => {
    if (isSubPolicy && parentPolicy) {
      setSubPolicyStatuses(prev => ({
        ...prev,
        [parentPolicy]: {
          ...prev[parentPolicy],
          [policyName]: status
        }
      }));
    } else {
      setPolicyStatuses(prev => ({
        ...prev,
        [policyName]: status
      }));
    }
  };

  const [selectedSubPolicy, setSelectedSubPolicy] = useState<SubPolicy | null>(null);
  const [selectedParentPolicy, setSelectedParentPolicy] = useState<string | null>(null);

  const renderPolicy = (policy: PolicyWithSubPolicies) => (
    <div key={policy.name} className="border rounded-lg overflow-hidden">
      <div className="flex items-center justify-between p-4 bg-white">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 text-lg">{policy.name}</h3>
          <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
            <Clock className="w-4 h-4" />
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </div>
        {!policy.subPolicies && (
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${getStatusColor(policyStatuses[policy.name] || 'pending')}`}>
              {getStatusIcon(policyStatuses[policy.name] || 'pending')}
              <span className="text-sm font-medium capitalize">
                {(policyStatuses[policy.name] || 'pending').replace('_', ' ')}
              </span>
            </div>
            <select
              className="rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              value={policyStatuses[policy.name] || 'pending'}
              onChange={(e) => handleStatusChange(policy.name, e.target.value)}
            >
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        )}
      </div>

      {policy.subPolicies && (
        <div className="bg-gray-50 p-4 space-y-3">
          {policy.subPolicies.map((subPolicy) => (
            <div
              key={subPolicy.name}
              className="flex items-center justify-between p-3 bg-white border rounded-lg cursor-pointer hover:bg-gray-50"
              onClick={() => {
                setSelectedSubPolicy(subPolicy);
                setSelectedParentPolicy(policy.name);
              }}
            >
              <span className="text-sm text-gray-700 flex-1 cursor-pointer">
                {subPolicy.name}
              </span>
              <div className="flex items-center gap-4">
                <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${getStatusColor(subPolicyStatuses[policy.name]?.[subPolicy.name] || 'pending')}`}>
                  {getStatusIcon(subPolicyStatuses[policy.name]?.[subPolicy.name] || 'pending')}
                  <span className="text-sm font-medium capitalize">
                    {(subPolicyStatuses[policy.name]?.[subPolicy.name] || 'pending').replace('_', ' ')}
                  </span>
                </div>
                <select
                  className="rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  value={subPolicyStatuses[policy.name]?.[subPolicy.name] || 'pending'}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleStatusChange(subPolicy.name, e.target.value, true, policy.name);
                  }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* // In the renderPolicy function, replace the SubPolicyDetails with: */}
      {selectedSubPolicy && selectedParentPolicy === policy.name && (
      <SubPolicyPage
        subPolicy={{
          ...selectedSubPolicy,
          status: subPolicyStatuses[policy.name]?.[selectedSubPolicy.name] || 'pending'
        }}
        parentPolicy={policy.name}
        onBack={() => {
          setSelectedSubPolicy(null);
          setSelectedParentPolicy(null);
        }}
        onStatusChange={(status) => {
          handleStatusChange(selectedSubPolicy.name, status, true, policy.name);
          setSelectedSubPolicy({ ...selectedSubPolicy, status });
        }}
      />
      )}
    </div>
  );

  return (
    <Dialog open={true} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-white rounded-xl shadow-xl p-6 max-w-4xl w-full max-h-[85vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-6 pb-4 border-b">
            <div>
              <Dialog.Title className="text-2xl font-semibold text-gray-900">
                {type} Policies
              </Dialog.Title>
              <p className="text-sm text-gray-500 mt-2 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {type === 'Critical' ? 
                  `${criticalPolicies.length} policies with ${
                    criticalPolicies.reduce((acc, policy) => acc + (policy.subPolicies?.length || 0), 0)
                  } sub-policies require attention` : 
                  `${policies.length} policies require attention`}
              </p>
            </div>
          </div>
          <div className="space-y-4">
            {criticalPolicies.map(renderPolicy)}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};