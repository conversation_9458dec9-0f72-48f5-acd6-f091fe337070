import type { AxiosResponse } from 'axios';
import axios from 'axios';

const BASE_URL = 'https://api.mockapi.io/v1';
const apiKey = import.meta.env.VITE_MOCK_API_KEY;

export const fetchComplianceData = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/compliance`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching compliance data:', error);
    return null;
  }
};