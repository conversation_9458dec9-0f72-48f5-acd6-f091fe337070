import React, { useEffect, useRef, useState } from 'react';
import { Chart } from 'chart.js/auto';
import { useCompliance } from '../context/ComplianceContext';
import { useTheme } from '../context/ThemeContext';
import { getChartColors, getChartTheme } from '../utils/chartOptimizations';

// Add loading skeleton
const PolicyDistributionChart: React.FC = () => {
  const { metrics, fetchData, isLoading } = useCompliance(); // Change from loading to isLoading
  const { mode } = useTheme();
  const isDark = mode === 'dark';
  const colors = getChartColors(isDark);
  const theme = getChartTheme(isDark);

  if (isLoading) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm border border-border animate-pulse">
        <div className="h-8 bg-surface rounded w-1/3 mb-6"></div>
        <div className="h-[350px] bg-surface rounded"></div>
      </div>
    );
  }
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);
  // Remove duplicate declaration since it's already declared at the top

  // Add state for monthly data
  const [monthlyData, setMonthlyData] = useState({
    high: [72, 73, 74, 73, 74, 73, 74, 73, 74, 73, 74, 73],
    medium: [18, 17, 16, 17, 16, 17, 16, 17, 16, 17, 16, 17],
    low: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10]
  });

  // Add effect for data updates
  useEffect(() => {
    const updateData = () => {
      setMonthlyData(prev => {
        const newData = {
          high: [...prev.high],
          medium: [...prev.medium],
          low: [...prev.low]
        };

        // April is index 3
        const aprilIndex = 3;
        const monthVariation = (Math.random() - 0.5) * 3;

        // Base values for April
        const baseHigh = 55 + (Math.random() - 0.5) * 10;
        const baseMedium = 20 + (Math.random() - 0.5) * 5;
        const baseLow = 25 + (Math.random() - 0.5) * 5;

        // Update only April values
        newData.high[aprilIndex] = Math.min(60, Math.max(50, baseHigh + monthVariation));
        newData.medium[aprilIndex] = Math.min(25, Math.max(15, baseMedium + monthVariation));
        newData.low[aprilIndex] = Math.min(30, Math.max(20, baseLow + monthVariation));

        // Normalize April to ensure total is 100%
        const total = newData.high[aprilIndex] + newData.medium[aprilIndex] + newData.low[aprilIndex];
        const factor = 100 / total;
        newData.high[aprilIndex] *= factor;
        newData.medium[aprilIndex] *= factor;
        newData.low[aprilIndex] *= factor;

        return newData;
      });
    };

    const interval = setInterval(updateData, 10000);
    return () => clearInterval(interval);
  }, []);

  // Update chart options for smoother animations
  useEffect(() => {
    if (!chartRef.current) return;

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    chartInstance.current = new Chart(chartRef.current, {
      type: 'bar',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [
          {
            label: 'High Compliance',
            data: monthlyData.high,
            backgroundColor: colors.compliant + 'CC', // Add transparency
            borderColor: colors.compliant,
            borderWidth: isDark ? 1 : 0,
            barPercentage: 0.6,
            categoryPercentage: 0.8,
            stack: 'stack0'
          },
          {
            label: 'Medium Compliance',
            data: monthlyData.medium,
            backgroundColor: colors.pending + 'CC', // Add transparency
            borderColor: colors.pending,
            borderWidth: isDark ? 1 : 0,
            barPercentage: 0.6,
            categoryPercentage: 0.8,
            stack: 'stack0'
          },
          {
            label: 'Low Compliance',
            data: monthlyData.low,
            backgroundColor: colors.nonCompliant + 'CC', // Add transparency
            borderColor: colors.nonCompliant,
            borderWidth: isDark ? 1 : 0,
            barPercentage: 0.6,
            categoryPercentage: 0.8,
            stack: 'stack0'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            align: 'start',
            labels: {
              usePointStyle: true,
              pointStyle: 'circle',
              padding: 20,
              boxWidth: 8,
              boxHeight: 8,
              color: '#64748b',
              font: {
                size: 12,
                family: "'Inter', sans-serif"
              }
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: theme.tooltipBg,
            titleColor: theme.textColor,
            bodyColor: theme.textSecondary,
            borderColor: theme.tooltipBorder,
            borderWidth: 1,
            padding: {
              x: 12,
              y: 8
            },
            bodyFont: {
              size: 12,
              family: "'Inter', sans-serif"
            },
            titleFont: {
              size: 13,
              family: "'Inter', sans-serif",
              weight: 600
            },
            cornerRadius: 8,
            displayColors: true,
            boxWidth: 8,
            boxHeight: 8,
            usePointStyle: true,
            callbacks: {
              label: (context) => ` ${context.dataset.label}: ${context.parsed.y}%`
            }
          }
        },
        scales: {
          y: {
            stacked: true,
            beginAtZero: true,
            max: 120,
            grid: {
              color: theme.gridColor,
              display: false,
              lineWidth: 1
            },
            ticks: {
              stepSize: 20,
              padding: 10,
              color: theme.textSecondary,
              font: {
                size: 12,
                family: "'Inter', sans-serif"
              }
            },
            border: {
              display: false
            }
          },
          x: {
            stacked: true,
            grid: {
              display: false // Remove duplicate display property and drawBorder
            },
            ticks: {
              padding: 10,
              color: theme.textSecondary,
              font: {
                size: 12,
                family: "'Inter', sans-serif"
              }
            },
            border: {
              display: false
            }
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [monthlyData, metrics, fetchData]); // Add monthlyData to dependencies

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-text">Compliance Distribution</h3>
        <p className="text-sm text-text-secondary mt-1">Monthly compliance level breakdown</p>
      </div>
      <div className="h-[350px] w-full">
        <canvas ref={chartRef} />
      </div>
    </div>
  );
};

export default PolicyDistributionChart;