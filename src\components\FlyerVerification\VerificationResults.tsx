import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, AlertTriangle, Search, Filter, Download, Eye } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface VerificationRecord {
  id: string;
  passengerName: string;
  documentType: 'passport' | 'visa';
  documentNumber: string;
  nationality: string;
  verificationStatus: 'approved' | 'denied' | 'pending';
  confidence: number;
  riskLevel: 'low' | 'medium' | 'high';
  timestamp: string;
  processingTime: number; // in seconds
  verifiedBy: string;
  errors?: string[];
}

const VerificationResults: React.FC = () => {
  const [records, setRecords] = useState<VerificationRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<VerificationRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [riskFilter, setRiskFilter] = useState<string>('all');
  const [selectedRecord, setSelectedRecord] = useState<VerificationRecord | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Streamlined mock data with exactly 6 realistic test cases
  useEffect(() => {
    const generateMockRecords = (): VerificationRecord[] => {
      const testCases: VerificationRecord[] = [
        // Approved Cases
        {
          id: 'VER-0001',
          passengerName: 'Rajesh Kumar Patel',
          documentType: 'passport',
          documentNumber: 'P12345678',
          nationality: 'Indian',
          verificationStatus: 'approved',
          confidence: 94.2,
          riskLevel: 'low',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          processingTime: 2.3,
          verifiedBy: 'System Auto-Verification',
          auditTrailId: 'AUD-0001'
        },
        {
          id: 'VER-0002',
          passengerName: 'James William Smith',
          documentType: 'passport',
          documentNumber: 'P87654321',
          nationality: 'British',
          verificationStatus: 'approved',
          confidence: 96.8,
          riskLevel: 'low',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
          processingTime: 1.8,
          verifiedBy: 'System Auto-Verification',
          auditTrailId: 'AUD-0002'
        },
        // Denied Cases
        {
          id: 'VER-0003',
          passengerName: 'Mohammad Ali Hosseini',
          documentType: 'passport',
          documentNumber: 'P11223344',
          nationality: 'Iranian',
          verificationStatus: 'denied',
          confidence: 89.1,
          riskLevel: 'critical',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
          processingTime: 3.2,
          verifiedBy: 'System Auto-Verification',
          errors: ['Executive Order 13769 - Iranian nationals prohibited from US entry', 'Nationality-based travel restriction in effect'],
          auditTrailId: 'AUD-0003'
        },
        {
          id: 'VER-0004',
          passengerName: 'Ahmed Hassan Al-Rashid',
          documentType: 'passport',
          documentNumber: 'P55667788',
          nationality: 'Syrian',
          verificationStatus: 'denied',
          confidence: 91.5,
          riskLevel: 'critical',
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
          processingTime: 2.9,
          verifiedBy: 'System Auto-Verification',
          errors: ['Presidential Proclamation 9645 - Syrian nationals banned from US travel', 'Security concerns - enhanced screening required'],
          auditTrailId: 'AUD-0004'
        },
        // Pending Case
        {
          id: 'VER-0005',
          passengerName: 'Ahmad Shah Ahmadi',
          documentType: 'passport',
          documentNumber: 'P99887766',
          nationality: 'Afghan',
          verificationStatus: 'pending',
          confidence: 78.3,
          riskLevel: 'medium',
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          processingTime: 4.1,
          verifiedBy: 'Manual Review Required',
          auditTrailId: 'AUD-0005'
        },
        // Visa Required Case
        {
          id: 'VER-0006',
          passengerName: 'Hans Mueller',
          documentType: 'passport',
          documentNumber: 'P44556677',
          nationality: 'German',
          verificationStatus: 'approved',
          confidence: 95.7,
          riskLevel: 'low',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          processingTime: 2.1,
          verifiedBy: 'System Auto-Verification',
          auditTrailId: 'AUD-0006'
        }
      ];

      return testCases;
    };

    setTimeout(() => {
      const mockRecords = generateMockRecords();
      setRecords(mockRecords);
      setFilteredRecords(mockRecords);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter records based on search and filters
  useEffect(() => {
    let filtered = records;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.passengerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.documentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.nationality.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(record => record.verificationStatus === statusFilter);
    }

    // Risk filter
    if (riskFilter !== 'all') {
      filtered = filtered.filter(record => record.riskLevel === riskFilter);
    }

    setFilteredRecords(filtered);
  }, [records, searchTerm, statusFilter, riskFilter]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-success" />;
      case 'denied':
        return <XCircle className="w-5 h-5 text-error" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-warning" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-text-secondary" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-success/10 text-success`;
      case 'denied':
        return `${baseClasses} bg-error/10 text-error`;
      case 'pending':
        return `${baseClasses} bg-warning/10 text-warning`;
      default:
        return `${baseClasses} bg-surface text-text-secondary`;
    }
  };

  const getRiskBadge = (risk: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (risk) {
      case 'low':
        return `${baseClasses} bg-success/10 text-success`;
      case 'medium':
        return `${baseClasses} bg-warning/10 text-warning`;
      case 'high':
        return `${baseClasses} bg-error/10 text-error`;
      default:
        return `${baseClasses} bg-surface text-text-secondary`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleExportResults = () => {
    toast.success('Verification results exported successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-text-secondary">Loading verification results...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card rounded-lg p-6 border border-border">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-text">Verification Results</h2>
            <p className="text-text-secondary">Real-time verification status and history</p>
          </div>
          <button
            onClick={handleExportResults}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search by name, document number, or nationality..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-surface text-text"
              />
            </div>
          </div>
          <div className="flex space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
            >
              <option value="all">All Status</option>
              <option value="approved">Approved</option>
              <option value="denied">Denied</option>
              <option value="pending">Pending</option>
            </select>
            <select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              className="px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
            >
              <option value="all">All Risk Levels</option>
              <option value="low">Low Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="high">High Risk</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Table */}
      <div className="bg-card rounded-lg border border-border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-surface">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Passenger
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Document
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Risk Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {filteredRecords.map((record) => (
                <tr key={record.id} className="hover:bg-surface">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-text">{record.passengerName}</div>
                      <div className="text-sm text-text-secondary">{record.nationality}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-text capitalize">{record.documentType}</div>
                      <div className="text-sm text-text-secondary font-mono">{record.documentNumber}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(record.verificationStatus)}
                      <span className={getStatusBadge(record.verificationStatus)}>
                        {record.verificationStatus.toUpperCase()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getRiskBadge(record.riskLevel)}>
                      {record.riskLevel.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-text">{record.confidence.toFixed(1)}%</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-text-secondary">{formatDate(record.timestamp)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => setSelectedRecord(record)}
                      className="text-primary hover:text-primary-hover flex items-center space-x-1"
                    >
                      <Eye className="w-4 h-4" />
                      <span>View</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredRecords.length === 0 && (
          <div className="text-center py-8">
            <p className="text-text-secondary">No verification records found matching your criteria.</p>
          </div>
        )}
      </div>

      {/* Record Detail Modal */}
      {selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text">Verification Details</h3>
              <button
                onClick={() => setSelectedRecord(null)}
                className="text-text-secondary hover:text-text"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-text-secondary">Verification ID</label>
                  <p className="text-text font-mono">{selectedRecord.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-text-secondary">Processing Time</label>
                  <p className="text-text">{selectedRecord.processingTime.toFixed(1)}s</p>
                </div>
              </div>
              
              {selectedRecord.errors && selectedRecord.errors.length > 0 && (
                <div className="bg-error/10 border border-error/20 rounded-lg p-4">
                  <h4 className="font-medium text-error mb-2">Verification Errors</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {selectedRecord.errors.map((error, index) => (
                      <li key={index} className="text-error text-sm">{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VerificationResults;
