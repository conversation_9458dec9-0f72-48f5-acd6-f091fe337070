import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Shield,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Search,
  Calendar,
  Activity,
  TrendingUp,
  TrendingDown,
  Target,
  Bell,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Layers,
  History,
  Users,
  Building,
  Globe,
  Database,
  Server,
  Lock,
  Unlock,
  Key,
  FileText,
  Zap,
  AlertCircle,
  MapPin,
  User,
  Wifi,
  Monitor
} from 'lucide-react';

// Data Protection Types
interface DataBreachAlert {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'investigating' | 'contained' | 'resolved';
  detectedAt: Date;
  source: string;
  affectedRecords: number;
  dataTypes: string[];
  location: string;
  investigator?: string;
  estimatedImpact: 'minimal' | 'moderate' | 'significant' | 'severe';
  responseActions: Array<{
    id: string;
    action: string;
    timestamp: Date;
    performer: string;
    status: 'pending' | 'completed';
  }>;
}

interface DataProtectionMetric {
  id: string;
  name: string;
  category: 'access_control' | 'data_encryption' | 'backup_recovery' | 'monitoring' | 'compliance';
  currentValue: number;
  targetValue: number;
  unit: string;
  healthScore: number; // 0-100
  status: 'healthy' | 'warning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    healthScore: number;
    incidents: number;
  }>;
}

interface SystemHealthIndicator {
  id: string;
  name: string;
  type: 'database' | 'application' | 'network' | 'storage' | 'backup';
  status: 'online' | 'degraded' | 'offline';
  healthScore: number;
  uptime: number; // percentage
  lastCheck: Date;
  responseTime: number; // milliseconds
  errorRate: number; // percentage
  throughput: number;
  alerts: number;
}

interface DataProtectionOverview {
  overallHealthScore: number;
  totalDataAssets: number;
  protectedAssets: number;
  vulnerableAssets: number;
  activeBreaches: number;
  resolvedBreaches: number;
  averageResponseTime: number; // minutes
  complianceScore: number;
  lastAssessment: Date;
  nextScheduledScan: Date;
  monitoredSystems: number;
  alertsLast24h: number;
}

interface DataProtectionData {
  overview: DataProtectionOverview;
  breachAlerts: DataBreachAlert[];
  metrics: DataProtectionMetric[];
  systemHealth: SystemHealthIndicator[];
  recentActivity: Array<{
    id: string;
    timestamp: Date;
    user: string;
    action: string;
    system: string;
    details: string;
    severity: 'info' | 'warning' | 'error';
  }>;
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    activeScans: number;
    pendingInvestigations: number;
  };
}

interface DataProtectionMonitoringProps {
  className?: string;
}

// Mock data generator
const generateDataProtectionData = (): DataProtectionData => {
  const breachAlerts: DataBreachAlert[] = [
    {
      id: 'breach-001',
      title: 'Unauthorized Database Access Attempt',
      description: 'Multiple failed login attempts detected on customer database',
      severity: 'high',
      status: 'investigating',
      detectedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      source: '*************',
      affectedRecords: 0,
      dataTypes: ['Customer Data', 'Personal Information'],
      location: 'Primary Database Server',
      investigator: 'Security Team',
      estimatedImpact: 'moderate',
      responseActions: [
        {
          id: 'action-001',
          action: 'IP address blocked',
          timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
          performer: 'Automated System',
          status: 'completed'
        },
        {
          id: 'action-002',
          action: 'Security team notified',
          timestamp: new Date(Date.now() - 1.8 * 60 * 60 * 1000),
          performer: 'Alert System',
          status: 'completed'
        }
      ]
    },
    {
      id: 'breach-002',
      title: 'Data Encryption Anomaly',
      description: 'Unusual encryption patterns detected in file storage system',
      severity: 'medium',
      status: 'contained',
      detectedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      source: 'File Storage System',
      affectedRecords: 1250,
      dataTypes: ['Document Files', 'Archived Data'],
      location: 'Secondary Storage',
      investigator: 'Data Protection Officer',
      estimatedImpact: 'minimal',
      responseActions: [
        {
          id: 'action-003',
          action: 'Encryption keys rotated',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
          performer: 'Security Admin',
          status: 'completed'
        }
      ]
    },
    {
      id: 'breach-003',
      title: 'Suspicious Data Export Activity',
      description: 'Large volume data export detected outside normal business hours',
      severity: 'critical',
      status: 'active',
      detectedAt: new Date(Date.now() - 30 * 60 * 1000),
      source: 'Internal User Account',
      affectedRecords: 5000,
      dataTypes: ['Customer Records', 'Financial Data', 'Personal Information'],
      location: 'Data Warehouse',
      estimatedImpact: 'significant',
      responseActions: [
        {
          id: 'action-004',
          action: 'User account suspended',
          timestamp: new Date(Date.now() - 25 * 60 * 1000),
          performer: 'Security System',
          status: 'completed'
        },
        {
          id: 'action-005',
          action: 'Incident response team activated',
          timestamp: new Date(Date.now() - 20 * 60 * 1000),
          performer: 'Alert System',
          status: 'pending'
        }
      ]
    }
  ];

  const metrics: DataProtectionMetric[] = [
    {
      id: 'metric-001',
      name: 'Data Encryption Coverage',
      category: 'data_encryption',
      currentValue: 94.7,
      targetValue: 98.0,
      unit: '%',
      healthScore: 87,
      status: 'warning',
      trend: 'improving',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 90 + Math.random() * 8,
        healthScore: 80 + Math.random() * 15,
        incidents: Math.floor(Math.random() * 5)
      }))
    },
    {
      id: 'metric-002',
      name: 'Access Control Compliance',
      category: 'access_control',
      currentValue: 97.2,
      targetValue: 95.0,
      unit: '%',
      healthScore: 95,
      status: 'healthy',
      trend: 'stable',
      lastUpdated: new Date(Date.now() - 8 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 94 + Math.random() * 5,
        healthScore: 90 + Math.random() * 10,
        incidents: Math.floor(Math.random() * 3)
      }))
    },
    {
      id: 'metric-003',
      name: 'Backup Recovery Success Rate',
      category: 'backup_recovery',
      currentValue: 89.1,
      targetValue: 99.0,
      unit: '%',
      healthScore: 72,
      status: 'critical',
      trend: 'declining',
      lastUpdated: new Date(Date.now() - 12 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 85 + Math.random() * 10,
        healthScore: 70 + Math.random() * 20,
        incidents: Math.floor(Math.random() * 8)
      }))
    }
  ];

  const systemHealth: SystemHealthIndicator[] = [
    {
      id: 'system-001',
      name: 'Primary Database',
      type: 'database',
      status: 'online',
      healthScore: 96,
      uptime: 99.8,
      lastCheck: new Date(Date.now() - 2 * 60 * 1000),
      responseTime: 45,
      errorRate: 0.2,
      throughput: 1250,
      alerts: 0
    },
    {
      id: 'system-002',
      name: 'Application Server',
      type: 'application',
      status: 'degraded',
      healthScore: 78,
      uptime: 97.2,
      lastCheck: new Date(Date.now() - 1 * 60 * 1000),
      responseTime: 180,
      errorRate: 2.8,
      throughput: 890,
      alerts: 3
    },
    {
      id: 'system-003',
      name: 'Backup Storage',
      type: 'backup',
      status: 'online',
      healthScore: 92,
      uptime: 99.5,
      lastCheck: new Date(Date.now() - 3 * 60 * 1000),
      responseTime: 120,
      errorRate: 0.5,
      throughput: 450,
      alerts: 1
    }
  ];

  const recentActivity = Array.from({ length: 15 }, (_, i) => ({
    id: `activity-${i + 1}`,
    timestamp: new Date(Date.now() - i * 30 * 60 * 1000),
    user: ['System Monitor', 'Security Admin', 'Data Officer', 'Backup Service'][Math.floor(Math.random() * 4)],
    action: ['Scan Completed', 'Alert Generated', 'Policy Updated', 'Backup Verified'][Math.floor(Math.random() * 4)],
    system: ['Database', 'File Storage', 'Network', 'Backup'][Math.floor(Math.random() * 4)],
    details: 'Automated security scan completed successfully with no issues detected',
    severity: ['info', 'warning', 'error'][Math.floor(Math.random() * 3)] as any
  }));

  return {
    overview: {
      overallHealthScore: 87.3,
      totalDataAssets: 15420,
      protectedAssets: 14650,
      vulnerableAssets: 770,
      activeBreaches: breachAlerts.filter(b => ['active', 'investigating'].includes(b.status)).length,
      resolvedBreaches: breachAlerts.filter(b => ['resolved', 'contained'].includes(b.status)).length,
      averageResponseTime: 45,
      complianceScore: 94.2,
      lastAssessment: new Date(Date.now() - 4 * 60 * 60 * 1000),
      nextScheduledScan: new Date(Date.now() + 2 * 60 * 60 * 1000),
      monitoredSystems: systemHealth.length,
      alertsLast24h: 12
    },
    breachAlerts,
    metrics,
    systemHealth,
    recentActivity,
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      activeScans: Math.floor(Math.random() * 3),
      pendingInvestigations: Math.floor(Math.random() * 5)
    }
  };
};

export const DataProtectionMonitoring: React.FC<DataProtectionMonitoringProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<DataProtectionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSeverity, setSelectedSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'investigating' | 'contained' | 'resolved'>('all');
  const [expandedAlert, setExpandedAlert] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'alerts' | 'metrics' | 'systems'>('overview');
  const [searchTerm, setSearchTerm] = useState('');

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1300));
        const data = generateDataProtectionData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load data protection monitoring data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          overview: {
            ...prevData.overview,
            overallHealthScore: Math.max(70, Math.min(100, prevData.overview.overallHealthScore + (Math.random() - 0.5) * 2)),
            alertsLast24h: Math.max(0, prevData.overview.alertsLast24h + Math.floor(Math.random() * 3) - 1)
          },
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            activeScans: Math.floor(Math.random() * 3),
            pendingInvestigations: Math.floor(Math.random() * 5)
          }
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter breach alerts based on criteria
  const filteredAlerts = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.breachAlerts.filter(alert => {
      const matchesSeverity = selectedSeverity === 'all' || alert.severity === selectedSeverity;
      const matchesStatus = selectedStatus === 'all' || alert.status === selectedStatus;
      const matchesSearch = searchTerm === '' ||
        alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.source.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSeverity && matchesStatus && matchesSearch;
    });
  }, [dashboardData, selectedSeverity, selectedStatus, searchTerm]);

  // Generate health score distribution chart
  const healthScoreData = useMemo(() => {
    if (!dashboardData) return null;

    const healthyCount = dashboardData.metrics.filter(m => m.status === 'healthy').length;
    const warningCount = dashboardData.metrics.filter(m => m.status === 'warning').length;
    const criticalCount = dashboardData.metrics.filter(m => m.status === 'critical').length;

    return {
      labels: ['Healthy', 'Warning', 'Critical'],
      datasets: [{
        data: [healthyCount, warningCount, criticalCount],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(248, 113, 113, 0.8)'
        ],
        borderColor: [
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
        hoverOffset: 8
      }]
    };
  }, [dashboardData]);

  // Generate metrics performance chart
  const metricsPerformanceData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.metrics.map(m => m.name),
      datasets: [{
        label: 'Current Value',
        data: dashboardData.metrics.map(m => m.currentValue),
        backgroundColor: dashboardData.metrics.map(m =>
          m.status === 'healthy' ? 'rgba(52, 211, 153, 0.8)' :
          m.status === 'warning' ? 'rgba(251, 191, 36, 0.8)' :
          'rgba(248, 113, 113, 0.8)'
        ),
        borderColor: dashboardData.metrics.map(m =>
          m.status === 'healthy' ? 'rgb(52, 211, 153)' :
          m.status === 'warning' ? 'rgb(251, 191, 36)' :
          'rgb(248, 113, 113)'
        ),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }, {
        label: 'Target Value',
        data: dashboardData.metrics.map(m => m.targetValue),
        backgroundColor: 'rgba(156, 163, 175, 0.3)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'medium':
        return <AlertCircle className="w-5 h-5 text-blue-500" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Shield className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSeverityColorClass = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'resolved':
      case 'contained':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'investigating':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'active':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-amber-500" />;
      case 'offline':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateDataProtectionData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAlertClick = useCallback((alertId: string) => {
    setExpandedAlert(expandedAlert === alertId ? null : alertId);
  }, [expandedAlert]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-80" />
            <LoadingSkeleton className="h-80" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Data Protection Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Data Protection Monitoring</h1>
              <p className="text-text-secondary">
                Live breach detection, automated health scoring, and real-time data protection monitoring
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live Monitoring' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • {dashboardData.realTimeUpdates.activeScans} scans active
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh monitoring data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Security Report
            </button>
          </div>
        </div>

        {/* Overview Statistics with Animated Health Scores */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Overall Health Score</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.overallHealthScore.toFixed(1)}</p>
                </div>
                <div className={`p-2 rounded-lg ${
                  dashboardData.overview.overallHealthScore >= 90 ? 'bg-green-100 dark:bg-green-900/20' :
                  dashboardData.overview.overallHealthScore >= 70 ? 'bg-amber-100 dark:bg-amber-900/20' :
                  'bg-red-100 dark:bg-red-900/20'
                }`}>
                  <Shield className={`w-6 h-6 ${
                    dashboardData.overview.overallHealthScore >= 90 ? 'text-green-500' :
                    dashboardData.overview.overallHealthScore >= 70 ? 'text-amber-500' :
                    'text-red-500'
                  }`} />
                </div>
              </div>
              <div className="mt-2">
                <div className="w-full bg-border rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-500 ${
                      dashboardData.overview.overallHealthScore >= 90 ? 'bg-green-500' :
                      dashboardData.overview.overallHealthScore >= 70 ? 'bg-amber-500' :
                      'bg-red-500'
                    }`}
                    style={{ width: `${dashboardData.overview.overallHealthScore}%` }}
                  />
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Active Breaches</p>
                  <p className="text-2xl font-bold text-red-500">{dashboardData.overview.activeBreaches}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Protected Assets</p>
                  <p className="text-2xl font-bold text-green-500">{dashboardData.overview.protectedAssets.toLocaleString()}</p>
                </div>
                <Database className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Avg Response Time</p>
                  <p className="text-2xl font-bold text-blue-500">{dashboardData.overview.averageResponseTime}m</p>
                </div>
                <Clock className="w-8 h-8 text-blue-500" />
              </div>
            </div>
          </div>
        )}

        {/* View Tabs */}
        <div className="flex items-center gap-2 mb-6">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'overview'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('alerts')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'alerts'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Bell className="w-4 h-4 inline mr-2" />
            Breach Alerts
          </button>
          <button
            onClick={() => setActiveView('metrics')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'metrics'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Target className="w-4 h-4 inline mr-2" />
            Metrics
          </button>
          <button
            onClick={() => setActiveView('systems')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'systems'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Monitor className="w-4 h-4 inline mr-2" />
            System Health
          </button>
        </div>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && (
        <>
          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Health Score Distribution */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                System Health Distribution
              </h3>
              {healthScoreData && (
                <div className="h-64 flex items-center justify-center">
                  <Doughnut
                    data={healthScoreData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>

            {/* Metrics Performance */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Protection Metrics Performance
              </h3>
              {metricsPerformanceData && (
                <div className="h-64">
                  <Bar
                    data={metricsPerformanceData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      },
                      scales: {
                        x: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                          }
                        },
                        y: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                            callback: (value) => `${value}%`
                          },
                          min: 0,
                          max: 100
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-surface rounded-lg p-6">
            <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Protection Activity
            </h3>

            {dashboardData && (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {dashboardData.recentActivity.slice(0, 8).map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                    <div className="flex items-center gap-3">
                      <div className={`p-1 rounded ${
                        activity.severity === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                        activity.severity === 'warning' ? 'bg-amber-100 dark:bg-amber-900/20' :
                        'bg-blue-100 dark:bg-blue-900/20'
                      }`}>
                        <Activity className={`w-3 h-3 ${
                          activity.severity === 'error' ? 'text-red-500' :
                          activity.severity === 'warning' ? 'text-amber-500' :
                          'text-blue-500'
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium text-text">{activity.action}</p>
                        <p className="text-sm text-text-secondary">
                          {activity.user} • {activity.system} • {activity.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}

      {/* Alerts View */}
      {activeView === 'alerts' && (
        <div className="bg-surface rounded-lg p-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search breach alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              />
            </div>

            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="investigating">Investigating</option>
              <option value="contained">Contained</option>
              <option value="resolved">Resolved</option>
            </select>
          </div>

          {/* Breach Alert Cards */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text">Data Breach Alerts ({filteredAlerts.length})</h3>

            {filteredAlerts.length === 0 ? (
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">No breach alerts found matching your criteria.</p>
              </div>
            ) : (
              filteredAlerts.map((alert) => (
                <div key={alert.id} className="bg-card rounded-lg border border-border overflow-hidden">
                  {/* Alert Header */}
                  <div
                    className="p-4 cursor-pointer hover:bg-border/30 transition-colors"
                    onClick={() => handleAlertClick(alert.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getSeverityIcon(alert.severity)}
                        <div>
                          <h4 className="text-lg font-semibold text-text">{alert.title}</h4>
                          <p className="text-text-secondary">{alert.description}</p>
                          <div className="flex items-center gap-4 text-sm text-text-secondary mt-2">
                            <span>Source: {alert.source}</span>
                            <span>Records: {alert.affectedRecords.toLocaleString()}</span>
                            <span>Location: {alert.location}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="text-sm text-text-secondary">Detected</p>
                          <p className="text-sm font-medium text-text">{alert.detectedAt.toLocaleString()}</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(alert.status)}`}>
                            {alert.status}
                          </span>

                          {expandedAlert === alert.id ? (
                            <ChevronDown className="w-5 h-5 text-text-secondary" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-text-secondary" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Expanded Alert Details */}
                  {expandedAlert === alert.id && (
                    <div className="border-t border-border p-4 bg-background/50">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Alert Details */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Alert Details</h5>
                          <div className="space-y-3">
                            <div className="p-3 bg-card rounded border border-border">
                              <p className="text-sm text-text-secondary">Affected Data Types</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {alert.dataTypes.map((type, index) => (
                                  <span key={index} className="px-2 py-1 bg-primary/10 text-primary rounded text-xs">
                                    {type}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div className="p-3 bg-card rounded border border-border">
                              <p className="text-sm text-text-secondary">Estimated Impact</p>
                              <p className="text-text font-medium capitalize">{alert.estimatedImpact}</p>
                            </div>
                            {alert.investigator && (
                              <div className="p-3 bg-card rounded border border-border">
                                <p className="text-sm text-text-secondary">Assigned Investigator</p>
                                <p className="text-text font-medium">{alert.investigator}</p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Response Actions */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Response Actions</h5>
                          <div className="space-y-3">
                            {alert.responseActions.map((action) => (
                              <div key={action.id} className="p-3 bg-card rounded border border-border">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <p className="font-medium text-text">{action.action}</p>
                                    <p className="text-sm text-text-secondary">
                                      By: {action.performer} • {action.timestamp.toLocaleString()}
                                    </p>
                                  </div>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    action.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                    'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
                                  }`}>
                                    {action.status}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Metrics View */}
      {activeView === 'metrics' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Target className="w-5 h-5" />
            Data Protection Metrics
          </h3>

          {dashboardData && (
            <div className="space-y-4">
              {dashboardData.metrics.map((metric) => (
                <div key={metric.id} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="text-lg font-semibold text-text">{metric.name}</h4>
                      <p className="text-sm text-text-secondary capitalize">{metric.category.replace('_', ' ')}</p>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-text">{metric.currentValue.toFixed(1)}{metric.unit}</p>
                        <p className="text-xs text-text-secondary">Current</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg text-text-secondary">{metric.targetValue.toFixed(1)}{metric.unit}</p>
                        <p className="text-xs text-text-secondary">Target</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-primary">{metric.healthScore}</p>
                        <p className="text-xs text-text-secondary">Health Score</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-text-secondary">Progress to Target</span>
                      <span className="text-text font-medium">
                        {Math.min((metric.currentValue / metric.targetValue) * 100, 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          metric.status === 'healthy' ? 'bg-green-500' :
                          metric.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'
                        }`}
                        style={{
                          width: `${Math.min((metric.currentValue / metric.targetValue) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* System Health View */}
      {activeView === 'systems' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            System Health Monitoring
          </h3>

          {dashboardData && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dashboardData.systemHealth.map((system) => (
                <div key={system.id} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getHealthStatusIcon(system.status)}
                      <h4 className="text-lg font-semibold text-text">{system.name}</h4>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      system.status === 'online' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      system.status === 'degraded' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400' :
                      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {system.status}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-text-secondary">Health Score</span>
                        <span className="text-text font-medium">{system.healthScore}/100</span>
                      </div>
                      <div className="w-full bg-border rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            system.healthScore >= 90 ? 'bg-green-500' :
                            system.healthScore >= 70 ? 'bg-amber-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${system.healthScore}%` }}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-text-secondary">Uptime</p>
                        <p className="text-text font-medium">{system.uptime.toFixed(1)}%</p>
                      </div>
                      <div>
                        <p className="text-text-secondary">Response</p>
                        <p className="text-text font-medium">{system.responseTime}ms</p>
                      </div>
                      <div>
                        <p className="text-text-secondary">Error Rate</p>
                        <p className="text-text font-medium">{system.errorRate.toFixed(1)}%</p>
                      </div>
                      <div>
                        <p className="text-text-secondary">Alerts</p>
                        <p className="text-text font-medium">{system.alerts}</p>
                      </div>
                    </div>

                    <div className="text-xs text-text-secondary">
                      Last Check: {system.lastCheck.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
