import React, { useState } from 'react';
import { ArrowLeft, AlertCircle, Users, TrendingDown, Calendar, Shield, AlertTriangle, RefreshCw, Download, FileText, Phone, AlertOctagon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { useRedLevelData } from '../../hooks/usePrivacyDashboard';
import { FullPageSkeleton, ErrorState } from './LoadingSkeleton';
import { toast } from 'react-toastify';

const RedLevelDetails: React.FC = () => {
  const navigate = useNavigate();
  const { mode } = useTheme();
  const { data, isLoading, error, refresh, exportData, generateReport, addressCriticalIssues, contactLegalTeam } = useRedLevelData();
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleBackClick = () => {
    navigate('/');
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel' = 'csv') => {
    setActionLoading('export');
    try {
      await exportData(format);
    } finally {
      setActionLoading(null);
    }
  };

  const handleGenerateReport = async () => {
    setActionLoading('report');
    try {
      await generateReport();
    } finally {
      setActionLoading(null);
    }
  };

  const handleAddressCritical = async () => {
    setActionLoading('critical');
    try {
      await addressCriticalIssues!();
    } finally {
      setActionLoading(null);
    }
  };

  const handleContactLegal = async () => {
    setActionLoading('legal');
    try {
      await contactLegalTeam!();
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefresh = async () => {
    setActionLoading('refresh');
    try {
      await refresh();
      toast.success('Data refreshed successfully!');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return <FullPageSkeleton />;
  }

  if (error || !data) {
    return (
      <ErrorState
        error={error || 'Failed to load red level data'}
        onRetry={refresh}
        onBack={handleBackClick}
      />
    );
  }

  return (
    <div className="flex-1 bg-background">
      <div className="p-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackClick}
              className="flex items-center text-text-secondary hover:text-text transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Privacy Dashboard
            </button>
            <button
              onClick={handleRefresh}
              disabled={actionLoading === 'refresh'}
              className="flex items-center px-3 py-2 text-sm border border-border rounded-md hover:bg-surface transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${actionLoading === 'refresh' ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          <h1 className="text-sm text-text-secondary">Customer privacy management portal</h1>
          <h2 className="text-2xl font-bold text-text">Red Level Data Consent</h2>
          <p className="text-text-secondary mt-1">Last updated: {data.lastUpdated.toLocaleString()}</p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Overview Card */}
          <div className="lg:col-span-2 bg-card rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full border-4 flex items-center justify-center bg-surface mr-4"
                   style={{ borderColor: 'var(--dashboard-red)' }}>
                <AlertCircle className="w-8 h-8" style={{ color: 'var(--dashboard-red)' }} />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text">Non-Compliant Data Consent</h3>
                <p className="text-text-secondary">{data.percentage.toFixed(1)}% of total data subjects</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-text mb-3">Overview</h4>
                <p className="text-text-secondary leading-relaxed">
                  Red Level represents data subjects with non-compliant consent status. This includes individuals
                  who have withdrawn consent, have expired consent without renewal, or have incomplete consent
                  documentation. Immediate action is required to address these compliance issues.
                </p>
              </div>

              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-red)' }} />
                  <h5 className="font-medium text-text">Critical Compliance Issues</h5>
                </div>
                <ul className="text-sm text-text-secondary space-y-1">
                  <li>• {Math.floor(data.totalSubjects * data.violationTypes.withdrawnConsent / 100)} consent withdrawals requiring data deletion</li>
                  <li>• {Math.floor(data.totalSubjects * data.violationTypes.expiredConsent / 100)} expired consents with continued processing</li>
                  <li>• {Math.floor(data.totalSubjects * data.violationTypes.incompleteRecords / 100)} incomplete consent records</li>
                  <li>• {Math.floor(data.totalSubjects * data.violationTypes.violations / 100)} consent violations reported</li>
                </ul>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Non-compliant user details coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Users className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-red)' }} />
                    <span className="font-medium text-text">Total Subjects</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.totalSubjects.toLocaleString()}</p>
                  <p className="text-sm text-text-secondary">Non-compliant users</p>
                </div>

                <div className="bg-surface rounded-lg p-4 hover:bg-surface/80 transition-colors cursor-pointer"
                     onClick={() => toast.info('Risk assessment details coming soon!')}>
                  <div className="flex items-center mb-2">
                    <Shield className="w-5 h-5 mr-2" style={{ color: 'var(--dashboard-red)' }} />
                    <span className="font-medium text-text">Risk Level</span>
                  </div>
                  <p className="text-2xl font-bold text-text">{data.riskLevel}</p>
                  <p className="text-sm text-text-secondary">Immediate action needed</p>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div className="space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Violation Types</h4>
              <div className="space-y-4">
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Withdrawn consent details coming soon!')}>
                  <span className="text-text-secondary">Withdrawn Consent</span>
                  <span className="font-medium text-text">{data.violationTypes.withdrawnConsent}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Expired consent details coming soon!')}>
                  <span className="text-text-secondary">Expired Consent</span>
                  <span className="font-medium text-text">{data.violationTypes.expiredConsent}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Incomplete records details coming soon!')}>
                  <span className="text-text-secondary">Incomplete Records</span>
                  <span className="font-medium text-text">{data.violationTypes.incompleteRecords}%</span>
                </div>
                <div className="flex justify-between items-center hover:bg-surface/50 p-2 rounded transition-colors cursor-pointer"
                     onClick={() => toast.info('Violation details coming soon!')}>
                  <span className="text-text-secondary">Violations</span>
                  <span className="font-medium text-text">{data.violationTypes.violations}%</span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Urgent Actions</h4>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 text-text-secondary" />
                  <span className="text-text-secondary">Deadline: Within 72 hours</span>
                </div>
                <div className="text-sm text-text-secondary">
                  <p>• {data.urgentActions.dataDeletionRequests} data deletion requests</p>
                  <p>• {data.urgentActions.processingSuspensions} processing suspensions</p>
                  <p>• {data.urgentActions.complianceNotifications} compliance notifications</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h4 className="text-lg font-medium text-text mb-4">Compliance Metrics</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Resolution Time</span>
                  <span className="font-medium text-text">{data.complianceMetrics.resolutionTime}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Success Rate</span>
                  <span className="font-medium text-text">{data.complianceMetrics.successRate.toFixed(0)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-text-secondary">Escalations</span>
                  <span className="font-medium text-text">{data.complianceMetrics.activeEscalations} active</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            onClick={handleAddressCritical}
            disabled={actionLoading === 'critical'}
            className="flex items-center px-6 py-2 rounded transition-colors disabled:opacity-50"
            style={{
              backgroundColor: 'var(--dashboard-red)',
              color: 'white'
            }}
          >
            {actionLoading === 'critical' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <AlertOctagon className="w-4 h-4 mr-2" />
            )}
            Address Critical Issues
          </button>
          <button
            onClick={handleGenerateReport}
            disabled={actionLoading === 'report'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'report' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FileText className="w-4 h-4 mr-2" />
            )}
            Generate Compliance Report
          </button>
          <button
            onClick={handleContactLegal}
            disabled={actionLoading === 'legal'}
            className="flex items-center px-6 py-2 border border-border rounded transition-colors hover:bg-surface text-text disabled:opacity-50"
          >
            {actionLoading === 'legal' ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Phone className="w-4 h-4 mr-2" />
            )}
            Contact Legal Team
          </button>
        </div>
      </div>
    </div>
  );
};

export default RedLevelDetails;
