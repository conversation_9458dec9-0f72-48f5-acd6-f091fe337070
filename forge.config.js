module.exports = {
  packagerConfig: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    executableName: 'praeferre',
    asar: true
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: '<PERSON><PERSON><PERSON><PERSON>'
      }
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['win32']
    }
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-vite',
      config: {
        build: [
          {
            entry: 'electron.js',
            config: 'vite.config.ts'
          }
        ],
        renderer: [
          {
            name: 'main_window',
            entry: 'src/main.tsx',
            config: 'vite.config.ts'
          }
        ]
      }
    }
  ]
};