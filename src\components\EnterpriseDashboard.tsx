import React from 'react';

const ConsentMetric: React.FC<{
  percentage: number;
  color: string;
  label: string;
  sublabel: string;
  size?: 'normal' | 'large';
}> = ({ percentage, color, label, sublabel, size = 'normal' }) => {
  const sizeClasses = size === 'large' ? 'w-40 h-40' : 'w-28 h-28';
  const radius = size === 'large' ? 65 : 40;
  const strokeWidth = size === 'large' ? 12 : 8;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="flex flex-col items-center">
      <div className={`${sizeClasses} relative`}>
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 150 150"
        >
          {/* Background circle */}
          <circle
            cx="75"
            cy="75"
            r={radius}
            fill="transparent"
            stroke="var(--color-border)"
            strokeWidth={strokeWidth}
          />
          {/* Progress circle */}
          <circle
            cx="75"
            cy="75"
            r={radius}
            fill="transparent"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            style={{
              strokeDasharray: circumference,
              strokeDashoffset: strokeDashoffset,
              transition: 'stroke-dashoffset 1s ease-in-out',
            }}
          />
        </svg>
        {/* Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <span className="text-2xl font-bold text-text">{percentage}%</span>
          </div>
        </div>
      </div>
      <div className="mt-3 text-center">
        <p className="text-sm font-medium text-text">{label}</p>
        <p className="text-xs text-text-secondary">{sublabel}</p>
      </div>
    </div>
  );
};

const EnterpriseDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-text">Policy Dashboard</h1>
      </div>

      <div className="bg-card p-8 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold mb-8 text-text">Data Consent Overview</h3>

        <div className="flex justify-center space-x-8 mb-12">
          <ConsentMetric
            percentage={31}
            color="#4ade80"
            label="Green Level"
            sublabel="Data Consent"
          />
          <ConsentMetric
            percentage={58}
            color="#fbbf24"
            label="Amber Level"
            sublabel="Data Consent"
          />
          <ConsentMetric
            percentage={11}
            color="#f87171"
            label="Red Level"
            sublabel="Data Consent"
          />
        </div>

        <div className="flex flex-col items-center mb-8">
          <ConsentMetric
            percentage={100}
            color="#1A1F2E"
            label="Data Privacy"
            sublabel="Acknowledgement"
            size="large"
          />
        </div>

        <div className="flex justify-center">
          <button className="px-4 py-2 bg-gradient-to-r from-secondary to-primary hover:from-secondary-light hover:to-primary-light text-white rounded transition-all duration-200 shadow-md hover:shadow-lg">
            Download Report
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Total Enterprises</h3>
          <p className="text-3xl font-bold text-blue-500">150</p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Active Users</h3>
          <p className="text-3xl font-bold text-green-500">1,234</p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Total Revenue</h3>
          <p className="text-3xl font-bold text-purple-500">$50,000</p>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-text mb-4">Enterprise List</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-surface">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Users</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Last Active</th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-text">Enterprise A</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700/50">Active</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">250</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">2 hours ago</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-text">Enterprise B</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700/50">Active</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">180</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">5 hours ago</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseDashboard;